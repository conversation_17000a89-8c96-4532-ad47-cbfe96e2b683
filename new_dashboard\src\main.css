@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

* {
   box-sizing: border-box;
   font-family: var(--font-nunitoSans);
   font-optical-sizing: auto;
   font-style: normal;
   font-variation-settings:
      'wdth' 100,
      'YTLC' 500;
}

::-webkit-scrollbar {
   display: none;
}

/* Custom scrollbar for table containers */
.scrollable::-webkit-scrollbar {
   display: block;
   width: 8px;
   height: 8px;
}

.scrollable::-webkit-scrollbar-track {
   background: var(--porcelain);
   border-radius: 4px;
}

.scrollable::-webkit-scrollbar-thumb {
   background: var(--navy);
   border-radius: 4px;
   transition: background 0.2s ease;
}

.scrollable::-webkit-scrollbar-thumb:hover {
   background: var(--ash);
}

.scrollable::-webkit-scrollbar-corner {
   background: var(--porcelain);
}

:root {
   --black: oklch(0 0 0);
   --white: oklch(1 0 0);

   --jet: #111111;
   --midnight: #182f5a;
   --charcoal: #333333;
   --smoky: #343330;
   --stone: #717680;
   --steel: #5e6c84;
   --lavenderGray: #8f9bba;
   --slate: #393e4a;
   --porcelain: #f9f9fa;
   --ash: #c2c2c2;
   --fog: #e7e9eb;
   --cloud: #e9eaeb;
   --silver: #d5d7da;

   /* Blues */
   --skyLight: #e4f2ff;
   --navy: #2563eb;
   --indigo: #1852bd;
   --azure: #175cd3;
   --cerulean: #3c76e1;
   --royal: #437eeb;

   /* Accents */
   --lime: #609e04;
   --amber: #fbbc04;
   --orange: #ffab00;
   --crimson: #b00020;
   --vermilion: #ff5630;
   --emerald: #10b981;
   --golden: #f59e0b;
   --ruby: #ef4444;

   --radius: 0.875rem;

   /* --radius: 0.875rem;
   --card: oklch(1 0 0);
   --card-foreground: oklch(0.145 0 0);
   --popover: oklch(1 0 0);
   --popover-foreground: oklch(0.145 0 0);
   --primary: oklch(0.205 0 0);
   --primary-foreground: oklch(0.985 0 0);
   --secondary: oklch(0.97 0 0);
   --secondary-foreground: oklch(0.205 0 0);
   --muted: oklch(0.97 0 0);
   --muted-foreground: oklch(0.556 0 0);
   --accent: oklch(0.97 0 0);
   --accent-foreground: oklch(0.205 0 0);
   --destructive: oklch(0.577 0.245 27.325);
   --border: oklch(0.922 0 0);
   --input: oklch(0.922 0 0);
   --ring: oklch(0.708 0 0);
   --chart-1: oklch(0.646 0.222 41.116);
   --chart-2: oklch(0.6 0.118 184.704);
   --chart-3: oklch(0.398 0.07 227.392);
   --chart-4: oklch(0.828 0.189 84.429);
   --chart-5: oklch(0.769 0.188 70.08);
   --sidebar: oklch(0.985 0 0);
   --sidebar-foreground: oklch(0.145 0 0);
   --sidebar-primary: oklch(0.205 0 0);
   --sidebar-primary-foreground: oklch(0.985 0 0);
   --sidebar-accent: oklch(0.97 0 0);
   --sidebar-accent-foreground: oklch(0.205 0 0);
   --sidebar-border: oklch(0.922 0 0);
   --sidebar-ring: oklch(0.708 0 0); */
}

.dark {
   --background: oklch(0.145 0 0);
   --foreground: oklch(0.985 0 0);
   --card: oklch(0.205 0 0);
   --card-foreground: oklch(0.985 0 0);
   --popover: oklch(0.205 0 0);
   --popover-foreground: oklch(0.985 0 0);
   --primary: oklch(0.922 0 0);
   --primary-foreground: oklch(0.205 0 0);
   --secondary: oklch(0.269 0 0);
   --secondary-foreground: oklch(0.985 0 0);
   --muted: oklch(0.269 0 0);
   --muted-foreground: oklch(0.708 0 0);
   --accent: oklch(0.269 0 0);
   --accent-foreground: oklch(0.985 0 0);
   --destructive: oklch(0.704 0.191 22.216);
   --border: oklch(1 0 0 / 10%);
   --input: oklch(1 0 0 / 15%);
   --ring: oklch(0.556 0 0);
   --chart-1: oklch(0.488 0.243 264.376);
   --chart-2: oklch(0.696 0.17 162.48);
   --chart-3: oklch(0.769 0.188 70.08);
   --chart-4: oklch(0.627 0.265 303.9);
   --chart-5: oklch(0.645 0.246 16.439);
   --sidebar: oklch(0.205 0 0);
   --sidebar-foreground: oklch(0.985 0 0);
   --sidebar-primary: oklch(0.488 0.243 264.376);
   --sidebar-primary-foreground: oklch(0.985 0 0);
   --sidebar-accent: oklch(0.269 0 0);
   --sidebar-accent-foreground: oklch(0.985 0 0);
   --sidebar-border: oklch(1 0 0 / 10%);
   --sidebar-ring: oklch(0.556 0 0);
}

@theme inline {
   --font-nunitoSans: 'Nunito Sans', sans-serif;
   --font-poppins: 'Poppins', sans-serif;

   --color-black: var(--black);
   --color-white: var(--white);

   --color-jet: var(--jet);
   --color-midnight: var(--midnight);
   --color-charcoal: var(--charcoal);
   --color-smoky: var(--smoky);
   --color-stone: var(--stone);
   --color-steel: var(--steel);
   --color-lavenderGray: var(--lavenderGray);
   --color-slate: var(--slate);
   --color-porcelain: var(--porcelain);
   --color-ash: var(--ash);
   --color-fog: var(--fog);
   --color-cloud: var(--cloud);
   --color-silver: var(--silver);

   --color-skyLight: var(--skyLight);
   --color-navy: var(--navy);
   --color-indigo: var(--indigo);
   --color-azure: var(--azure);
   --color-cerulean: var(--cerulean);
   --color-royal: var(--royal);

   --color-lime: var(--lime);
   --color-amber: var(--amber);
   --color-orange: var(--orange);
   --color-crimson: var(--crimson);
   --color-vermilion: var(--vermilion);
   --color-emerald: var(--emerald);
   --color-golden: var(--golden);
   --color-ruby: var(--ruby);

   --font-h1: 2.5rem;
   --font-h2: 2rem;
   --font-h3: 1.875rem;
   --font-h4: 1.5rem;
   --font-h5: 1.25rem;
   --font-h6: 1.125rem;

   --font-p1: 1.375rem;
   --font-p2: 1.25rem;
   --font-p3: 1.125rem;
   --font-p4: 1rem;
   --font-p5: 0.875rem;
   --font-p6: 0.75rem;

   --radius: 14px;
   --radius-sm: calc(var(--radius) - 4px);
   --radius-md: calc(var(--radius) - 2px);
   --radius-lg: var(--radius);
   --radius-xl: calc(var(--radius) + 2px);
   --radius-2xl: calc(var(--radius) + 4px);
   --radius-3xl: calc(var(--radius) + 6px);
   --radius-4xl: calc(var(--radius) + 8px);

   --shadow-md:
      0px 4px 8px -2px rgba(10, 13, 18, 0.1),
      0px 2px 4px -2px rgba(10, 13, 18, 0.06);

   --shadow-lg:
      0px 12px 16px -4px rgba(10, 13, 18, 0.08),
      0px 4px 6px -2px rgba(10, 13, 18, 0.03);
}

@layer base {
   body {
      @apply bg-white text-black;
   }
}

@layer components {
   .skeleton-shimmer {
      position: relative;
      overflow: hidden;
      background: linear-gradient(
         90deg,
         var(--color-porcelain) 0%,
         var(--color-silver) 50%,
         var(--color-porcelain) 100%
      );
      background-size: 1000px 100%;
      animation: shimmer 2s infinite linear;
   }
}

@layer utilities {
   /* For screens above 2001px  */
   @media (min-width: 2001px) {
      /* Paragraphs */
      .para1 {
         font-size: calc(var(--font-p1) * 1.2);
         line-height: 170%;
      }

      .para2 {
         font-size: calc(var(--font-p2) * 1.2);
         line-height: 170%;
      }

      .para3 {
         font-size: calc(var(--font-p3) * 1.2);
         line-height: 170%;
      }

      .para4 {
         font-size: calc(var(--font-p4) * 1.2);
         line-height: 170%;
      }

      .para5 {
         font-size: calc(var(--font-p5) * 1.2);
         line-height: 170%;
      }

      .para6 {
         font-size: calc(var(--font-p6) * 1.2);
         line-height: 170%;
      }

      /* Headings */
      .head1 {
         font-size: calc(var(--font-h1) * 1.2);
         line-height: 140%;
         font-family: var(--font-poppins) !important;
      }

      .head2 {
         font-size: calc(var(--font-h2) * 1.2);
         line-height: 140%;
         font-family: var(--font-poppins) !important;
      }

      .head3 {
         font-size: calc(var(--font-h3) * 1.2);
         line-height: 140%;
         font-family: var(--font-poppins) !important;
      }

      .head4 {
         font-size: calc(var(--font-h4) * 1.2);
         line-height: 140%;
         font-family: var(--font-poppins) !important;
      }

      .head5 {
         font-size: calc(var(--font-h5) * 1.2);
         line-height: 140%;
         font-family: var(--font-poppins) !important;
      }

      .head6 {
         font-size: calc(var(--font-h6) * 1.2);
         line-height: 140%;
      }
   }

   /* For screens from 1560px to 2000px (Line height: 150%) */
   @media (max-width: 2000px) and (min-width: 1561px) {
      /* Paragraphs */
      .para1 {
         font-size: calc(var(--font-p1) * 1.1);
         line-height: 170%;
      }

      .para2 {
         font-size: calc(var(--font-p2) * 1.1);
         line-height: 170%;
      }

      .para3 {
         font-size: calc(var(--font-p3) * 1.1);
         line-height: 170%;
      }

      .para4 {
         font-size: calc(var(--font-p4) * 1.1);
         line-height: 170%;
      }

      .para5 {
         font-size: calc(var(--font-p5) * 1.1);
         line-height: 170%;
      }

      .para6 {
         font-size: calc(var(--font-p6) * 1.1);
         line-height: 170%;
      }

      /* Headings */
      .head1 {
         font-size: calc(var(--font-h1) * 1.1);
         line-height: 140%;
         font-family: var(--font-poppins) !important;
      }

      .head2 {
         font-size: calc(var(--font-h2) * 1.1);
         line-height: 140%;
         font-family: var(--font-poppins) !important;
      }

      .head3 {
         font-size: calc(var(--font-h3) * 1.1);
         line-height: 140%;
         font-family: var(--font-poppins) !important;
      }

      .head4 {
         font-size: calc(var(--font-h4) * 1.1);
         line-height: 140%;
         font-family: var(--font-poppins) !important;
      }

      .head5 {
         font-size: calc(var(--font-h5) * 1.1);
         line-height: 140%;
         font-family: var(--font-poppins) !important;
      }

      .head6 {
         font-size: calc(var(--font-h6) * 1.1);
         line-height: 140%;
      }
   }

   /* For screens below1 1560px */
   @media (max-width: 1560px) {
      /* Paragraphs */
      .para1 {
         font-size: var(--font-p1);
         line-height: 170%;
      }

      .para2 {
         font-size: var(--font-p2);
         line-height: 170%;
      }

      .para3 {
         font-size: var(--font-p3);
         line-height: 170%;
      }

      .para4 {
         font-size: var(--font-p4);
         line-height: 170%;
      }

      .para5 {
         font-size: var(--font-p5);
         line-height: 170%;
      }

      .para6 {
         font-size: var(--font-p6);
         line-height: 170%;
      }

      /* Headings */
      .head1 {
         font-size: var(--font-h1);
         line-height: 140%;
         font-family: var(--font-poppins) !important;
      }

      .head2 {
         font-size: var(--font-h2);
         line-height: 140%;
         font-family: var(--font-poppins) !important;
      }

      .head3 {
         font-size: var(--font-h3);
         line-height: 140%;
         font-family: var(--font-poppins) !important;
      }

      .head4 {
         font-size: var(--font-h4);
         line-height: 140%;
         font-family: var(--font-poppins) !important;
      }

      .head5 {
         font-size: var(--font-h5);
         line-height: 140%;
         font-family: var(--font-poppins) !important;
      }

      .head6 {
         font-size: var(--font-h6);
         line-height: 140%;
      }
   }

   /* For screens from 769px to 1024px (Line height: 140%) */
   /* @media (max-width: 1024px) and (min-width: 769px) {
      .para3 {
         font-size: calc(var(--font-p3) * 0.9);
         line-height: 130%;
      }

      .para3-bold {
         font-size: calc(var(--font-p3) * 0.9);
         font-weight: 700;
         line-height: 130%;
      }

      .para2 {
         font-size: calc(var(--font-p2) * 0.9);
         line-height: 130%;
      }

      .para2-bold {
         font-size: calc(var(--font-p2) * 0.9);
         font-weight: 700;
         line-height: 130%;
      }

      .para1 {
         font-size: calc(var(--font-p1) * 0.9);
         line-height: 130%;
      }

      .para1-bold {
         font-size: calc(var(--font-p1) * 0.9);
         font-weight: 700;
         line-height: 130%;
      }

      .head4 {
         font-size: calc(var(--font-h4) * 0.9);
         line-height: 130%;
      }

      .head4-bold {
         font-size: calc(var(--font-h4) * 0.9);
         font-weight: 700;
         line-height: 130%;
      }

      .head3 {
         font-size: calc(var(--font-h3) * 0.9);
         line-height: 130%;
      }

      .head3-bold {
         font-size: calc(var(--font-h3) * 0.9);
         font-weight: 700;
         line-height: 130%;
      }

      .head2 {
         font-size: calc(var(--font-h2) * 0.9);
         line-height: 130%;
      }

      .head2-bold {
         font-size: calc(var(--font-h2) * 0.9);
         font-weight: 700;
         line-height: 130%;
      }

      .head1 {
         font-size: calc(var(--font-h1) * 0.9);
         line-height: 150%;
      }

      .head1-bold {
         font-size: calc(var(--font-h1) * 0.9);
         font-weight: 700;
         line-height: 150%;
      }
   } */

   /* For screens from 768px to 426px (Font size:0.8 times, Line height: 130%) */
   /* @media (max-width: 768px) and (min-width: 426px) {
      .para3 {
         font-size: calc(var(--font-p3) * 0.8);
         line-height: 130%;
      }

      .para3-bold {
         font-size: calc(var(--font-p3) * 0.8);
         font-weight: 700;
         line-height: 130%;
      }

      .para2 {
         font-size: calc(var(--font-p2) * 0.8);
         line-height: 130%;
      }

      .para2-bold {
         font-size: calc(var(--font-p2) * 0.8);
         font-weight: 700;
         line-height: 130%;
      }

      .para1 {
         font-size: calc(var(--font-p1) * 0.8);
         line-height: 130%;
      }

      .para1-bold {
         font-size: calc(var(--font-p1) * 0.8);
         font-weight: 700;
         line-height: 130%;
      }

      .head4 {
         font-size: calc(var(--font-h4) * 0.8);
         line-height: 130%;
      }

      .head4-bold {
         font-size: calc(var(--font-h4) * 0.8);
         font-weight: 700;
         line-height: 130%;
      }

      .head3 {
         font-size: calc(var(--font-h3) * 0.8);
         line-height: 130%;
      }

      .head3-bold {
         font-size: calc(var(--font-h3) * 0.8);
         font-weight: 700;
         line-height: 130%;
      }

      .head2 {
         font-size: calc(var(--font-h2) * 0.8);
         line-height: 130%;
      }

      .head2-bold {
         font-size: calc(var(--font-h2) * 0.8);
         font-weight: 700;
         line-height: 130%;
      }

      .head1 {
         font-size: calc(var(--font-h1) * 0.8);
         line-height: 150%;
      }

      .head1-bold {
         font-size: calc(var(--font-h1) * 0.8);
         font-weight: 700;
         line-height: 150%;
      }
   } */

   /* For screens 425px and below (Font size:0.7 times, Line height: 125%) */
   /* @media (max-width: 425px) {
      .para3 {
         font-size: calc(var(--font-p3) * 0.7);
         line-height: 125%;
      }

      .para3-bold {
         font-size: calc(var(--font-p3) * 0.7);
         font-weight: 700;
         line-height: 125%;
      }

      .para2 {
         font-size: calc(var(--font-p2) * 0.7);
         line-height: 125%;
      }

      .para2-bold {
         font-size: calc(var(--font-p2) * 0.7);
         font-weight: 700;
         line-height: 125%;
      }

      .para1 {
         font-size: calc(var(--font-p1) * 0.7);
         line-height: 125%;
      }

      .para1-bold {
         font-size: calc(var(--font-p1) * 0.7);
         font-weight: 700;
         line-height: 125%;
      }

      .head4 {
         font-size: calc(var(--font-h4) * 0.7);
         line-height: 125%;
      }

      .head4-bold {
         font-size: calc(var(--font-h4) * 0.7);
         font-weight: 700;
         line-height: 125%;
      }

      .head3 {
         font-size: calc(var(--font-h3) * 0.7);
         line-height: 125%;
      }

      .head3-bold {
         font-size: calc(var(--font-h3) * 0.7);
         font-weight: 700;
         line-height: 125%;
      }

      .head2 {
         font-size: calc(var(--font-h2) * 0.7);
         line-height: 125%;
      }

      .head2-bold {
         font-size: calc(var(--font-h2) * 0.7);
         font-weight: 700;
         line-height: 125%;
      }

      .head1 {
         font-size: calc(var(--font-h1) * 0.7);
         line-height: 150%;
      }

      .head1-bold {
         font-size: calc(var(--font-h1) * 0.7);
         font-weight: 700;
         line-height: 150%;
      }
   } */
}

@keyframes shimmer {
   0% {
      background-position: -1000px 0;
   }

   100% {
      background-position: 1000px 0;
   }
}
