import {
   Button,
   Checkbox,
   Flex,
   Input,
   InputGroup,
   InputLeftElement,
   NumberDecrementStepper,
   NumberIncrementStepper,
   NumberInput,
   NumberInputField,
   NumberInputStepper,
   Select,
   Spinner,
   Text,
   useOutsideClick,
   useToast,
} from '@chakra-ui/react';
import { IoSearchSharp } from 'react-icons/io5';
import './../shipping-profile/shipping-profile.scss';
import ModalWrapper from '../modal-wrapper';
import { useAppSelector } from '../../../store/store';
import { Controller, useForm } from 'react-hook-form';
import { VariableExpense } from '../../../pages/settings/interface';
import {
   AutoComplete,
   AutoCompleteCreatable,
   AutoCompleteInput,
   AutoCompleteItem,
   AutoCompleteList,
} from '@choc-ui/chakra-autocomplete';
import {
   CUSTOM_EXPENSES_STRING,
   METRIC_LIST,
} from '../../../utils/strings/cfo';
import { MdOutlinePercent } from 'react-icons/md';
import DatePicker from 'react-datepicker';
import { useDispatch } from 'react-redux';
import { closeModal } from '../../../store/reducer/modal-reducer';
import { CFOKeys } from '../../../pages/dashboard/utils/query-keys';
import endPoints, { OutNewId } from '../../../api/service/cfo';
import { useApiMutation } from '../../../hooks/react-query-hooks';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { KPICategory } from '../../../utils/strings/kpi-constants';
import { setVariableExpenses } from '../../../store/reducer/cfo-reducer';
import { AiOutlineInfoCircle } from 'react-icons/ai';
import './ce.scss';
import { FaPlusCircle } from 'react-icons/fa';
import { useRef, useState } from 'react';
function VariableExpenseModal() {
   const { payload } = useAppSelector((state) => state.modal);
   const { variableExpenses } = useAppSelector((state) => state.cfo);

   const [isListOpen, setIsListOpen] = useState(false);
   const [isSourceOpen, setIsSourceOpen] = useState(false);
   const categoryRef = useRef<HTMLDivElement>(null);
   const sourceRef = useRef<HTMLDivElement>(null);
   const { kpiMeta } = useAppSelector((state) => state.kpi);
   const sourceList: string[] = kpiMeta
      .reduce((prev: string[], curr) => {
         if (!prev.includes(curr.category)) return [...prev, curr.category];
         return prev;
      }, [] as string[])
      .map((x) => KPICategory[x] || x);
   const dispatch = useDispatch();
   const toast = useToast();
   const modalProps = (payload?.modalProps || {
      name: '',
      campaign: '',
      metric: '',
      source: '',
      categories:
         variableExpenses.length == 0
            ? []
            : (JSON.parse(variableExpenses[0].categories) as string[]),
      selCategory: '',
      startDate: null,
      endDate: null,
      percent: '',
      adSpend: false,
   }) as VariableExpense;
   const {
      register,
      setValue,
      formState: { errors },
      watch,
      handleSubmit,
      control,
   } = useForm<VariableExpense>({
      defaultValues: modalProps,
   });
   const categories = watch('categories');
   const onSubmit = (data: VariableExpense) => {
      const startDate = data.startDate ? new Date(data.startDate) : null;
      const endDate = data.endDate ? new Date(data.endDate) : null;
      upsertVariableExpense({
         ...data,
         clientId: LocalStorageService.getItem(Keys.ClientId) as string,
         id: modalProps?.id ?? null,
         startDate: startDate
            ? `${startDate?.getFullYear()}-${startDate?.getMonth() + 1}-${startDate?.getDate()}`
            : null,
         endDate: endDate
            ? `${endDate?.getFullYear()}-${endDate?.getMonth() + 1}-${endDate?.getDate()}`
            : null,
      });
   };
   const handleSuccess = (data: OutNewId[], payload?: VariableExpense) => {
      dispatch(closeModal());
      toast({
         title: 'Variable Expense Created',
         status: 'success',
         duration: 2000,
         isClosable: true,
      });
      if (payload) {
         dispatch(
            setVariableExpenses([
               ...variableExpenses.filter((x) => x.id !== data[0].new_id),
               {
                  id: data[0].new_id,
                  name: payload.name,
                  percent: payload.percent,
                  metric: payload.metric,
                  source: payload.source,
                  campaign: payload.campaign,
                  categories: JSON.stringify(payload.categories),
                  sel_category: payload.selCategory,
                  start_date: payload.startDate,
                  end_date: payload.endDate,
                  ad_spend: payload.adSpend,
               },
            ]),
         );
      }
   };
   const { mutate: upsertVariableExpense, isPending: saveLoad } =
      useApiMutation({
         queryKey: [CFOKeys.upsertVariableExpense],
         mutationFn: endPoints.upsertVariableExpense,
         onSuccessHandler: handleSuccess,
      });
   useOutsideClick({
      ref: categoryRef,
      handler: () => {
         if (categoryRef.current) {
            setTimeout(() => setIsListOpen(false), 100);
         }
      },
   });
   useOutsideClick({
      ref: sourceRef,
      handler: () => {
         if (sourceRef.current) {
            setTimeout(() => setIsSourceOpen(false), 100);
         }
      },
   });
   return (
      <ModalWrapper
         heading={`${modalProps?.id ? 'Edit' : 'Add New'} Variable Expense`}
         parentClassName='variable-expense'
      >
         <Flex direction={'column'} gap={3}>
            {modalProps?.id && (
               <Text
                  className='ce-edit'
                  display={'flex'}
                  gap={2}
                  fontSize={'14px'}
                  p={4}
                  border={'1px solid #DDE9FF'}
                  borderRadius={'6px'}
                  backgroundColor={'#F0F8FD'}
               >
                  <AiOutlineInfoCircle />{' '}
                  {CUSTOM_EXPENSES_STRING.editVairableExpense}
               </Text>
            )}
            <form onSubmit={handleSubmit(onSubmit) as () => void}>
               <Flex direction={'column'} gap={1}>
                  <Input
                     aria-invalid={errors.name ? true : false}
                     {...register('name', {
                        required: 'Title is required',
                     })}
                     className='inputs'
                     placeholder='Name your custom variable expense'
                  />
                  {errors.name && (
                     <p className='err-message' role='alert'>
                        {'  '}
                        {errors.name?.message}
                     </p>
                  )}
               </Flex>
               <Flex
                  direction={'column'}
                  gap={1}
                  className='category'
                  ref={categoryRef}
               >
                  <Controller
                     control={control}
                     rules={{
                        required: 'Category is required',
                     }}
                     render={({ field: { onChange, onBlur, value } }) => (
                        <AutoComplete
                           openOnFocus
                           creatable
                           closeOnBlur={false}
                           closeOnSelect
                           restoreOnBlurIfEmpty={false}
                           suggestWhenEmpty
                           onChange={onChange}
                           onBlur={onBlur}
                           value={value}
                           onSelectOption={({ item, isNewInput }) => {
                              setTimeout(() => setIsListOpen(false), 200);
                              if (isNewInput)
                                 setValue('categories', [
                                    ...categories,
                                    item.value,
                                 ] as string[]);
                           }}
                        >
                           <AutoCompleteInput
                              variant='filled'
                              placeholder={'Type in Category'}
                              onFocus={() => setIsListOpen(true)}
                           />
                           {isListOpen && (
                              <AutoCompleteList>
                                 <AutoCompleteCreatable alwaysDisplay>
                                    {() => (
                                       <span className='creatable-text'>
                                          Click <FaPlusCircle /> to Add{' '}
                                       </span>
                                    )}
                                 </AutoCompleteCreatable>
                                 {categories.map((person, oid) => (
                                    <AutoCompleteItem
                                       key={`option-${oid}`}
                                       value={person}
                                       textTransform='capitalize'
                                       align='center'
                                    >
                                       <Text ml='4'>{person}</Text>
                                    </AutoCompleteItem>
                                 ))}
                              </AutoCompleteList>
                           )}
                        </AutoComplete>
                     )}
                     name='selCategory'
                  />
                  {errors.selCategory && (
                     <p className='err-message' role='alert'>
                        {'  '}
                        {errors.selCategory?.message}
                     </p>
                  )}
               </Flex>
               <Controller
                  control={control}
                  rules={{
                     required: 'Metric is required',
                  }}
                  render={({ field: { onChange, onBlur, value } }) => (
                     <Select
                        onChange={onChange}
                        onBlur={onBlur}
                        value={value}
                        cursor={'pointer'}
                        placeholder='Set Metric'
                        height={'40px'}
                        fontSize={'15px'}
                     >
                        {METRIC_LIST.map((kpi: string) => (
                           <option key={kpi} value={kpi}>
                              {kpi}
                           </option>
                        ))}
                     </Select>
                  )}
                  name='metric'
               />
               {errors.metric && (
                  <p className='err-message' role='alert'>
                     {'  '}
                     {errors.metric?.message}
                  </p>
               )}
               <Controller
                  control={control}
                  rules={{
                     required: 'Percent is required',
                  }}
                  render={({ field: { onChange, onBlur, value } }) => (
                     <InputGroup backgroundColor={'white'}>
                        <InputLeftElement pointerEvents='none' top={''}>
                           <MdOutlinePercent />
                        </InputLeftElement>
                        <NumberInput
                           width={'100%'}
                           aria-invalid={errors.percent ? true : false}
                           onChange={onChange}
                           onBlur={onBlur}
                           value={value}
                        >
                           <NumberInputField pl={10} placeholder='Percent' />
                           <NumberInputStepper>
                              <NumberIncrementStepper />
                              <NumberDecrementStepper />
                           </NumberInputStepper>
                        </NumberInput>
                     </InputGroup>
                  )}
                  name='percent'
               />

               {errors.percent && (
                  <p className='err-message' role='alert'>
                     {'  '}
                     {errors.percent?.message}
                  </p>
               )}
               <Flex className='source' ref={sourceRef}>
                  <Controller
                     control={control}
                     rules={{
                        required: 'Source is required',
                     }}
                     render={({ field: { onChange, onBlur, value } }) => (
                        <AutoComplete
                           onChange={onChange}
                           openOnFocus
                           creatable
                           closeOnBlur={false}
                           closeOnSelect
                           restoreOnBlurIfEmpty={false}
                           suggestWhenEmpty
                           onBlur={onBlur}
                           value={value}
                        >
                           <InputGroup backgroundColor={'white'}>
                              <InputLeftElement pointerEvents='none'>
                                 {' '}
                                 <IoSearchSharp />
                              </InputLeftElement>
                              <AutoCompleteInput
                                 variant='subtle'
                                 placeholder='Source'
                                 onFocus={() => setIsSourceOpen(true)}
                              />
                           </InputGroup>
                           {isSourceOpen && (
                              <AutoCompleteList>
                                 {sourceList.map((option, oid) => (
                                    <AutoCompleteItem
                                       key={`option-${oid}`}
                                       value={option}
                                       textTransform='capitalize'
                                    >
                                       {option}
                                    </AutoCompleteItem>
                                 ))}
                              </AutoCompleteList>
                           )}
                        </AutoComplete>
                     )}
                     name='source'
                  />
               </Flex>
               {errors.source && (
                  <p className='err-message' role='alert'>
                     {'  '}
                     {errors.source?.message}
                  </p>
               )}
               {/* <Flex>
                  <InputGroup backgroundColor={'white'}>
                     <InputLeftElement pointerEvents='none'>
                        {' '}
                        <IoSearchSharp />
                     </InputLeftElement>
                     <Input placeholder='Campaign' {...register('campaign')} />
                  </InputGroup>
               </Flex>
               <Text pt={4} fontSize={'12px'} color={'#717171'}>
                  {CUSTOM_EXPENSES_STRING.campaignAtVariableExp}
               </Text>
               {errors.campaign && (
                  <p className='err-message' role='alert'>
                     {'  '}
                     {errors.campaign?.message}
                  </p>
               )} */}
               <Flex direction={'column'} gap={1}>
                  <Flex>
                     {' '}
                     <Checkbox {...register('adSpend')}>Ad Spend</Checkbox>
                  </Flex>
                  <Text fontSize={'12px'} color={'#717171'}>
                     {CUSTOM_EXPENSES_STRING.adSpendHelpText}
                  </Text>
                  {errors.adSpend && (
                     <p className='err-message' role='alert'>
                        {'  '}
                        {errors.adSpend?.message}
                     </p>
                  )}
               </Flex>
               <Flex justifyContent={'space-between'} alignItems={'center'}>
                  <Text>Starting </Text>
                  <div>
                     <Controller
                        control={control}
                        rules={{
                           required: 'Start Date is required',
                        }}
                        render={({ field: { onChange, onBlur, value } }) => (
                           <DatePicker
                              placeholderText='Select a date'
                              dateFormat='MMM d, yyyy'
                              selected={value as Date}
                              onChange={onChange}
                              onBlur={onBlur}
                              maxDate={new Date()}
                           />
                        )}
                        name='startDate'
                     />
                  </div>
               </Flex>

               {errors.startDate && (
                  <p className='err-message' role='alert'>
                     {'  '}
                     {errors.startDate?.message}
                  </p>
               )}
               <Flex justifyContent={'space-between'} alignItems={'center'}>
                  <Text>Ending (optional) </Text>
                  <Controller
                     control={control}
                     render={({ field: { onChange, onBlur, value } }) => (
                        <DatePicker
                           placeholderText='Select a date'
                           dateFormat='MMM d, yyyy'
                           selected={value as Date}
                           onChange={onChange}
                           onBlur={onBlur}
                        />
                     )}
                     name='endDate'
                  />
               </Flex>

               <Flex justifyContent={'flex-end'} width={'100% !important'}>
                  <Button
                     type='submit'
                     onClick={handleSubmit(onSubmit) as () => void}
                     color={'white'}
                     _hover={{
                        backgroundColor: '#437EEBBB',
                     }}
                     backgroundColor={'#437EEB'}
                     py={4}
                     px={6}
                     border={'1px solid #437EEB'}
                     borderRadius={'7px'}
                     disabled={saveLoad}
                  >
                     Save
                     {saveLoad && (
                        <Spinner
                           ml={2}
                           thickness='4px'
                           speed='0.65s'
                           emptyColor='gray.200'
                           color='blue.500'
                           size='sm'
                        />
                     )}
                  </Button>
               </Flex>
            </form>
         </Flex>
      </ModalWrapper>
   );
}

export default VariableExpenseModal;
