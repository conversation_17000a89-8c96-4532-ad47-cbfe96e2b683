import { AlertingAgentChat } from '@/api/service/agentic-workflow/alerting-agent';
import { AnalyticsAgentChat } from '@/api/service/agentic-workflow/analytics-agent';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export type Agents =
   | 'alerting-agent'
   | 'analytics-agent'
   | 'meta-ads-manager-agent'
   | 'meta-ads-manager-auto';

interface InitialState {
   currentAgent: Agents;
   currentHistory: {
      'alerting-agent': AlertingAgentChat[];
      'analytics-agent': AnalyticsAgentChat[];
      // 'meta-ads-manager-auto': AlertingAgentChat[];
   };
}

const initialState: InitialState = {
   currentAgent: 'analytics-agent',
   currentHistory: {
      'alerting-agent': [],
      'analytics-agent': [],
      // 'meta-ads-manager-auto': [],
   },
};

const marcoSlice = createSlice({
   name: 'marco',
   initialState,
   reducers: {
      setCurrentAgent: (state, action: PayloadAction<Agents>) => {
         state.currentAgent = action.payload;
      },
      setCurrentHistory: (
         state,
         action: PayloadAction<{
            agent: Agents;
            history: AlertingAgentChat[] | AnalyticsAgentChat[];
         }>,
      ) => {
         const { agent, history } = action.payload;

         const merged = [
            ...state.currentHistory[
               agent as 'alerting-agent' | 'analytics-agent'
            ],
            ...history,
         ].filter((chat) => chat.session_id);

         const uniqueBySessionId = Array.from(
            new Map(merged.map((chat) => [chat.session_id, chat])).values(),
         );

         if (agent === 'alerting-agent') {
            state.currentHistory['alerting-agent'] =
               uniqueBySessionId as AlertingAgentChat[];
         } else if (agent === 'analytics-agent') {
            state.currentHistory['analytics-agent'] =
               uniqueBySessionId as AnalyticsAgentChat[];
         }
      },
   },
});

export const { setCurrentAgent, setCurrentHistory } = marcoSlice.actions;

export default marcoSlice.reducer;
