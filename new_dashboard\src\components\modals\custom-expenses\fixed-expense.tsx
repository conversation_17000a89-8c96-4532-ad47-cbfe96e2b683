import {
   Button,
   Checkbox,
   Flex,
   Input,
   InputGroup,
   InputLeftElement,
   NumberDecrementStepper,
   NumberIncrementStepper,
   NumberInput,
   NumberInputField,
   NumberInputStepper,
   Spinner,
   Text,
   useOutsideClick,
   useToast,
} from '@chakra-ui/react';
import { FaPlusCircle } from 'react-icons/fa';
import ModalWrapper from '../modal-wrapper';
import { useAppSelector } from '../../../store/store';
import { Controller, useForm } from 'react-hook-form';
import { FixedExpense } from '../../../pages/settings/interface';
import { FaIndianRupeeSign } from 'react-icons/fa6';
import {
   AutoComplete,
   AutoCompleteCreatable,
   AutoCompleteInput,
   AutoCompleteItem,
   AutoCompleteList,
} from '@choc-ui/chakra-autocomplete';
import { CUSTOM_EXPENSES_STRING } from '../../../utils/strings/cfo';
import './../shipping-profile/shipping-profile.scss';
import DatePicker from 'react-datepicker';

import 'react-datepicker/dist/react-datepicker.css';
import { CFOKeys } from '../../../pages/dashboard/utils/query-keys';
import endPoints, { OutNewId } from '../../../api/service/cfo';
import { useApiMutation } from '../../../hooks/react-query-hooks';
import { useDispatch } from 'react-redux';
import { closeModal } from '../../../store/reducer/modal-reducer';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { setFixedExpenses } from '../../../store/reducer/cfo-reducer';
import { AiOutlineInfoCircle } from 'react-icons/ai';
import { KPICategory } from '../../../utils/strings/kpi-constants';
import { IoSearchSharp } from 'react-icons/io5';
import { useRef, useState } from 'react';
function FixedExpenseModal() {
   const { payload } = useAppSelector((state) => state.modal);
   const { fixedExpenses } = useAppSelector((state) => state.cfo);
   const { kpiMeta } = useAppSelector((state) => state.kpi);
   const [isListOpen, setIsListOpen] = useState(false);
   const [isSourceOpen, setIsSourceOpen] = useState(false);
   const categoryRef = useRef<HTMLDivElement>(null);
   const sourceRef = useRef<HTMLDivElement>(null);
   const sourceList: string[] = kpiMeta
      .reduce((prev: string[], curr) => {
         if (!prev.includes(curr.category)) return [...prev, curr.category];
         return prev;
      }, [] as string[])
      .map((x) => KPICategory[x] || x);
   const dispatch = useDispatch();
   const toast = useToast();
   const modalProps = (payload?.modalProps || {
      title: '',
      cost: '',
      source: '',
      categories:
         fixedExpenses.length == 0
            ? []
            : (JSON.parse(fixedExpenses[0].categories) as string[]),
      selCategory: '',
      startDate: null,
      endDate: null,
      adSpend: false,
      recurringDays: '',
   }) as FixedExpense;
   const {
      register,
      setValue,
      formState: { errors },
      watch,
      control,
      handleSubmit,
   } = useForm<FixedExpense>({
      defaultValues: modalProps,
   });
   const categories = watch('categories');
   const onSubmit = (data: FixedExpense) => {
      const startDate = data.startDate ? new Date(data.startDate) : null;
      const endDate = data.endDate ? new Date(data.endDate) : null;
      upsertFixedExpense({
         ...data,
         clientId: LocalStorageService.getItem(Keys.ClientId) as string,
         id: modalProps.id ?? null,
         startDate: startDate
            ? `${startDate?.getFullYear()}-${startDate?.getMonth() + 1}-${startDate?.getDate()}`
            : null,
         endDate: endDate
            ? `${endDate?.getFullYear()}-${endDate?.getMonth() + 1}-${endDate?.getDate()}`
            : null,
      });
   };
   const handleSuccess = (data: OutNewId[], payload?: FixedExpense) => {
      dispatch(closeModal());
      toast({
         title: 'Fixed Expense Created',
         status: 'success',
         duration: 2000,
         isClosable: true,
      });
      if (payload) {
         dispatch(
            setFixedExpenses([
               ...fixedExpenses.filter((x) => x.id !== data[0].new_id),
               {
                  id: data[0].new_id,
                  title: payload.title,
                  cost: payload.cost,
                  source: payload.source,
                  categories: JSON.stringify(payload.categories),
                  sel_category: payload.selCategory,
                  recurring_days: payload.recurringDays,
                  start_date: payload.startDate,
                  end_date: payload.endDate,
                  ad_spend: payload.adSpend,
               },
            ]),
         );
      }
   };
   const { mutate: upsertFixedExpense, isPending: saveLoad } = useApiMutation({
      queryKey: [CFOKeys.upsertFixedExpense],
      mutationFn: endPoints.upsertFixedExpense,
      onSuccessHandler: handleSuccess,
   });
   useOutsideClick({
      ref: categoryRef,
      handler: () => {
         if (categoryRef.current) {
            setTimeout(() => setIsListOpen(false), 100);
         }
      },
   });
   useOutsideClick({
      ref: sourceRef,
      handler: () => {
         if (sourceRef.current) {
            setTimeout(() => setIsSourceOpen(false), 100);
         }
      },
   });
   return (
      <ModalWrapper
         heading={`${modalProps?.id ? 'Edit' : 'Add New'} Fixed Expense`}
         parentClassName='fixed-expense'
      >
         <Flex direction={'column'} gap={3}>
            {modalProps?.id && (
               <Text
                  className='ce-edit'
                  display={'flex'}
                  gap={2}
                  fontSize={'14px'}
                  p={4}
                  border={'1px solid #DDE9FF'}
                  borderRadius={'6px'}
                  backgroundColor={'#F0F8FD'}
               >
                  <AiOutlineInfoCircle />{' '}
                  {CUSTOM_EXPENSES_STRING.editFixedExpense}
               </Text>
            )}
            <form onSubmit={handleSubmit(onSubmit) as () => void}>
               <Flex direction={'column'} gap={1}>
                  <Text>Title</Text>
                  <Input
                     aria-invalid={errors.title ? true : false}
                     {...register('title', {
                        required: 'Title is required',
                     })}
                     className='inputs'
                     placeholder='Name your custom expense'
                  />
                  {errors.title && (
                     <p className='err-message' role='alert'>
                        {'  '}
                        {errors.title?.message}
                     </p>
                  )}
               </Flex>
               <Flex direction={'column'} gap={1}>
                  <Text>Cost</Text>
                  <Controller
                     control={control}
                     rules={{
                        required: 'Cost is required',
                     }}
                     render={({ field: { onChange, onBlur, value } }) => (
                        <InputGroup backgroundColor={'white'}>
                           <InputLeftElement pointerEvents='none'>
                              {' '}
                              <FaIndianRupeeSign />
                           </InputLeftElement>
                           <NumberInput
                              width={'100%'}
                              aria-invalid={errors.cost ? true : false}
                              onChange={onChange}
                              onBlur={onBlur}
                              value={value}
                           >
                              <NumberInputField pl={8} />
                              <NumberInputStepper>
                                 <NumberIncrementStepper />
                                 <NumberDecrementStepper />
                              </NumberInputStepper>
                           </NumberInput>
                        </InputGroup>
                     )}
                     name='cost'
                  />

                  {errors.cost && (
                     <p className='err-message' role='alert'>
                        {'  '}
                        {errors.cost?.message}
                     </p>
                  )}
               </Flex>{' '}
               <Flex
                  direction={'column'}
                  className='category'
                  gap={1}
                  ref={categoryRef}
               >
                  <Text>Category</Text>
                  <Controller
                     control={control}
                     rules={{
                        required: 'Category is required',
                     }}
                     render={({ field: { onChange, onBlur, value } }) => (
                        <AutoComplete
                           openOnFocus
                           creatable
                           closeOnBlur={false}
                           closeOnSelect
                           restoreOnBlurIfEmpty={false}
                           suggestWhenEmpty
                           onChange={onChange}
                           onBlur={onBlur}
                           value={value}
                           onSelectOption={({ item, isNewInput }) => {
                              setTimeout(() => setIsListOpen(false), 200);
                              if (isNewInput)
                                 setValue('categories', [
                                    ...categories,
                                    item.value,
                                 ] as string[]);
                           }}
                        >
                           <AutoCompleteInput
                              variant='filled'
                              placeholder={'Type in Category'}
                              onFocus={() => setIsListOpen(true)}
                           />
                           {isListOpen && (
                              <AutoCompleteList>
                                 <AutoCompleteCreatable alwaysDisplay>
                                    {() => (
                                       <span className='creatable-text'>
                                          Click <FaPlusCircle /> to Add{' '}
                                       </span>
                                    )}
                                 </AutoCompleteCreatable>
                                 {categories.map((person, oid) => (
                                    <AutoCompleteItem
                                       key={`option-${oid}`}
                                       value={person}
                                       textTransform='capitalize'
                                       align='center'
                                    >
                                       <Text ml='4'>{person}</Text>
                                    </AutoCompleteItem>
                                 ))}
                              </AutoCompleteList>
                           )}
                        </AutoComplete>
                     )}
                     name='selCategory'
                  />

                  {errors.selCategory && (
                     <p className='err-message' role='alert'>
                        {'  '}
                        {errors.selCategory?.message}
                     </p>
                  )}
               </Flex>
               <Flex className='source' ref={sourceRef}>
                  <Controller
                     control={control}
                     rules={{
                        required: 'Source is required',
                     }}
                     render={({ field: { onChange, onBlur, value } }) => (
                        <AutoComplete
                           openOnFocus
                           creatable
                           closeOnBlur={false}
                           closeOnSelect
                           restoreOnBlurIfEmpty={false}
                           suggestWhenEmpty
                           onChange={onChange}
                           onBlur={onBlur}
                           value={value}
                        >
                           <InputGroup backgroundColor={'white'}>
                              <InputLeftElement pointerEvents='none'>
                                 {' '}
                                 <IoSearchSharp />
                              </InputLeftElement>
                              <AutoCompleteInput
                                 variant='subtle'
                                 placeholder='Source'
                                 onFocus={() => setIsSourceOpen(true)}
                              />
                           </InputGroup>
                           {isSourceOpen && (
                              <AutoCompleteList>
                                 {sourceList.map((option, oid) => (
                                    <AutoCompleteItem
                                       key={`option-${oid}`}
                                       value={option}
                                       textTransform='capitalize'
                                    >
                                       {option}
                                    </AutoCompleteItem>
                                 ))}
                              </AutoCompleteList>
                           )}
                        </AutoComplete>
                     )}
                     name='source'
                  />
               </Flex>
               {errors.source && (
                  <p className='err-message' role='alert'>
                     {'  '}
                     {errors.source?.message}
                  </p>
               )}
               <Flex
                  justifyContent={'space-between'}
                  alignItems={'center'}
                  pt={4}
               >
                  <Text>Starting </Text>
                  <div>
                     <Controller
                        control={control}
                        rules={{
                           required: 'Start Date is required',
                        }}
                        render={({ field: { onChange, onBlur, value } }) => (
                           <DatePicker
                              placeholderText='Select a date'
                              dateFormat='MMM d, yyyy'
                              selected={value as Date}
                              onChange={onChange}
                              onBlur={onBlur}
                              maxDate={new Date()}
                           />
                        )}
                        name='startDate'
                     />
                  </div>
               </Flex>
               {errors.startDate && (
                  <p className='err-message' role='alert'>
                     {'  '}
                     {errors.startDate?.message}
                  </p>
               )}
               <Flex direction={'column'} gap={4}>
                  <Flex
                     justifyContent={'space-between'}
                     alignItems={'center'}
                     gap={20}
                     pt={4}
                  >
                     <Text>Recurring</Text>
                     <Controller
                        control={control}
                        rules={{
                           required: 'Recurring days is required',
                        }}
                        render={({ field: { onChange, onBlur, value } }) => (
                           <InputGroup
                              backgroundColor={'white'}
                              width={'auto'}
                              minWidth={28}
                           >
                              <NumberInput
                                 onChange={onChange}
                                 onBlur={onBlur}
                                 value={value}
                              >
                                 <NumberInputField
                                    paddingInlineEnd={'70px'}
                                    aria-invalid={
                                       errors.recurringDays ? true : false
                                    }
                                 />
                                 <Text
                                    position={'absolute'}
                                    top={'7px'}
                                    right={'30px'}
                                 >
                                    days
                                 </Text>
                                 <NumberInputStepper>
                                    <NumberIncrementStepper />
                                    <NumberDecrementStepper />
                                 </NumberInputStepper>
                              </NumberInput>
                           </InputGroup>
                        )}
                        name='recurringDays'
                     />
                  </Flex>
                  {errors.recurringDays && (
                     <p className='err-message' role='alert'>
                        {'  '}
                        {errors.recurringDays?.message}
                     </p>
                  )}
               </Flex>
               <Flex
                  justifyContent={'space-between'}
                  alignItems={'center'}
                  pt={4}
               >
                  <Text>Ending (optional) </Text>
                  <Controller
                     control={control}
                     render={({ field: { onChange, onBlur, value } }) => (
                        <DatePicker
                           placeholderText='Select a date'
                           dateFormat='MMM d, yyyy'
                           selected={value as Date}
                           onChange={onChange}
                           onBlur={onBlur}
                        />
                     )}
                     name='endDate'
                  />
               </Flex>
               <Flex direction={'column'} gap={1} pt={4}>
                  <Flex>
                     {' '}
                     <Checkbox {...register('adSpend')}>Ad Spend</Checkbox>
                  </Flex>
                  <Text fontSize={'12px'} color={'#717171'}>
                     {CUSTOM_EXPENSES_STRING.adSpendHelpText}
                  </Text>
                  {errors.adSpend && (
                     <p className='err-message' role='alert'>
                        {'  '}
                        {errors.adSpend?.message}
                     </p>
                  )}
               </Flex>
               <Flex justifyContent={'flex-end'} width={'100% !important'}>
                  <Button
                     type='submit'
                     onClick={handleSubmit(onSubmit) as () => void}
                     color={'white'}
                     _hover={{
                        backgroundColor: '#437EEBBB',
                     }}
                     backgroundColor={'#437EEB'}
                     py={4}
                     px={6}
                     border={'1px solid #437EEB'}
                     borderRadius={'7px'}
                     disabled={saveLoad}
                  >
                     Save
                     {saveLoad && (
                        <Spinner
                           ml={2}
                           thickness='4px'
                           speed='0.65s'
                           emptyColor='gray.200'
                           color='blue.500'
                           size='sm'
                        />
                     )}
                  </Button>
               </Flex>
            </form>
         </Flex>
      </ModalWrapper>
   );
}

export default FixedExpenseModal;
