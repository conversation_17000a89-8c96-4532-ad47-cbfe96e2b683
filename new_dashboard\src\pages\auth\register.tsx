import React, { useState } from 'react';
import { <PERSON>a<PERSON>ye, FaEyeSlash } from 'react-icons/fa';
import {
   Box,
   Text,
   Button,
   FormControl,
   Input,
   Stack,
   Image,
   useToast,
   FormErrorMessage,
   InputGroup,
   InputRightElement,
   FormLabel,
   Checkbox,
   Link,
} from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { useApiMutation } from '../../hooks/react-query-hooks';
import { appStrings } from '../../utils/strings/app-strings';
import { registerStrings } from '../../utils/strings/auth-strings';
import { LocalStorageService, Keys } from '../../utils/local-storage';

import ICON from '../../assets/icons/icon.png';
import { regex } from '../../utils/strings/auth-strings';
import keys from '../../utils/strings/query-keys';
import authEndpoints from '../../api/service/auth';

function Register() {
   const [showPassword, setShowPassword] = useState(false);
   const [form, setForm] = useState({
      full_name: '',
      email_address: '',
      password: '',
      cb_product_updates: false,
      conditionsAndPolicy: false,
   });
   const [error, setError] = useState({
      full_name: '',
      email_address: '',
      password: '',
   });

   const toast = useToast();
   const navigate = useNavigate();

   const { mutate, isPending } = useApiMutation({
      queryKey: [keys.register],
      mutationFn: authEndpoints.register,
      onSuccessHandler: (response) => {
         LocalStorageService.setItem(Keys.UserName, response.email_address);
         navigate('/auth/email-verification');

         toast({
            title: 'Success',
            description: registerStrings.mailSent,
            status: 'success',
            duration: 5000,
            isClosable: true,
         });
      },
      onError: (message) => {
         toast({
            title: registerStrings.registerFailed,
            description: message,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
   });

   const validateFields = (name: string, value: string) => {
      if (name === 'full_name') {
         if (!value) {
            setError((prevErrors) => ({
               ...prevErrors,
               full_name: 'Full Name cannot be empty',
            }));
         } else {
            setError((prevErrors) => ({
               ...prevErrors,
               full_name: '',
            }));
         }
      }

      if (name === 'email_address') {
         if (!regex.email_address.test(value)) {
            setError((prevErrors) => ({
               ...prevErrors,
               email_address: 'Please enter a valid email address',
            }));
         } else {
            setError((prevErrors) => ({
               ...prevErrors,
               email_address: '',
            }));
         }
      }

      if (name === 'password') {
         if (!regex.password.test(value)) {
            setError((prevErrors) => ({
               ...prevErrors,
               password:
                  'Password must be at least 8 characters long, must have atleast one number and one special character',
            }));
         } else {
            setError((prevErrors) => ({
               ...prevErrors,
               password: '',
            }));
         }
      }
   };

   const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setForm((prevForm) => ({
         ...prevForm,
         [name]: value,
      }));
      validateFields(name, value);
   };

   const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, checked } = e.target;
      setForm((prevForm) => ({
         ...prevForm,
         [name]: checked,
      }));
   };

   const validateForm = () => {
      let isValid = true;
      let fullNameError = '';
      let emailError = '';
      let passwordError = '';
      if (!form.full_name) {
         fullNameError = 'Full name is required';
         isValid = false;
      }
      if (!form.email_address) {
         emailError = 'Email is required';
         isValid = false;
      } else if (!/\S+@\S+\.\S+/.test(form.email_address)) {
         emailError = 'Please enter a valid email address';
         isValid = false;
      }
      if (!form?.password) {
         passwordError = 'Password is required';
         isValid = false;
      } else if (form?.password?.length < 8) {
         passwordError =
            'Password must be at least 8 characters long, must have atleast one number and one special character';
         isValid = false;
      }
      setError({
         full_name: fullNameError,
         email_address: emailError,
         password: passwordError,
      });
      return isValid;
   };

   const handleNavigateToLogin = () => {
      navigate('/auth/login');
   };

   const handleSubmit = () => {
      if (!validateForm()) {
         return;
      }
      mutate(form);
   };

   return (
      <>
         <Box
            display='flex'
            justifyContent='center'
            alignItems='center'
            height='16%'
         >
            <Image src={ICON} alt='Flable Icon' h='45%' />
            <Box ml={2} fontSize='xl' fontWeight='bold'>
               {appStrings.companyName}
            </Box>
         </Box>
         <FormControl
            id='full_name'
            isInvalid={!!error.full_name}
            height='17%'
            minHeight='80px'
            maxHeight='85px'
         >
            <FormLabel ml={1} mb={0} fontSize='14px'>
               Full Name *
            </FormLabel>
            <Input
               type='string'
               name='full_name'
               fontSize='16px'
               value={form.full_name}
               onChange={handleChange}
               placeholder='Enter your name'
               variant='outline'
               height='50%'
               maxHeight='42px'
            />
            {error.full_name && (
               <FormErrorMessage ml={1} mt={1} fontSize='12px'>
                  {error.full_name}
               </FormErrorMessage>
            )}
         </FormControl>
         <FormControl
            id='email_address'
            height='17%'
            isInvalid={!!error.email_address}
            minHeight='80px'
            maxHeight='85px'
         >
            <FormLabel ml={1} mb={0} fontSize='14px'>
               Email Address *
            </FormLabel>
            <Input
               type='email'
               name='email_address'
               value={form.email_address}
               onChange={handleChange}
               placeholder='Enter your email address'
               variant='outline'
               height='50%'
               fontSize='16px'
               maxHeight='42px'
            />
            {error.email_address && (
               <FormErrorMessage ml={1} mt={1} fontSize='12px'>
                  {error.email_address}
               </FormErrorMessage>
            )}
         </FormControl>
         <FormControl
            id='password'
            isInvalid={!!error.password}
            height='17%'
            minHeight='80px'
            maxHeight='85px'
         >
            <FormLabel ml={1} mb={0} fontSize='14px'>
               Password *
            </FormLabel>
            <InputGroup height='45%'>
               <Input
                  type={showPassword ? 'text' : 'password'}
                  name='password'
                  value={form.password}
                  onChange={handleChange}
                  placeholder='Enter your password'
                  variant='outline'
                  height='100%'
                  fontSize='16px'
                  maxHeight='42px'
               />
               <InputRightElement>
                  <Button
                     h='1.75rem'
                     size='sm'
                     onClick={() => setShowPassword(!showPassword)}
                     variant='ghost'
                  >
                     {showPassword ? <FaEyeSlash /> : <FaEye />}
                  </Button>
               </InputRightElement>
            </InputGroup>
            {error.password && (
               <FormErrorMessage ml={1} mt={1} fontSize='12px'>
                  {error.password}
               </FormErrorMessage>
            )}
         </FormControl>
         <Stack direction='column' height='10%' mt={3}>
            <FormControl display='flex' gap={2} height='30%'>
               <Checkbox
                  size='md'
                  colorScheme='green'
                  name='cb_product_updates'
                  isChecked={form.cb_product_updates}
                  onChange={handleCheckboxChange}
               />
               <Text as='span' fontSize='sm'>
                  Sign me up for product updates.
               </Text>
            </FormControl>
            <FormControl display='flex' gap={2} height='30%'>
               <Checkbox
                  size='md'
                  colorScheme='green'
                  name='conditionsAndPolicy'
                  isChecked={form.conditionsAndPolicy}
                  onChange={handleCheckboxChange}
               />
               <Text as='span' fontSize='sm' lineHeight='1.2'>
                  I agree to the{' '}
                  <Link
                     isExternal
                     href='https://flable.ai/privacy-policy.html'
                     sx={{
                        display: 'inline',
                        color: '#22DBF5',
                        cursor: 'pointer',
                        '&:hover': { textDecoration: 'underline' },
                     }}
                  >
                     Privacy Policy
                  </Link>{' '}
                  and the{' '}
                  <Link
                     isExternal
                     href='https://flable.ai/Terms-sevice.html'
                     sx={{
                        display: 'inline',
                        color: '#22DBF5',
                        cursor: 'pointer',
                        '&:hover': { textDecoration: 'underline' },
                     }}
                  >
                     Terms and Conditions.
                  </Link>
               </Text>
            </FormControl>
         </Stack>
         <Button
            width='full'
            mt={2}
            colorScheme='blue'
            onClick={handleSubmit}
            height='7%'
            disabled={
               !form.full_name ||
               !form.email_address ||
               !form.password ||
               !form.conditionsAndPolicy ||
               isPending
            }
            isLoading={isPending}
         >
            Register
         </Button>
         <Text mt={5} textAlign='center' fontSize='sm'>
            Already have an account?{' '}
            <Text
               as='span'
               onClick={() => handleNavigateToLogin()}
               sx={{
                  display: 'inline',
                  color: '#22DBF5',
                  cursor: 'pointer',
                  '&:hover': { textDecoration: 'underline' },
               }}
            >
               Login
            </Text>
         </Text>
      </>
   );
}

export default Register;
