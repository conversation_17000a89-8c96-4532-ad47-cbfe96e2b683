import {
   Box,
   Button,
   Flex,
   Heading,
   useColorModeValue,
} from '@chakra-ui/react';
import { useState } from 'react';
import { FiPlus } from 'react-icons/fi';
import { MdArrowBackIosNew } from 'react-icons/md';
import AllReports from './components/all-reports';
import CreateReport from './components/create-report';
import './reports.scss';
import { MetricReport } from '../dashboard/utils/interface';
function Reports() {
   const [allReports, setallReports] = useState<boolean>(true);
   const [editReport, seteditReport] = useState<MetricReport>(
      {} as MetricReport,
   );
   return (
      <Box width={'100%'} overflow={'auto'} className='Reports'>
         {allReports ? (
            <>
               {' '}
               <Flex p={5} justifyContent={'space-between'}>
                  {' '}
                  <Heading fontWeight={'500'}>Reports</Heading>
                  <Button
                     onClick={() => setallReports(false)}
                     backgroundColor={useColorModeValue(
                        '#437EEB',
                        'var(--controls)',
                     )}
                     borderRadius={'8px'}
                     color={'white'}
                     fontWeight={'400'}
                     fontSize={'16px'}
                     _hover={{
                        backgroundColor: useColorModeValue(
                           'var(--light-hover)',
                           'var(--controls-hover)',
                        ),
                     }}
                  >
                     <FiPlus /> Create new report
                  </Button>
               </Flex>{' '}
               <AllReports
                  seteditReport={seteditReport}
                  setallReports={setallReports}
               />
            </>
         ) : (
            <>
               <Flex pt={1}>
                  {' '}
                  <Button
                     onClick={() => {
                        setallReports(true);
                        seteditReport({} as MetricReport);
                     }}
                     background={'none'}
                  >
                     <MdArrowBackIosNew style={{ marginRight: '4px' }} />
                     Back
                  </Button>{' '}
               </Flex>
               <CreateReport
                  report={editReport}
                  seteditReport={seteditReport}
                  setallReports={setallReports}
               />
            </>
         )}
      </Box>
   );
}

export default Reports;
