import { Box, Flex, Spinner } from '@chakra-ui/react';
import { useApiQuery } from '../../hooks/react-query-hooks.ts';
import { useAppDispatch } from '../../store/store.ts';

import Sidebar from './components/sidebar.tsx';
import Steps from './components/steps.tsx';
import keys from '../../utils/strings/query-keys.ts';
import onboardingEndpoints from '../../api/service/onboarding/index.ts';
import { LocalStorageService, Keys } from '../../utils/local-storage.ts';
import {
   setRegisterProgress,
   setMasterList,
   setOrganizationType,
   setAccountDetails,
} from '../../store/reducer/onboarding-reducer.ts';
import FlableLoader from '../../components/flable-loader/flable-loader.tsx';
import { useNavigate } from 'react-router-dom';

const Onboarding = () => {
   const dispatch = useAppDispatch();
   const navigate = useNavigate();

   const {
      isLoading: isUserDetailsLoading,
      isFetching: isUserDetailsFetching,
   } = useApiQuery({
      queryKey: [keys.fetchUserDetails],
      queryFn: () =>
         onboardingEndpoints.getUserDetails({
            email_address: LocalStorageService.getItem(Keys.UserName) as string,
         }),
      selectHandler: (data) => {
         const { userDetails, accountDetails } = data.details;

         if (userDetails.register_progress.trim() === 'Completed') {
            navigate('/auth/choose-profile');
         } else {
            dispatch(setRegisterProgress(userDetails.register_progress.trim()));

            if (accountDetails && accountDetails.length > 0) {
               dispatch(setAccountDetails(accountDetails));

               dispatch(
                  setOrganizationType(
                     (accountDetails[0].organization_type as
                        | 'Individual Business'
                        | 'Marketing Agency') || 'Individual Business',
                  ),
               );

               if (userDetails.register_progress.trim() === 'Step 2') {
                  LocalStorageService.removeItem(Keys.FlableUserDetails);
                  LocalStorageService.removeItem(Keys.ClientId);
               } else {
                  const currentUser = accountDetails.filter(
                     (item) => item.register_progress !== 'Completed',
                  )[0];

                  const userDetails = {
                     email: LocalStorageService.getItem(
                        Keys.UserName,
                     ) as string,
                     client_id: currentUser.client_id,
                  };

                  LocalStorageService.setItem(
                     Keys.FlableUserDetails,
                     userDetails,
                  );
                  LocalStorageService.setItem(
                     Keys.ClientId,
                     currentUser.client_id,
                  );
               }
            }
         }

         return data;
      },
      enabled: true,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      refetchOnMount: false,
   });

   const { isLoading: isUserMasterListLoading } = useApiQuery({
      queryKey: [keys.userMasterList],
      queryFn: () => onboardingEndpoints.fetchUserMasterList(),
      selectHandler: (data) => {
         dispatch(setMasterList(data.list));
         return data;
      },
      enabled: true,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      refetchOnMount: false,
   });

   if (isUserDetailsFetching) {
      return (
         <Flex
            width='100%'
            height='100vh'
            justifyContent='center'
            alignItems='center'
         >
            <Spinner size='xl' />
         </Flex>
      );
   }

   return (
      <Box
         display='flex'
         justifyContent='start'
         alignItems='center'
         height='100vh'
      >
         {isUserDetailsLoading || isUserMasterListLoading ? (
            <Box>
               <FlableLoader />
            </Box>
         ) : (
            <>
               <Steps />
               <Sidebar />
            </>
         )}
      </Box>
   );
};

export default Onboarding;
