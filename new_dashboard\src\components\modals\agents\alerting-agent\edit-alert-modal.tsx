import { InputGroup, InputRightAddon, Select, Spinner } from '@chakra-ui/react';
import ModalWrapper from '../../modal-wrapper';
import {
   AlertCampaign,
   AlertingAgentChat,
   sendAlertPrompt,
   SendAlertPromptPayload,
   SendAlertResponse,
} from '../../../../api/service/agentic-workflow/alerting-agent';
import { useEffect, useState } from 'react';
import { Keys, LocalStorageService } from '../../../../utils/local-storage';
import { AuthUser } from '../../../../types/auth';
import {
   COMPARATORS,
   DATA_SOURCE,
   KPIS,
   REFERENCE,
   sortReferenceByValue,
} from '../../../../pages/marco/utils/alerting-agent/constants';
import {
   getValue,
   splitAndUppercaseString,
} from '../../../../pages/marco/utils/alerting-agent/agents-helpers';
import { VALID_EMAILS_PATTERN } from '../../../../utils/strings/settings-strings';
import { closeModal } from '../../../../store/reducer/modal-reducer';
import { useAppDispatch, useAppSelector } from '../../../../store/store';
import {
   useAddChatToSessionHistoryMutation,
   useCreateAlertMutation,
   useFetchAlertByIdQuery,
   useFetchAlertsBySessionIdQuery,
   useFetchAllAlertsQuery,
   useFetchHistoryBySessionIdQuery,
   usePauseUnpauseAlertMutation,
   useUpdateAlertMutation,
} from '@/pages/marco/apis/alerting-agent-apis';
import {
   PiCheck,
   PiCheckBold,
   PiInfoBold,
   PiPencilSimple,
   PiWarningCircleBold,
   PiX,
} from 'react-icons/pi';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { MultiSelect } from '@/components/ui/multi-select';

interface Form {
   alert_name: string;
   alert_description: string;
   alert_instruction: string;
   new_recipient: string;
   recipients: { email: string; valid: boolean }[];
   channel: string;
   campaigns: AlertCampaign[];
   kpi: string;
   trend: string;
   value: string;
   value_type: string;
   comparison: string;
   comparison_type: string;
   alert_time: string;
   alert_timeframe: string;
}

interface EditAlertModalProps {
   alert_setup_status: 'success' | 'missing';
   alert_id: string;
   alert_name: string;
   alert_description: string;
   currentChat: AlertingAgentChat;
   meta_data: SendAlertResponse;
}

interface Reference {
   key: string;
   value: string;
   title: string;
}

interface EditAlertName {
   alert_name: string;
   editMode: boolean;
   editConfirmed: boolean;
}

const EditAlertModal = () => {
   const dispatch = useAppDispatch();

   const { client_id, email, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) ?? {};

   const { currentAlertID } = useAppSelector((state) => state.alertingAgent);
   const currentModal = useAppSelector((state) => state.modal);
   const { generalSettings } = useAppSelector((state) => state.settings);

   const { alert_setup_status, meta_data, currentChat, alert_id } = currentModal
      .payload?.modalProps as EditAlertModalProps;

   const [updatedReference, setUpdatedReference] = useState<Reference[]>([]);
   const [form, setForm] = useState<Form>({
      alert_name: '',
      alert_description: '',
      alert_instruction: '',
      new_recipient: '',
      recipients: [],
      channel: '',
      campaigns: [],
      kpi: '',
      trend: '',
      value: '',
      value_type: 'number',
      comparison: '',
      comparison_type: '',
      alert_time: '09:00:00',
      alert_timeframe: '',
   });
   const [editAlertName, setEditAlertName] = useState<EditAlertName>({
      alert_name: '',
      editMode: false,
      editConfirmed: false,
   });

   const {
      data: alert,
      isFetching: isAlertFetching,
      refetch: refetchAlert,
   } = useFetchAlertByIdQuery();

   const { mutateAsync: createAlert } = useCreateAlertMutation();

   const { refetch: refetchSessionHistory } = useFetchHistoryBySessionIdQuery();

   const { refetch: refetchSessionAlerts } = useFetchAlertsBySessionIdQuery();

   const { refetch: refetchAllAlerts } = useFetchAllAlertsQuery();

   const { mutateAsync: addToSessionHistory } =
      useAddChatToSessionHistoryMutation();

   const { mutateAsync: pauseUnpauseAlert } = usePauseUnpauseAlertMutation();

   const { mutateAsync: updateAlert } = useUpdateAlertMutation();

   const handleAddRecipient = () => {
      if (!form.new_recipient) {
         toast.error('Please enter a valid email address');
         return;
      }

      const result = VALID_EMAILS_PATTERN.test(form.new_recipient);

      if (!result) {
         toast.error('Please enter a valid email address');
      }

      const emailExists = form.recipients.some(
         (recipient) => recipient.email === form.new_recipient,
      );

      if (emailExists) {
         toast.info('Email already added');

         setForm((prevForm) => ({
            ...prevForm,
            new_recipient: '',
         }));

         return;
      }

      setForm((prevForm) => ({
         ...prevForm,
         recipients: [
            ...prevForm.recipients,
            { email: form.new_recipient, valid: result },
         ],
         new_recipient: '',
      }));
   };

   const handleDeleteRecipient = (idx: number) => {
      setForm((prevForm) => {
         const newData = [...prevForm.recipients];
         newData.splice(idx, 1);
         return { ...prevForm, recipients: newData };
      });
   };

   const handleEditAlertName = () => {
      setEditAlertName((prev) => ({
         ...prev,
         editMode: true,
         alert_name: form.alert_name,
         editConfirmed: prev.editConfirmed ? true : false,
      }));
   };

   const handleCancelAlertName = () => {
      setForm((prevForm) => ({
         ...prevForm,
         alert_name: editAlertName.alert_name,
      }));

      setEditAlertName((prev) => ({
         ...prev,
         editConfirmed: prev.editConfirmed ? true : false,
         editMode: false,
         alert_name: '',
      }));
   };

   const handleConfirmAlertName = () => {
      if (!form.alert_name) {
         toast.error('Please enter a valid alert name');
         return;
      }

      setEditAlertName((prev) => ({
         ...prev,
         editConfirmed: true,
         editMode: false,
         alert_name: '',
      }));
   };

   const removeLastRecipient = () => {
      if (form.recipients.length === 0) {
         return;
      }

      setForm((prevForm) => ({
         ...prevForm,
         recipients: prevForm.recipients.slice(0, -1),
      }));
   };

   const focusToInput = () => {
      const inputElement = document.getElementById('new_recipient');
      if (inputElement) {
         inputElement.focus();
      }
   };

   const handleSubmit = async () => {
      const allRecipients = form.recipients.map((recipient) => recipient.email);

      if (
         !form.alert_name ||
         !form.channel ||
         !form.kpi ||
         !form.trend ||
         !form.value ||
         (form.value_type === 'percentage' && !form.comparison) ||
         (!form.recipients.length && !form.new_recipient)
      ) {
         toast.error('Please fill in all the required fields');
         return;
      }

      if (
         form.recipients.length > 0 &&
         form.recipients.some((recipient) => !recipient.valid)
      ) {
         toast.error('Please enter a valid email address');
         return;
      }

      if (form.new_recipient) {
         const result = VALID_EMAILS_PATTERN.test(form.new_recipient);

         if (!result) {
            toast.error('Please enter a valid email address');

            setForm((prevForm) => ({
               ...prevForm,
               recipients: [
                  ...prevForm.recipients,
                  { email: form.new_recipient, valid: result },
               ],
               new_recipient: '',
            }));
            return;
         }

         const emailExists = form.recipients.some(
            (recipient) => recipient.email === form.new_recipient,
         );

         if (!emailExists) {
            allRecipients.push(form.new_recipient);
         }
      }

      const data = {
         channel: form.channel,
         campaign: form.campaigns,
         kpi: form.kpi,
         trend: form.trend,
         value: form.value,
         comparison: form.comparison,
      };

      const agentPayload: SendAlertPromptPayload = {
         client_id: client_id!,
         chat: JSON.stringify(data),
         mode: 'json',
      };

      const agentResponse = await sendAlertPrompt(agentPayload);

      const alertPayload = {
         client_id: client_id || '',
         user_id: user_id || '',
         session_id: currentChat.session_id || '',
         chat_id: currentChat.chat_id || '',
         alert_name: editAlertName.editConfirmed
            ? form.alert_name
            : agentResponse?.data?.title || 'SAMPLE',
         alert_description: agentResponse?.data?.description || 'SAMPLE',
         alert_instruction: form.alert_instruction,
         recipients: allRecipients.join(','),
         channel: form.channel,
         campaigns: form.campaigns,
         kpi: form.kpi,
         trend: form.trend,
         value: form.value,
         value_type: form.value_type,
         comparison: form.comparison,
         comparison_type: form.comparison_type || 'time',
         alert_status: alert?.alert_status || 'ACTIVE',
         alert_time: form.alert_time,
         alert_timeframe: form.alert_timeframe,
         user_timezone: generalSettings.timezone_name || 'UTC',
         emails_triggered: alert?.emails_triggered || 0,
         timestamps_when_triggered: alert?.timestamps_when_triggered || '',
      };

      if (currentAlertID) {
         await updateAlert({
            ...alertPayload,
            alert_id: String(alert_id),
         });
      } else {
         const alertSaveResponse = await createAlert(alertPayload);

         const updatedSessionHistoryPayload = {
            client_id: String(client_id),
            user_id: String(user_id),
            session_id: String(currentChat.session_id),
            chat_id: String(currentChat.chat_id),
            user_query: currentChat.user_query,
            response_status: 'success' as 'success' | 'error' | 'pending',
            final_response:
               JSON.stringify({
                  alert_id: alertSaveResponse.alert_id,
                  alert_name: alertPayload.alert_name,
                  alert_description: alertPayload.alert_description,
                  options: meta_data?.options || {},
                  alert_setup_status: 'success',
                  meta_data: {
                     ...agentResponse,
                     options: meta_data?.options || {},
                  },
               }) || '',
            session_summary: currentChat.session_summary,
            response_time: currentChat.response_time,
         };

         await addToSessionHistory(updatedSessionHistoryPayload);
      }

      await refetchAlert();
      await refetchAllAlerts();
      await refetchSessionHistory();
      await refetchSessionAlerts();
      dispatch(closeModal());
   };

   const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === ',') {
         e.preventDefault();
         handleAddRecipient();
      }

      if (e.key === 'Backspace' && !form.new_recipient) {
         e.preventDefault();
         removeLastRecipient();
      }
   };

   const handleStatusClick = async () => {
      await pauseUnpauseAlert({
         client_id: client_id || '',
         user_id: user_id || '',
         alert_id: alert_id,
      });

      await refetchAlert();
      await refetchAllAlerts();
      await refetchSessionAlerts();
   };

   const handleChange = (
      e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
   ) => {
      const { name, value } = e.target;

      setForm((prevForm) => {
         switch (name) {
            case 'alert_name':
            case 'alert_instruction':
            case 'kpi':
            case 'new_recipient':
            case 'value':
            case 'alert_time':
               return { ...prevForm, [name]: value };

            case 'comparison': {
               const newTimeframe = value
                  ? parseInt(value.match(/\d+/)?.[0] ?? '', 10)
                  : 1;
               return {
                  ...prevForm,
                  [name]: value,
                  alert_timeframe: String(newTimeframe),
               };
            }

            case 'channel':
               return {
                  ...prevForm,
                  campaigns: [],
                  kpi: '',
                  [name]: value,
               };

            case 'trend':
               return {
                  ...prevForm,
                  [name]: value,
                  value_type: ['More_than', 'Less_than'].includes(value)
                     ? 'number'
                     : 'percentage',
               };

            default:
               return prevForm;
         }
      });
   };

   const handleCampaignChange = (value: string[]) => {
      const campaigns = meta_data?.options?.campaigns?.[
         form.channel as keyof typeof meta_data.options.campaigns
      ]?.filter((c: { id: string; name: string }) => value.includes(c.id));

      setForm((prevForm) => ({
         ...prevForm,
         campaigns: campaigns || [],
      }));
   };

   useEffect(() => {
      if (currentAlertID) {
         void refetchAlert();
      }
   }, [currentAlertID, refetchAlert]);

   useEffect(() => {
      if (
         alert_setup_status === 'success' &&
         alert_id === currentAlertID &&
         alert
      ) {
         setForm({
            alert_name: alert.alert_name,
            alert_description: alert.alert_description,
            alert_instruction: alert.alert_instruction,
            new_recipient: '',
            recipients: alert.recipients
               ? alert.recipients
                    .split(',')
                    .map((email) => ({ email: email, valid: true }))
               : [],
            channel: alert.channel,
            campaigns: alert.campaigns || [],
            kpi: alert.kpi,
            trend: alert.trend,
            value: alert.value,
            value_type: alert.value_type,
            comparison: alert.comparison,
            comparison_type: alert.comparison_type,
            alert_time: alert.alert_time,
            alert_timeframe: alert.alert_timeframe,
         });
      } else {
         setForm({
            alert_name: meta_data?.data?.title ?? '',
            alert_description: meta_data?.data?.description ?? '',
            alert_instruction: meta_data?.data?.instruction ?? '',
            new_recipient: '',
            recipients: [
               {
                  email: email ?? '',
                  valid: true,
               },
            ],
            channel: getValue(meta_data?.data?.channel ?? ''),
            campaigns: Array.isArray(meta_data?.data?.campaign)
               ? meta_data.data.campaign
               : [],
            kpi: getValue(meta_data?.data?.kpi ?? ''),
            trend: getValue(meta_data?.data?.trend ?? ''),
            value: getValue(meta_data?.data?.value ?? ''),
            value_type:
               getValue(meta_data?.data?.value_type || '') ||
               (meta_data?.missing_fields?.includes('comparison')
                  ? 'percentage'
                  : 'number'),
            comparison: getValue(meta_data?.data?.comparison ?? ''),
            comparison_type: getValue(
               meta_data?.data?.comparison_type ?? '',
               'time',
            ),
            alert_time: '09:00:00',
            alert_timeframe: getValue(meta_data?.options?.time ?? ''),
         });
      }
   }, [alert]);

   useEffect(() => {
      const referenceExists = REFERENCE.find(
         (item) => item.value === meta_data?.options?.time,
      );

      setUpdatedReference(
         !referenceExists
            ? sortReferenceByValue([
                 {
                    value: meta_data?.options?.time ?? '',
                    key: meta_data.data.comparison ?? '',
                    title: splitAndUppercaseString(
                       meta_data.data.comparison ?? '',
                    ),
                 },
                 ...REFERENCE,
              ])
            : sortReferenceByValue(REFERENCE),
      );
   }, [meta_data?.missing_fields]);

   return (
      <>
         <ModalWrapper
            size='2xl'
            heading={
               alert_setup_status === 'success' ? 'Edit Alert' : 'Setup Alert'
            }
         >
            {isAlertFetching ? (
               <div className='flex flex-col justify-center items-center h-[500px]'>
                  <Spinner size='xl' />
               </div>
            ) : (
               <div className='flex flex-col justify-center items-center gap-4'>
                  <div className='w-full flex items-center justify-center gap-1'>
                     <PiInfoBold size='16px' />
                     <p className='text-[14px]'>
                        <b>Please note:</b> Alert will be generated based on
                        data from the previous day.
                     </p>
                  </div>
                  {alert && alert.alert_status === 'PAUSED' && (
                     <div className='w-full flex justify-center items-center gap-4 border border-[#e2e8f0] rounded-md p-2'>
                        <p className='text-[14px]'>
                           This alert has been paused. Would you like to resume
                           it?
                        </p>
                        <Button
                           size='sm'
                           className='bg-green-700 text-white hover:cursor-pointer hover:bg-green-800'
                           onClick={() => void handleStatusClick()}
                        >
                           RESUME
                        </Button>
                     </div>
                  )}
                  <div className='w-full flex items-end justify-between gap-2'>
                     <div className='w-full flex flex-col gap-1 cursor-not-allowed'>
                        <Label
                           htmlFor='alert_name'
                           className='text-[14px] ml-1'
                        >
                           Alert Name
                        </Label>
                        <Input
                           className={`h-[40px] ${!form.alert_name ? 'border-red-500' : 'border-[#e2e8f0]'}`}
                           id='alert_name'
                           type='text'
                           name='alert_name'
                           value={form.alert_name}
                           onChange={handleChange}
                           disabled={!editAlertName.editMode}
                        />
                     </div>
                     {editAlertName.editMode ? (
                        <div className='flex gap-1'>
                           <Button
                              className='cursor-pointer bg-gray-400 hover:bg-gray-500'
                              onClick={handleConfirmAlertName}
                           >
                              <PiCheck />
                           </Button>
                           <Button
                              className='cursor-pointer bg-gray-400 hover:bg-gray-500'
                              onClick={handleCancelAlertName}
                           >
                              <PiX />
                           </Button>
                        </div>
                     ) : (
                        <Button
                           className='cursor-pointer bg-gray-400 hover:bg-gray-500'
                           onClick={handleEditAlertName}
                        >
                           <PiPencilSimple />
                        </Button>
                     )}
                  </div>
                  {form.alert_instruction && (
                     <div className='w-full flex justify-between items-end gap-2'>
                        <div className='w-full flex flex-col gap-1 cursor-not-allowed'>
                           <Label
                              className='text-[14px] font-semibold ml-1'
                              htmlFor='alert_instruction'
                           >
                              Instructions
                           </Label>
                           <Input
                              type='text'
                              name='alert_instruction'
                              value={form.alert_instruction}
                              disabled
                              className={`h-[40px] ${!form.alert_instruction ? 'border-red-500' : 'border-[#e2e8f0]'}`}
                              onChange={handleChange}
                           />
                        </div>
                        <div className='w-[30%]'>
                           <div className='w-full flex flex-col gap-1'>
                              <Label
                                 htmlFor='alert_time'
                                 className='text-[14px] font-semibold ml-1'
                              >
                                 Alert Time
                              </Label>
                              <Input
                                 className='h-[40px]'
                                 type='time'
                                 name='alert_time'
                                 value={form.alert_time}
                                 onChange={handleChange}
                              />
                           </div>
                        </div>
                     </div>
                  )}
                  <div className='w-full'>
                     <div className='w-full flex flex-col gap-1'>
                        <Label
                           htmlFor='email_recipients'
                           className='text-[14px] font-semibold ml-1'
                        >
                           Email Recipients
                        </Label>
                        <div
                           onClick={focusToInput}
                           className={`w-full hover:cursor-text flex items-center flex-wrap gap-2 rounded-[5px] p-1 border ${form.recipients.length === 0 && !form.new_recipient ? '!border-red-500' : 'border-[#e2e8f0]'}`}
                        >
                           {form.recipients.length > 0 &&
                              form.recipients?.map((recipient, index) => (
                                 <Badge
                                    className={`text-[12px] text-white px-2 py-0 ${recipient.valid ? 'bg-green-700' : 'bg-red-600'} font-semibold rounded-full`}
                                 >
                                    <div className='flex items-center gap-1'>
                                       {recipient.valid ? (
                                          <PiCheckBold />
                                       ) : (
                                          <PiWarningCircleBold size={18} />
                                       )}
                                       {recipient.email}
                                    </div>
                                    <Button
                                       variant='ghost'
                                       className={`${recipient.valid ? 'hover:bg-green-700' : 'hover:bg-red-600'} hover:text-gray-300 hover:cursor-pointer w-[22px] text-[12px] p-0 h-[22px]`}
                                       onClick={() =>
                                          handleDeleteRecipient(index)
                                       }
                                    >
                                       <PiX />
                                    </Button>
                                 </Badge>
                              ))}
                           <Input
                              className='w-full ml-0 px-2 max-w-fit border-0 shadow-none focus-visible:border-0 focus-visible:ring-0 focus-visible:outline-none'
                              autoComplete='off'
                              width='100%'
                              type='text'
                              id='new_recipient'
                              name='new_recipient'
                              value={form.new_recipient}
                              onKeyDown={handleKeyDown}
                              onChange={handleChange}
                           />
                        </div>
                     </div>
                  </div>
                  <div className='w-full flex items-end justify-between gap-2'>
                     <div className='w-full min-w-[33%] max-w-[50%] flex flex-col gap-2'>
                        <p className='text-[14px] font-semibold ml-1'>
                           Channel
                        </p>
                        <Select
                           value={form.channel}
                           border={
                              !form.channel
                                 ? '1px solid #ef4444'
                                 : '1px solid #e2e8f0'
                           }
                           onChange={handleChange}
                           name='channel'
                        >
                           <option hidden></option>
                           {meta_data?.options?.channels?.map((channel) => (
                              <option key={channel} value={channel}>
                                 {
                                    DATA_SOURCE[
                                       channel as keyof typeof DATA_SOURCE
                                    ]
                                 }
                              </option>
                           ))}
                        </Select>
                     </div>
                     {meta_data?.options?.campaigns &&
                        meta_data?.options?.campaigns?.[
                           form.channel as keyof typeof meta_data.options.campaigns
                        ]?.length > 0 && (
                           <div className='flex flex-col min-w-[33%] gap-2'>
                              <p className='text-[14px] font-semibold ml-1'>
                                 Campaign
                              </p>
                              <MultiSelect
                                 options={meta_data?.options?.campaigns?.[
                                    form.channel as keyof typeof meta_data.options.campaigns
                                 ].map((campaign) => ({
                                    key: campaign.id,
                                    label: campaign.name,
                                    value: campaign.id,
                                    ...campaign,
                                 }))}
                                 values={form.campaigns.map((c) => c.id)}
                                 onValueChange={handleCampaignChange}
                                 placeholder='Select Campaigns'
                                 variant='inverted'
                                 maxCount={0}
                              />
                           </div>
                        )}
                     <div className='flex flex-col w-full min-w-[33%] max-w-[50%] gap-2'>
                        <p className='text-[14px] font-semibold ml-1'>KPI</p>

                        <Select
                           value={form.kpi}
                           border={
                              !form.kpi
                                 ? '1px solid #ef4444'
                                 : '1px solid #e2e8f0'
                           }
                           onChange={handleChange}
                           name='kpi'
                        >
                           <option hidden></option>
                           {(() => {
                              const kpiKey =
                                 form.campaigns.length > 0
                                    ? 'pulse_kpis'
                                    : 'dashboard_kpis';
                              const kpiOptions =
                                 meta_data?.options?.[kpiKey]?.[
                                    form.channel as keyof (typeof meta_data.options)[typeof kpiKey]
                                 ] ?? [];

                              return [...kpiOptions]
                                 .sort()
                                 .map((kpi: string) => (
                                    <option key={kpi} value={kpi}>
                                       {splitAndUppercaseString(kpi)}
                                    </option>
                                 ));
                           })()}
                        </Select>
                     </div>
                  </div>
                  <div className='w-full flex items-end justify-between gap-2'>
                     <div className='w-full min-w-[33%] max-w-[50%] flex flex-col gap-2'>
                        <p className='text-[14px] font-semibold ml-1'>Trend</p>
                        <Select
                           value={form.trend}
                           border={
                              !form.trend
                                 ? '1px solid #ef4444'
                                 : '1px solid #e2e8f0'
                           }
                           onChange={handleChange}
                           name='trend'
                        >
                           <option hidden></option>
                           {COMPARATORS?.map((comparator) => (
                              <option
                                 key={comparator.key}
                                 value={comparator.key}
                              >
                                 {comparator.title}
                              </option>
                           ))}
                        </Select>
                     </div>
                     <div className='flex flex-col w-full min-w-[33%] max-w-[50%] gap-2'>
                        <Label className='text-[14px] font-semibold ml-1'>
                           By
                        </Label>
                        {form.value_type === 'percentage' ? (
                           <InputGroup>
                              <Input
                                 className={`h-[40px] ${!form.value ? '!border-red-500' : 'border-[#e2e8f0]'} rounded-r-none shadow-none focus-visible:ring-0 focus-visible:outline-none`}
                                 type='number'
                                 value={form.value}
                                 onChange={handleChange}
                                 name='value'
                              />
                              <InputRightAddon>%</InputRightAddon>
                           </InputGroup>
                        ) : (
                           <Input
                              type='number'
                              value={form.value}
                              onChange={handleChange}
                              name='value'
                              className={`h-[40px] ${!form.value ? '!border-red-500' : 'border-[#e2e8f0]'} shadow-none focus-visible:ring-0 focus-visible:outline-none`}
                           />
                        )}
                     </div>
                     {form.value_type === 'percentage' && (
                        <div className='w-full min-w-[33%] max-w-[50%]'>
                           <Label className='text-[14px] font-semibold ml-1'>
                              Against
                           </Label>
                           <Select
                              value={form.comparison}
                              onChange={handleChange}
                              border={
                                 form.value_type === 'percentage' &&
                                 !form.comparison
                                    ? '1px solid #ef4444'
                                    : '1px solid #e2e8f0'
                              }
                              name='comparison'
                           >
                              <option hidden></option>
                              {form?.comparison_type === 'kpi'
                                 ? KPIS[form.channel as keyof typeof KPIS]
                                      ?.sort()
                                      ?.map((kpi) => (
                                         <option key={kpi} value={kpi}>
                                            {splitAndUppercaseString(kpi)}
                                         </option>
                                      ))
                                 : updatedReference?.map((reference) => (
                                      <option
                                         key={reference.key}
                                         value={reference.key}
                                      >
                                         {reference.title}
                                      </option>
                                   ))}
                           </Select>
                        </div>
                     )}
                  </div>
                  <div className='w-full flex justify-between px-[10px] py-[20px]'>
                     <div className='flex gap={5}'>
                        {alert && alert.alert_status === 'ACTIVE' && (
                           <Button
                              className='hover:cursor-pointer bg-[#3182ce] hover:bg-[#2b6cb0]'
                              onClick={() => void handleStatusClick()}
                           >
                              PAUSE
                           </Button>
                        )}
                     </div>
                     <div className='flex gap-2'>
                        <Button
                           className='hover:cursor-pointer'
                           variant='outline'
                           onClick={() => dispatch(closeModal())}
                        >
                           Cancel
                        </Button>
                        <Button
                           className='hover:cursor-pointer bg-[#3182ce] hover:bg-[#2b6cb0]'
                           onClick={() => void handleSubmit()}
                        >
                           Submit
                        </Button>
                     </div>
                  </div>
               </div>
            )}
         </ModalWrapper>
      </>
   );
};

export default EditAlertModal;
