import {
   useMutation,
   useQuery,
   useQueryClient,
   InitialDataFunction,
} from '@tanstack/react-query';
import { AxiosError, AxiosResponse } from 'axios';

/* eslint-disable @typescript-eslint/no-explicit-any */
export interface CustomErrorResponse {
   message: string;
   status: number;
   statusText: string;
   headers: Record<string, string>;
   config: any;
}
type ApiQueryFunction<TData> = () => Promise<AxiosResponse<TData>>;
type ApiMutationFunction<TData, TPayload> = (
   payload: TPayload,
) => Promise<AxiosResponse<TData>>;
const useApiQuery = <TData>({
   queryFn,
   queryKey,
   enabled,
   getInitialData,
   selectHandler,
   staleTime,
   gcTime,
   refetchOnWindowFocus,
   refetchOnReconnect,
   refetchOnMount,
}: {
   queryKey: string[];
   queryFn: ApiQueryFunction<TData>;
   enabled?: boolean;
   getInitialData?: InitialDataFunction<TData>;
   selectHandler?: (data: TData) => TData;
   staleTime?: number;
   gcTime?: number;
   refetchOnWindowFocus?: boolean;
   refetchOnReconnect?: boolean;
   refetchOnMount?: boolean | 'always';
}) => {
   const fetchData = async (): Promise<TData> => {
      const response = await queryFn();
      return response.data;
   };

   let errorMessage: string | null = null;

   const { data, error, isLoading, isFetching, isError, isSuccess, refetch } =
      useQuery<TData, AxiosError<CustomErrorResponse>>({
         queryKey,
         queryFn: fetchData,
         enabled,
         initialData: getInitialData,
         select: (data: TData) => {
            selectHandler?.(data);
            return data;
         },
         gcTime: gcTime,
         staleTime: staleTime,
         refetchOnWindowFocus: refetchOnWindowFocus,
         refetchOnReconnect: refetchOnReconnect,
         refetchOnMount: refetchOnMount,
      });

   if (isError) {
      errorMessage = error.response?.data.message || null;
   }

   return {
      data,
      isLoading,
      isFetching,
      errorMessage,
      isSuccess,
      refetch,
   };
};

const useApiMutation = <TData, TPayload>({
   queryKey,
   mutationFn,
   onSuccessHandler,
   onError,
   invalidateCacheQuery,
}: {
   queryKey?: string[];
   mutationFn: ApiMutationFunction<TData, TPayload>;
   onSuccessHandler?: (data: TData, payload?: TPayload) => void | Promise<void>;
   onError?: (msg: string | null) => void;
   invalidateCacheQuery?: string[];
}) => {
   const queryClient = useQueryClient();

   const fetchData = async (payload: TPayload): Promise<TData> => {
      const response = await mutationFn(payload);
      return response.data;
   };

   let errorMessage: string | null = null;

   const { mutate, mutateAsync, isPending, isError, error, data, variables } =
      useMutation<TData, AxiosError<CustomErrorResponse>, TPayload>({
         mutationFn: fetchData,
         mutationKey: queryKey,
         onSuccess: (props) => {
            void onSuccessHandler?.(props, variables);
            if (invalidateCacheQuery && invalidateCacheQuery.length > 0) {
               void queryClient.invalidateQueries({
                  queryKey: invalidateCacheQuery,
               });
            }
         },
         onError: (err) => {
            if (err) {
               onError?.(err.response?.data.message || null);
            }
         },
      });
   if (isError) {
      errorMessage = error.response?.data.message || null;
   }

   return {
      data,
      mutate,
      mutateAsync,
      isPending,
      errorMessage,
   };
};

export { useApiQuery, useApiMutation };
