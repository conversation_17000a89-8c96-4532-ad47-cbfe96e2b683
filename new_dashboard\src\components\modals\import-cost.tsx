import {
   Button,
   Flex,
   Input,
   Table,
   TableContainer,
   Tbody,
   Td,
   Text,
   Th,
   Thead,
   Tr,
   useToast,
} from '@chakra-ui/react';
import ModalWrapper from './modal-wrapper';
import './import-cost.scss';
import { ChangeEvent, useEffect, useRef, useState } from 'react';
import * as XLSX from 'xlsx';
import { FILE_EXTENSION, FILE_TYPE, HEADING } from '../../utils/strings/cfo';
import { useApiMutation, useApiQuery } from '../../hooks/react-query-hooks';
import { CFOKeys } from '../../pages/dashboard/utils/query-keys';
import endPoints, {
   OutNewId,
   ShippingCostByOrderId,
} from '../../api/service/cfo';
import { Keys, LocalStorageService } from '../../utils/local-storage';

function ImportCostModal() {
   const fileInput = useRef<HTMLInputElement>(null);
   const [fileData, setfileData] = useState<ShippingCostByOrderId[]>([]);
   const toast = useToast();
   useEffect(() => {
      getShippingCostsByOrderId()
         .then((data) => {
            if (data.data) {
               setfileData(
                  data.data.map((d) => {
                     return { Order_Id: d.order_id, Shipping_Cost: d.cost };
                  }),
               );
            }
         })
         .catch(console.log);
   }, []);
   const { refetch: getShippingCostsByOrderId } = useApiQuery({
      queryKey: [CFOKeys.getShippingCostsByOrderId],
      queryFn: () =>
         endPoints.getShippingCostByOrderId(
            LocalStorageService.getItem(Keys.ClientId) as string,
         ),
      enabled: false,
   });
   const handleUpload = (event: ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files as FileList;
      if (!files || files.length == 0 || files.length > 1) {
         toast({
            title: 'Error',
            description: 'Please select one file',
            status: 'error',
            duration: 2000,
            isClosable: true,
         });
         return;
      } else if (
         !FILE_EXTENSION.includes(files[0].name.split('.')[1]) ||
         !FILE_TYPE.includes(files[0].type)
      ) {
         toast({
            title: 'Error',
            description: 'Invalid file format',
            status: 'error',
            duration: 2000,
            isClosable: true,
         });
         return;
      }
      const fileReader = new FileReader();
      fileReader.readAsArrayBuffer(files[0]);
      fileReader.onload = (e) => {
         if (!e.target) return;
         const bufferArray = e.target.result;
         const wb = XLSX.read(bufferArray, { type: 'buffer' });
         const wsname = wb.SheetNames[0];
         const ws = wb.Sheets[wsname];
         const data: ShippingCostByOrderId[] = XLSX.utils.sheet_to_json(ws);
         const isvalidColumns = data.every(
            (d) => 'Order_Id' in d && 'Shipping_Cost' in d,
         );
         const isNumber = data.every(
            (d) =>
               typeof d.Order_Id === 'number' &&
               typeof d.Shipping_Cost === 'number',
         );
         if (!isvalidColumns) {
            toast({
               title: 'Error',
               description:
                  'CSV should contain Order_Id and Shipping_Cost columns',
               status: 'error',
               duration: 2000,
               isClosable: true,
            });
            return;
         } else if (!isNumber) {
            toast({
               title: 'Error',
               description: 'Order_Id and Shipping_Cost should be numbers',
               status: 'error',
               duration: 2000,
               isClosable: true,
            });
            return;
         }
         const finalData = data.map((d) => {
            return {
               Order_Id: d.Order_Id,
               Shipping_Cost: d.Shipping_Cost,
               clientId: LocalStorageService.getItem(Keys.ClientId) as string,
            };
         });
         updateShippingCosts(finalData);
      };

      fileReader.onerror = (error) => {
         console.log('error', error);
         toast({
            title: 'Error',
            description: 'Failed to read file',
            status: 'error',
            duration: 2000,
            isClosable: true,
         });
      };
   };
   const handleSuccess = (
      data: OutNewId[],
      payload?: ShippingCostByOrderId[],
   ) => {
      if (data[0].new_id) {
         toast({
            title: 'Success',
            description: 'Shipping Costs Imported Successfully',
            status: 'success',
            duration: 2000,
            isClosable: true,
         });
         setfileData(payload || []);
      }
   };
   const { mutate: updateShippingCosts } = useApiMutation({
      queryKey: [CFOKeys.upsertShippingCostByOrderId],
      mutationFn: endPoints.upsertShippingCostByOrderId,
      onSuccessHandler: handleSuccess,
   });
   const exportToCSV = (csvData: ShippingCostByOrderId[], fileName: string) => {
      //Had to create a new workbook and then add the header
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet([]);
      XLSX.utils.sheet_add_aoa(ws, HEADING);

      //Starting in the second row to avoid overriding and skipping headers
      XLSX.utils.sheet_add_json(
         ws,
         csvData.map((d) => {
            return { Order_Id: d.Order_Id, Shipping_Cost: d.Shipping_Cost };
         }),
         { origin: 'A2', skipHeader: true },
      );

      XLSX.utils.book_append_sheet(wb, ws, 'data');
      XLSX.writeFile(wb, `${fileName}.${FILE_EXTENSION[0]}`, {
         bookType: 'csv',
         type: 'array',
      });
   };
   return (
      <ModalWrapper
         parentClassName='import-cost'
         heading='Import Shipping Costs By Order ID CSV'
      >
         {' '}
         <Flex
            direction={'column'}
            gap={3}
            pt={4}
            borderTop={'1px solid #E0E0E0'}
         >
            <Flex direction={'column'} gap={2}>
               <Text>1. Download Template</Text>
               <Button
                  width={'fit-content'}
                  color={'white'}
                  _hover={{
                     backgroundColor: '#437EEBBB',
                  }}
                  onClick={() =>
                     exportToCSV(fileData, 'Shipping_Costs_By_Order_ID')
                  }
                  ml={5}
                  backgroundColor={'#437EEB'}
                  py={1}
                  fontSize={'15px'}
                  fontWeight={'400'}
                  px={2}
                  border={'1px solid #437EEB'}
                  borderRadius={'5px'}
               >
                  Export CSV File
               </Button>
            </Flex>
            <Flex direction={'column'} gap={2}>
               <Text>2. Edit Costs in the file</Text>
               <TableContainer className='import-cost-table' mx={5}>
                  <Table variant='simple'>
                     <Thead>
                        <Tr>
                           <Th>Order_Id</Th>
                           <Th>Shipping_Cost</Th>
                        </Tr>
                     </Thead>
                     <Tbody>
                        {' '}
                        {fileData.map((data, index) => {
                           return (
                              <Tr key={index}>
                                 <Td background={'#C2CBD4'}>{data.Order_Id}</Td>
                                 <Td textAlign={'center'}>
                                    {data.Shipping_Cost}
                                 </Td>
                              </Tr>
                           );
                        })}{' '}
                     </Tbody>
                  </Table>
               </TableContainer>
            </Flex>
            <Flex direction={'column'} gap={2}>
               <Text>3. Upload modified file</Text>
               <Button
                  width={'fit-content'}
                  color={'white'}
                  _hover={{
                     backgroundColor: '#437EEBBB',
                  }}
                  onClick={() => fileInput.current && fileInput.current.click()}
                  ml={5}
                  backgroundColor={'#437EEB'}
                  py={1}
                  fontSize={'15px'}
                  fontWeight={'400'}
                  px={2}
                  border={'1px solid #437EEB'}
                  borderRadius={'5px'}
               >
                  Import CSV File
               </Button>{' '}
               <Input
                  ref={fileInput}
                  onInput={handleUpload}
                  display={'none'}
                  type='file'
               ></Input>{' '}
            </Flex>
         </Flex>
      </ModalWrapper>
   );
}

export default ImportCostModal;
