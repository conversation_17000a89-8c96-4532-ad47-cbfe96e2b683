import React from 'react';
import { useNavigate } from 'react-router-dom';
import image from '../images/integrations/arrow.png';
import './woocommerce-steps.scss';

interface StoreConnectionStepsProps {
   steps: string[];
   link?: string;
   note?: string;
}

const processString = (input: string, index: number): JSX.Element => {
   const regex = /<([^:]+):([^>]+)>/g;
   const result = input.replace(regex, (_, p1, p2) => {
      return `<a href="${p2}" target="_blank">${p1}</a>`;
   });
   return (
      <li
         key={index}
         dangerouslySetInnerHTML={{
            __html: `<h5>Step ${index + 1}: ${result}</h5>`,
         }}
      />
   );
};
const StoreConnectionSteps: React.FC<StoreConnectionStepsProps> = ({
   steps,
   note,
}) => {
   const navigate = useNavigate();

   const goBack = () => {
      navigate(-1);
   };

   return (
      <div className='WoocommerceSteps'>
         <div className='back-arrow' onClick={goBack}>
            <img src={image} alt='arrow' />
            <h4 className='title'>Integration steps</h4>
         </div>
         {note && <p className='note'>{note}</p>}
         <ol className='steps'>
            {steps.map((step, index) => processString(step, index))}
         </ol>
      </div>
   );
};

export default StoreConnectionSteps;
