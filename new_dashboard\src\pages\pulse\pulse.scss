@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500&display=swap');

.Pulse {
   // @media (max-width: 1400px) {
   //    margin-left: 20px;
   // }

   // @media (min-width: 1600px) {
   //    margin-left: 7%;
   //    margin-right: 6%;
   // }
   .align {
      display: flex;
      align-items: center;
      margin-bottom: 25px;
      margin-top: 20px;
      width: 98%;
      justify-content: space-between;
   }

   .Main {
      font-size: 28px;
      font-weight: 500;
      color: #242424;
      margin-top: 20px;
   }

   .links {
      margin-left: 10px;
      padding: 10px;
      display: flex;
      .export {
         border: 1px solid #ccc;
         border-radius: 5px;
         margin-left: 15px;
         padding: 6px;
         font-size: 14px;
         background-color: #c2c0c0;
         color: #8e8e8e;
         cursor: none;
      }

      a {
         margin-right: 20px;
      }
      .select {
         color: blue;
         text-decoration: underline;
         font-weight: bold;
      }
   }
   .error {
      margin-left: 35%;
      margin-top: 5%;
   }
   .text {
      margin-left: 20%;

      h2 {
         font-family: 'Poppins', sans-serif;
         font-weight: 500;
         color: #242424;
         font-size: 25px;
      }
      ul {
         margin-left: 5%;
         font-weight: 500;
      }
   }
   .texts {
      margin-left: 20%;
      margin-top: 15%;
      h2 {
         font-family: 'Poppins', sans-serif;
         font-weight: 500;
         color: #242424;
         font-size: 25px;
      }
   }
   .spinner-fetch {
      margin-left: 50%;
      margin-top: 20%;
   }
   .Dropdowns {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .left-drop {
         margin-left: 10px;
         gap: 15px;
         display: flex;
      }
      .right-drop {
         display: flex;
         gap: 4px;
         margin-right: 140px;
         .export {
            border: 1px solid #ccc;
            border-radius: 5px;
            margin-left: 15px;
            padding: 6px;
            background-color: #ccc;
            cursor: none;
         }
      }
   }
   .cards {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 6px;
      margin-left: 6px;
      margin-top: 30px;
      margin-bottom: 30px;
      .card {
         width: 100%;
      }
      @media (max-width: 1200px) {
         grid-template-columns: repeat(2, 1fr);
      }

      @media (max-width: 768px) {
         grid-template-columns: 1fr;
      }
   }
   .channels {
      margin-top: 50px;
      display: flex;
      gap: 40px;
      flex-wrap: wrap;
   }
   .widget-container {
      display: flex;
      flex-direction: column;
      gap: 20px;
   }
   .No-Data {
      padding-left: 400px;
      padding-top: 200px;
   }
}
