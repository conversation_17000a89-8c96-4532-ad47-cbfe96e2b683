@use '../../../sass/variable.scss';
:root {
   --title-color: #2c3e50;
}

// [data-theme='dark'] {
//    --title-color: $text_color;
// }

.pulse {
   display: flex;
   flex-direction: column;
   gap: 20px;

   .title {
      h5 {
         color: var(--title-color);
         font-family: 'Poppins', sans-serif;
         font-size: 23px;
         font-weight: 700;
         line-height: 28px;
      }
   }

   .pulse-components {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-bottom: 20px;
      color: #333333;
      background-color: #ffffff;

      .pulse-components > * {
         flex: 1 1 calc(33.333% - 20px);
         max-width: calc(33.333% - 20px);
      }
      @media (max-width: 760px) {
         .pulse-components > * {
            flex: 1 1 100%;
            max-width: 100%;
         }
      }

      // Light theme (default)

      // [data-theme='dark'] & {
      //    background-color: $background;
      //    color: $text_color;
      // }
   }
}
