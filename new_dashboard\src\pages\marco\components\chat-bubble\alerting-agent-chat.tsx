import {
   PiList<PERSON>hecks,
   PiPauseCircle,
   PiPencilDuotone,
   PiPlayCircleBold,
} from 'react-icons/pi';
import { useAppDispatch } from '../../../../store/store';
import { openModal } from '../../../../store/reducer/modal-reducer';
import { modalTypes } from '../../../../components/modals/modal-types';
import { useNavigate } from 'react-router-dom';
import { setCurrentAlertID } from '../../../../store/reducer/alerting-agent-reducer';
import {
   AlertingAgentAlert,
   AlertingAgentChatMetaData,
   AlertingAgentChat as AlertingAgentChatType,
} from '../../../../api/service/agentic-workflow/alerting-agent';
import { Skeleton } from '@/components/ui/skeleton';
import {
   Card,
   CardAction,
   CardDescription,
   CardHeader,
   CardTitle,
} from '@/components/ui/card';
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuSeparator,
   DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { PiDotsThreeVerticalBold } from 'react-icons/pi';
import { Badge } from '@/components/ui/badge';
import {
   useFetchAlertsBySessionIdQuery,
   usePauseUnpauseAlertMutation,
} from '../../apis/alerting-agent-apis';

interface AlertingAgentChatProps {
   currentSessionChats: AlertingAgentChatType[];
   currentSessionAlerts: AlertingAgentAlert[];
}

const UserChatBubble = ({ content }: { content: string }) => {
   return (
      <div className='flex justify-end w-[100%] mt-2 mb-5'>
         <div className='w-[fit-content] max-w-[85%] md:max-w-[65%] bg-[#3444ab] text-white p-[12px_14px] text-left rounded-l-[15px] rounded-r-[0px] rounded-b-[15px] text-[15px] relative inline-block'>
            <p className='text-[12px] md:text-[14px]'>{content}</p>
         </div>
      </div>
   );
};

const ErrorChatBubble = ({ content }: { content: string }) => {
   return (
      <div className='flex justify-start w-[100%] mt-2'>
         <div className='w-[100%] text-black p-[12px_14px]'>
            <div className='flex gap-6'>
               <p className='text-md'>
                  {content ||
                     "I'm having trouble creating the alert. Please try again."}
               </p>
            </div>
         </div>
      </div>
   );
};

const PendingBubble = () => {
   return (
      <div className='flex justify-start w-[100%] mt-2'>
         <div className='w-[100%] text-white p-[12px_14px]'>
            <div className='flex flex-col gap-4'>
               <Skeleton className='h-4 w-[75%]' />
               <Skeleton className='h-4 w-[75%]' />
               <Skeleton className='h-4 w-[75%]' />
            </div>
         </div>
      </div>
   );
};

const AgentChatBubble = (payload: {
   currentChat: AlertingAgentChatType;
   currentSessionAlerts: AlertingAgentAlert[];
}) => {
   const dispatch = useAppDispatch();
   const navigate = useNavigate();

   const { currentChat, currentSessionAlerts } = payload;

   const chat_meta_data =
      (JSON.parse(currentChat.final_response) as AlertingAgentChatMetaData) ||
      {};

   const currentAlert = currentSessionAlerts.find(
      (alert) => String(alert.alert_id) === String(chat_meta_data.alert_id),
   );

   const { refetch: refetchAlertsBySession } = useFetchAlertsBySessionIdQuery();
   const { mutateAsync: pauseUnpauseAlert } = usePauseUnpauseAlertMutation();

   const handleEditAlert = (alert_id: string) => {
      dispatch(setCurrentAlertID(String(alert_id)));

      dispatch(
         openModal({
            modalType: modalTypes.EDIT_ALERT_MODAL,
            modalProps: {
               alert_setup_status: chat_meta_data.alert_setup_status,
               alert_id: String(alert_id),
               currentChat: currentChat,
               meta_data: chat_meta_data.meta_data,
            },
         }),
      );
   };

   const handlePauseUnpause = async (
      event: React.MouseEvent<HTMLDivElement>,
   ) => {
      event.stopPropagation();
      if (!currentAlert) return;

      await pauseUnpauseAlert({
         client_id: currentAlert.client_id,
         user_id: currentAlert.user_id,
         alert_id: String(currentAlert.alert_id),
      });

      await refetchAlertsBySession();
   };

   const handleViewAllAlerts = (event: React.MouseEvent<HTMLDivElement>) => {
      event.stopPropagation();
      navigate('/marco/alerting-agent/alerts');
   };

   return (
      <>
         {chat_meta_data.alert_setup_status === 'success' && (
            <div className='flex justify-start w-[100%] mt-2'>
               <div className='w-[100%] text-black py-[12px]'>
                  <div className='flex items-start gap-4'>
                     <Card
                        className={`w-full max-w-md ${currentAlert?.alert_id ? 'hover:cursor-pointer' : ''}`}
                        onClick={() =>
                           handleEditAlert(
                              currentAlert?.alert_id
                                 ? String(currentAlert?.alert_id)
                                 : '',
                           )
                        }
                     >
                        <CardHeader className='flex flex-row justify-between items-center flex-wrap gap-4'>
                           <div className='w-[89%]'>
                              <CardTitle className='flex items-center justify-start gap-2'>
                                 <p className='text-[16px] font-semibold'>
                                    {currentAlert?.alert_name ||
                                       chat_meta_data.alert_name}
                                 </p>
                                 <Badge
                                    className={`text-[12px] text-white py-0 ${currentAlert?.alert_status === 'ACTIVE' ? 'bg-green-600' : currentAlert?.alert_status === 'PAUSED' ? 'bg-gray-400' : 'bg-red-800'} font-semibold rounded-full`}
                                 >
                                    {currentAlert?.alert_status || 'DELETED'}
                                 </Badge>
                              </CardTitle>
                              <CardDescription>
                                 {currentAlert?.alert_description ||
                                    chat_meta_data.alert_description}
                              </CardDescription>
                           </div>
                           <CardAction className='my-auto'>
                              <DropdownMenu>
                                 <DropdownMenuTrigger>
                                    <PiDotsThreeVerticalBold
                                       size='24px'
                                       className='hover:cursor-pointer'
                                    />
                                 </DropdownMenuTrigger>
                                 <DropdownMenuContent className='bg-white'>
                                    {currentAlert && (
                                       <>
                                          <DropdownMenuItem
                                             onClick={() =>
                                                handleEditAlert(
                                                   String(
                                                      currentAlert?.alert_id,
                                                   ) || '',
                                                )
                                             }
                                             className='flex items-center gap-4 w-[200px] h-10 hover:cursor-pointer'
                                          >
                                             <PiPencilDuotone />
                                             <p>Edit</p>
                                          </DropdownMenuItem>
                                          <DropdownMenuItem
                                             onClick={(e) =>
                                                void handlePauseUnpause(e)
                                             }
                                             className='flex items-center gap-4 w-[200px] h-10 hover:cursor-pointer'
                                          >
                                             {currentAlert?.alert_status ===
                                             'ACTIVE' ? (
                                                <PiPauseCircle />
                                             ) : (
                                                <PiPlayCircleBold />
                                             )}
                                             <p>
                                                {currentAlert?.alert_status ===
                                                'ACTIVE'
                                                   ? 'Pause'
                                                   : 'Resume'}
                                             </p>
                                          </DropdownMenuItem>
                                          <DropdownMenuSeparator />
                                       </>
                                    )}
                                    <DropdownMenuItem
                                       onClick={handleViewAllAlerts}
                                       className='flex items-center gap-4 w-[200px] h-10 hover:cursor-pointer'
                                    >
                                       <PiListChecks />
                                       <p>View All Alerts</p>
                                    </DropdownMenuItem>
                                 </DropdownMenuContent>
                              </DropdownMenu>
                           </CardAction>
                        </CardHeader>
                     </Card>
                  </div>
               </div>
            </div>
         )}
         {chat_meta_data.alert_setup_status === 'missing' && (
            <div className='flex justify-start w-[100%] mt-2'>
               <div className='w-[100%] text-black py-[12px]'>
                  <div className='flex items-start gap-4'>
                     <Card className='w-full max-w-md !border-red-500'>
                        <CardHeader className='flex flex-row justify-between items-center flex-wrap gap-4'>
                           <div className='w-[89%]'>
                              <CardTitle className='flex items-center justify-start gap-2'>
                                 <p className='text-[16px] font-semibold'>
                                    {chat_meta_data.meta_data.data.title}
                                 </p>
                              </CardTitle>
                              <CardDescription>
                                 Some of the required fields are missing. Please
                                 edit to complete the setup.
                              </CardDescription>
                           </div>
                           <CardAction className='my-auto'>
                              <DropdownMenu>
                                 <DropdownMenuTrigger>
                                    <PiDotsThreeVerticalBold
                                       size='24px'
                                       className='hover:cursor-pointer'
                                    />
                                 </DropdownMenuTrigger>
                                 <DropdownMenuContent className='bg-white'>
                                    <DropdownMenuItem
                                       onClick={() => handleEditAlert('')}
                                       className='flex items-center gap-4 w-[200px] h-10 hover:cursor-pointer'
                                    >
                                       <PiPencilDuotone />
                                       <p>Edit</p>
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                       onClick={handleViewAllAlerts}
                                       className='flex items-center gap-4 w-[200px] h-10 hover:cursor-pointer'
                                    >
                                       <PiListChecks />
                                       <p>View All Alerts</p>
                                    </DropdownMenuItem>
                                 </DropdownMenuContent>
                              </DropdownMenu>
                           </CardAction>
                        </CardHeader>
                     </Card>
                  </div>
               </div>
            </div>
         )}
      </>
   );
};

const AlertingAgentChat = (props: AlertingAgentChatProps) => {
   const { currentSessionChats, currentSessionAlerts } = props;

   return (
      <>
         {currentSessionChats?.map((currentChat) => (
            <div key={currentChat.chat_id}>
               <UserChatBubble content={currentChat.user_query} />
               {currentChat.response_status === 'error' ? (
                  <ErrorChatBubble content={currentChat.final_response} />
               ) : currentChat.response_status === 'pending' ? (
                  <PendingBubble />
               ) : (
                  <AgentChatBubble
                     currentChat={currentChat}
                     currentSessionAlerts={currentSessionAlerts}
                  />
               )}
            </div>
         ))}
      </>
   );
};

export default AlertingAgentChat;
