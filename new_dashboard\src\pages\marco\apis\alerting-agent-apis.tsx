import alertingAgentEndpoints, {
   AlertingAgentChat,
} from '@/api/service/agentic-workflow/alerting-agent';
import { useApiMutation, useApiQuery } from '@/hooks/react-query-hooks';
import { useAppSelector } from '@/store/store';
import { AuthUser } from '@/types/auth';
import { Keys, LocalStorageService } from '@/utils/local-storage';

export const useFetchAlertingHistoryQuery = () => {
   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) || {};

   const { currentAgent } = useAppSelector((state) => state.marco);
   const { currentPage } = useAppSelector((state) => state.alertingAgent);

   return useApiQuery<AlertingAgentChat[]>({
      queryKey: ['alerting-agent-history', currentAgent, String(currentPage)],
      queryFn: () =>
         alertingAgentEndpoints.fetchAllSessionsByUserID({
            client_id: client_id || '',
            user_id: user_id || '',
            page: currentPage,
         }),
      enabled: !!client_id && !!user_id && currentAgent === 'alerting-agent',
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
   });
};

export const useFetchHistoryBySessionIdQuery = () => {
   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) || {};

   const { currentAgent } = useAppSelector((state) => state.marco);
   const { currentSessionID } = useAppSelector((state) => state.alertingAgent);

   return useApiQuery<AlertingAgentChat[]>({
      queryKey: ['chat-history', String(currentSessionID), currentAgent],
      queryFn: () =>
         alertingAgentEndpoints.fetchHistoryBySessionID({
            client_id: client_id || '',
            user_id: user_id || '',
            session_id: currentSessionID || '',
         }),
      enabled: !!currentSessionID,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
   });
};

export const useAddChatToSessionHistoryMutation = () => {
   return useApiMutation({
      queryKey: ['addToSessionHistory'],
      mutationFn: alertingAgentEndpoints.addChatToSessionHistory,
   });
};

export const useFetchChatByChatIdQuery = () => {
   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) || {};

   const { currentSessionID, currentChatID } = useAppSelector(
      (state) => state.alertingAgent,
   );

   return useApiQuery<AlertingAgentChat>({
      queryKey: [
         'chat-by-chat-id',
         String(currentSessionID),
         String(currentChatID),
      ],
      queryFn: () =>
         alertingAgentEndpoints.fetchChatByChatId({
            client_id: client_id || '',
            user_id: user_id || '',
            session_id: currentSessionID || '',
            chat_id: currentChatID || '',
         }),
      enabled: !!currentSessionID && !!currentChatID,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
   });
};

export const useFetchAlertsBySessionIdQuery = () => {
   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) || {};

   const { currentAgent } = useAppSelector((state) => state.marco);
   const { currentSessionID } = useAppSelector((state) => state.alertingAgent);

   return useApiQuery({
      queryKey: ['alerts-by-session', String(currentSessionID), currentAgent],
      queryFn: () =>
         alertingAgentEndpoints.fetchAlertsBySessionID({
            client_id: client_id || '',
            user_id: user_id || '',
            session_id: currentSessionID || '',
         }),
      enabled: !!currentSessionID,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
   });
};

export const useFetchFeatureUsageQuery = () => {
   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) ?? {};

   const fetchFeatureUsagePayload = {
      client_id: client_id || '',
      user_id: user_id || '',
      feature_name: 'analytics_agent',
      feature_type: 'agent',
   };

   return useApiQuery({
      queryKey: ['featureUsage', 'alerting-agent'],
      queryFn: () =>
         alertingAgentEndpoints.fetchUserFeatureUsage(fetchFeatureUsagePayload),
      enabled: !!client_id && !!user_id,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      refetchOnMount: false,
   });
};

export const useTrackFeatureUsageMutation = () => {
   return useApiMutation({
      queryKey: ['trackFeatureUsage', 'alerting-agent'],
      mutationFn: alertingAgentEndpoints.trackFeatureUsage,
   });
};

export const useFetchAllAlertsQuery = () => {
   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) || {};

   const { currentAgent } = useAppSelector((state) => state.marco);

   return useApiQuery({
      queryKey: ['alerting-agent-alerts', currentAgent],
      queryFn: () =>
         alertingAgentEndpoints.fetchAllAlerts({
            client_id: client_id || '',
            user_id: user_id || '',
         }),
      enabled: !!client_id && !!user_id,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
   });
};

export const useFetchAlertByIdQuery = () => {
   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) || {};

   const { currentAlertID } = useAppSelector((state) => state.alertingAgent);

   return useApiQuery({
      queryKey: ['alerting-agent-alert', currentAlertID],
      queryFn: () =>
         alertingAgentEndpoints.fetchAlertById({
            client_id: client_id || '',
            user_id: user_id || '',
            alert_id: currentAlertID,
         }),
      enabled: false,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
   });
};

export const useCreateAlertMutation = () => {
   return useApiMutation({
      queryKey: ['createAlert'],
      mutationFn: alertingAgentEndpoints.createAlert,
   });
};

export const useUpdateAlertMutation = () => {
   return useApiMutation({
      queryKey: ['updateAlert'],
      mutationFn: alertingAgentEndpoints.updateAlert,
   });
};

export const useDeleteAlertMutation = () => {
   return useApiMutation({
      queryKey: ['deleteAlert'],
      mutationFn: alertingAgentEndpoints.deleteAlert,
   });
};

export const useDeleteMultipleAlertsMutation = () => {
   return useApiMutation({
      queryKey: ['deleteMultipleAlerts'],
      mutationFn: alertingAgentEndpoints.deleteMultipleAlerts,
   });
};

export const usePauseUnpauseAlertMutation = () => {
   return useApiMutation({
      queryKey: ['pauseUnpauseAlert'],
      mutationFn: alertingAgentEndpoints.pauseUnpauseAlert,
   });
};

export const useUpdateEmailRecipientsMutation = () => {
   return useApiMutation({
      queryKey: ['updateEmailRecipients'],
      mutationFn: alertingAgentEndpoints.updateRecipients,
   });
};
