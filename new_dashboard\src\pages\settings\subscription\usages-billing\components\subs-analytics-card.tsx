import { RootState } from '@/store/store';
import { useSelector } from 'react-redux';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { useNavigate } from 'react-router-dom';
import { cn } from '@/utils';
import {
   Card,
   CardHeader,
   CardTitle,
   CardContent,
   CardFooter,
} from '@/components/ui/card';
import { useState } from 'react';
import { UserDetails } from '@/pages/socialwatch/interface';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import subsEndPoints from '@/api/service/subscription';
import { toast } from 'sonner';
import Swal from 'sweetalert2';

const SubsAnalyticsCard = () => {
   const navigate = useNavigate();
   const [isCancelling, setIsCancelling] = useState(false);
   const { isActive, autoRenew, users, planName, isYearly, amount, endDate } =
      useSelector((state: RootState) => state.subscription);
   const subscribed = isActive === true;
   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);

   const billing = isYearly ? 'Yearly' : 'Monthly';
   const formattedAmount = `$${amount}/${isYearly ? 'Year' : 'Month'}`;
   const formattedDate = endDate
      ? format(new Date(endDate), 'MMMM d, yyyy')
      : '-';

   const handleCancelSubscription = async () => {
      if (!subscribed) {
         toast.error('No active subscription to cancel.');
         return;
      }

      const { isConfirmed } = await Swal.fire({
         title: 'Cancel Subscription?',
         text: 'Are you sure you want to cancel your subscription? This action cannot be undone.',
         icon: 'warning',
         showCancelButton: true,
         confirmButtonColor: '#d33',
         cancelButtonColor: '#3085d6',
         confirmButtonText: 'Yes, cancel it!',
         cancelButtonText: 'No, keep it',
         reverseButtons: true,
      });

      if (!isConfirmed) {
         return;
      }

      setIsCancelling(true);

      try {
         const response = await subsEndPoints.cancelSubscription(
            userDetails.client_id,
         );

         if (response?.data?.success) {
            toast.success('Subscription cancelled successfully.');
            window.location.reload();
         } else {
            toast.error(
               response?.data?.message || 'Failed to cancel subscription.',
            );
         }
      } catch (error: unknown) {
         console.error('Error cancelling subscription:', error);
         toast.error(
            'An error occurred while cancelling your subscription. Please try again.',
         );
      } finally {
         setIsCancelling(false);
      }
   };

   return (
      <Card className='w-full h-full rounded-xl shadow-md border !border-porcelain bg-white'>
         <CardHeader className='pb-0'>
            <CardTitle className='text-lg font-semibold text-jet'>
               Subscription Analytics
            </CardTitle>
            <p className='text-sm text-stone mt-1'>
               Manage your Flable AI plan, view usage, and access your billing
               history.
            </p>
         </CardHeader>

         <CardContent className='space-y-6 pt-4'>
            <div className='space-y-2 pb-4'>
               <div className='flex justify-between text-sm font-medium text-charcoal'>
                  <span className='pb-2'>Users</span>
                  <span>{users}</span>
               </div>
               <div className='w-full h-2 bg-skyLight rounded-full overflow-hidden'>
                  {users && (
                     <div
                        className='h-full bg-navy transition-all duration-300'
                        style={{ width: `${Math.min(users * 5, 100)}%` }}
                     />
                  )}
               </div>
            </div>

            <div className='flex justify-between text-sm text-charcoal pb-4 border-b border-porcelain'>
               <span>Plan:</span>
               <span className='text-navy para4 font-medium'>
                  {planName || '—'}
               </span>
            </div>

            <div className='flex justify-between text-sm text-charcoal pb-4 border-b border-porcelain'>
               <span>Active:</span>
               <span
                  className={cn('font-medium', {
                     'text-vermilion': !subscribed,
                     'text-navy': subscribed,
                  })}
               >
                  {`${subscribed}`}
               </span>
            </div>

            <div className='flex justify-between text-sm text-charcoal pb-4 border-b border-porcelain'>
               <span>Auto Renew:</span>
               <span
                  className={cn('font-medium', {
                     'text-vermilion': !autoRenew,
                     'text-navy': autoRenew,
                  })}
               >
                  {`${autoRenew}`}
               </span>
            </div>

            <div className='flex justify-between text-sm text-charcoal pb-4 border-b border-porcelain'>
               <span>Billing:</span>
               <span>{billing}</span>
            </div>

            <div className='flex justify-between text-sm text-charcoal pb-4 border-b border-porcelain'>
               <span>Price:</span>
               <span>{formattedAmount}</span>
            </div>

            <div className='flex justify-between text-sm text-charcoal pb-2'>
               <span>Active Until</span>
               <span>{formattedDate}</span>
            </div>
         </CardContent>

         <CardFooter className='flex justify-start gap-4 pt-4'>
            <Button
               onClick={() => navigate('/settings?mode=plansTopups&tab=plans')}
            >
               Upgrade Plan
            </Button>
            <Button
               variant='ghost'
               className='text-navy para6 hover:text-vermilion'
               onClick={() => void handleCancelSubscription()}
               disabled={isCancelling || !subscribed}
            >
               {isCancelling ? 'Cancelling...' : 'Cancel Subscription'}
            </Button>
         </CardFooter>
      </Card>
   );
};

export default SubsAnalyticsCard;
