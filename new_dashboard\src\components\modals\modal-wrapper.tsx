import React from 'react';
import {
   <PERSON><PERSON>,
   <PERSON>dal<PERSON><PERSON>lay,
   Modal<PERSON><PERSON>nt,
   ModalHeader,
   ModalBody,
   Modal<PERSON>ooter,
   ModalCloseButton,
   useColorModeValue,
} from '@chakra-ui/react';
import { useDispatch } from 'react-redux';
import { closeModal } from '../../store/reducer/modal-reducer';

interface Props {
   size?:
      | 'xs'
      | 'sm'
      | 'md'
      | 'lg'
      | 'xl'
      | 'full'
      | '2xl'
      | '3xl'
      | '4xl'
      | '5xl'
      | '6xl'
      | undefined;
   children: React.ReactNode;
   footer?: React.ReactNode;
   heading: string | undefined;
   bgcolor?: string;
   parentClassName?: string;
   blockScrollOnMount?: boolean | undefined;
   overlayBgcolor?: string;
   closeOnEsc?: boolean;
   closeOnOverlayClick?: boolean;
   noCloseBtn?: boolean;
   isClosable?: boolean;
   closeFunction?: () => void;
   boxShadow?: boolean; // <-- new prop
}

function ModalWrapper({
   size,
   children,
   footer,
   heading,
   bgcolor,
   parentClassName,
   closeOnEsc,
   closeOnOverlayClick,
   noCloseBtn,
   blockScrollOnMount,
   overlayBgcolor,
   isClosable = true,
   closeFunction,
   boxShadow = true, // default true
}: Props) {
   const dispatch = useDispatch();
   const overlayBg = useColorModeValue('blackAlpha.600', 'blackAlpha.800');
   const modalBg = bgcolor ? bgcolor : useColorModeValue('white', 'gray.800');
   const headingColor = useColorModeValue('gray.900', 'white !important');

   function onClose() {
      if (isClosable) {
         closeFunction && closeFunction();
         dispatch(closeModal());
      }
   }

   return (
      <Modal
         size={size}
         isOpen={true}
         onClose={onClose}
         closeOnOverlayClick={closeOnOverlayClick}
         closeOnEsc={closeOnEsc}
         blockScrollOnMount={blockScrollOnMount}
      >
         <ModalOverlay bg={overlayBgcolor || overlayBg} />
         <ModalContent
            className={parentClassName}
            bg={modalBg}
            boxShadow={boxShadow ? 'lg' : 'none'}
         >
            {heading && (
               <ModalHeader color={headingColor}>{heading}</ModalHeader>
            )}

            {!noCloseBtn && <ModalCloseButton />}
            <ModalBody>{children}</ModalBody>
            {footer && <ModalFooter>{footer}</ModalFooter>}
         </ModalContent>
      </Modal>
   );
}

export default ModalWrapper;
