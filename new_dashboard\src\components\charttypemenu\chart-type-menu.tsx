import React, { useCallback, useState } from 'react';
import { <PERSON><PERSON>, Flex, Heading, Select, Tooltip } from '@chakra-ui/react';
import { PerformanceChartData } from '../chatbox/interface';
import './chart-type-menu.scss';
import { toHHMMSS } from '../../pages/dashboard/utils/helpers';
import { MdDownload } from 'react-icons/md';
interface ChartTypeMenuProps {
   id: string;
   handleChartTypeSelect: (id: string, chartType: string) => void;
   performanceData: PerformanceChartData | undefined;
   defaultValue: string | undefined;
}

const ChartTypeMenu: React.FC<ChartTypeMenuProps> = ({
   id,
   handleChartTypeSelect,
   performanceData,
   defaultValue,
}) => {
   if (!performanceData) return null;
   const [selectedChartType, setSelectedChartType] = useState<string>(
      defaultValue || 'bar',
   );

   const handleChartTypeChange = (value: string) => {
      setSelectedChartType(value);
      handleChartTypeSelect(id, value);
   };
   if (
      Array.isArray(performanceData) ||
      (performanceData && performanceData.data.length == 0)
   )
      return null;

   if (
      (performanceData.schema?.fields &&
         performanceData.schema?.fields?.length > 4) ||
      performanceData.table
   ) {
      const { fields } = performanceData.schema;
      const xAxisField = fields[1].name;
      const validFields = fields.filter((f) => !f.name.includes('cumulative'));
      const exportToCsv = useCallback(() => {
         const csv = [
            validFields
               .slice(1)
               .map((field) => field.name)
               .join(','), // header row first
            ...performanceData.data.map((d) =>
               validFields
                  .slice(1)
                  .map((field) => {
                     if (field.name == xAxisField) return d[field.name];
                     const showVal =
                        field.type == 'number'
                           ? Math.round(Number(d[field.name]) * 100) / 100
                           : toHHMMSS(Number(d[field.name]));
                     return showVal;
                  })
                  .join(','),
            ),
         ].join('\r\n');

         const blob = new Blob([csv], { type: 'text/csv' });
         const url = URL.createObjectURL(blob);
         const link = document.createElement('a');
         link.href = url;
         link.download = '_data.csv';
         link.click();
         document.body.removeChild(link);
      }, []);
      return (
         <Flex flex={1} justifyContent={'space-between'} alignItems={'center'}>
            <Heading fontSize={'16px'}>{performanceData.text}</Heading>
            <Tooltip
               label='Export to Excel'
               fontSize='sm'
               bg='#437eeb'
               color='white'
               marginTop='5px'
               marginLeft='30px'
            >
               <Button
                  fontSize={'12px'}
                  padding={'4px 6px'}
                  marginRight={'4px'}
                  minWidth={'fit-content'}
                  background={'none'}
                  onClick={exportToCsv}
               >
                  <MdDownload style={{ height: 20, width: 20 }} />
               </Button>
            </Tooltip>
         </Flex>
      );
   }

   return (
      <div>
         <Select
            value={selectedChartType}
            onChange={(e) => handleChartTypeChange(e.target.value)}
            width={{ base: '60px', sm: '60px', md: '70px', lg: '80px' }}
            height={{ base: '20px', sm: '20px', md: '25px', lg: '30px' }}
            fontWeight='normal'
            fontSize={{ base: 'xs', md: 'xs', lg: 'sm' }}
            borderColor='#5579f2'
            color='#5579f2'
         >
            <option value='bar'>Bar</option>
            <option value='line'>Line</option>
            <option value='pie'>Pie</option>
         </Select>
      </div>
   );
};

export default ChartTypeMenu;
