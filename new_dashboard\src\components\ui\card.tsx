import * as React from 'react';

import { cn } from '@/utils';

const Card = React.forwardRef<
   HTMLDivElement,
   React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
   <div
      ref={ref}
      className={cn(
         'bg-white w-full p-6 flex flex-col space-y-5 rounded-3xl border !border-lavenderGray shadow-lg items-center justify-between',
         className,
      )}
      {...props}
   />
));
Card.displayName = 'Card';

const CardHeader = React.forwardRef<
   HTMLDivElement,
   React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
   <div
      ref={ref}
      className={cn('flex flex-col w-full', className)}
      {...props}
   />
));
CardHeader.displayName = 'CardHeader';

const CardTitle = React.forwardRef<
   HTMLParagraphElement,
   React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
   <h3
      ref={ref}
      className={cn('para3 font-semibold w-full', className)}
      {...props}
   />
));
CardTitle.displayName = 'CardTitle';

const CardDescription = React.forwardRef<
   HTMLParagraphElement,
   React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
   <p ref={ref} className={cn('para6 w-full', className)} {...props} />
));
CardDescription.displayName = 'CardDescription';

function CardAction({ className, ...props }: React.ComponentProps<'div'>) {
   return (
      <div
         data-slot='card-action'
         className={cn(
            'col-start-2 row-span-2 row-start-1 self-start justify-self-end',
            className,
         )}
         {...props}
      />
   );
}

const CardContent = React.forwardRef<
   HTMLDivElement,
   React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
   <div ref={ref} className={cn('para6 w-full', className)} {...props} />
));
CardContent.displayName = 'CardContent';

const CardFooter = React.forwardRef<
   HTMLDivElement,
   React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
   <div
      ref={ref}
      className={cn('flex items-center w-full ', className)}
      {...props}
   />
));
CardFooter.displayName = 'CardFooter';

export {
   Card,
   CardHeader,
   CardFooter,
   CardTitle,
   CardDescription,
   CardContent,
   CardAction,
};
