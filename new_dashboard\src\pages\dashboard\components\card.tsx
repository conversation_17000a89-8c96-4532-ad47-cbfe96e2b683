import {
   Flex,
   Skeleton,
   Text,
   Tooltip,
   useToast,
   VStack,
   WrapItem,
   useColorMode,
} from '@chakra-ui/react';
import LineChart from './line-chart';

import { GrCircleQuestion } from 'react-icons/gr';
import { IoIosWarning } from 'react-icons/io';
import { MdCheckCircle } from 'react-icons/md';
import ViewDetails from './card-more-details';
import { CardProp, PinPayload, VisblePayload } from '../utils/interface';
import { BsPinAngleFill, BsPinAngle } from 'react-icons/bs';
import { useState } from 'react';
import { useApiMutation } from '../../../hooks/react-query-hooks';
import KPIQueryKeys from '../utils/query-keys';
import kpiService from '../../../api/service/kpi/index';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { useDispatch } from 'react-redux';
import { setUpdatedPins } from '../../../store/reducer/kpi-reducer';
import Value from './value';
import TooltipContent from './tooltip-content';
import KPIImage from './kpi-image';
import { calculateHelper } from '../../utils/kpiCalculaterHelper';
function KPICard(props: CardProp) {
   const [showRightIcons, setShowRightIcons] = useState(false);
   const { kpiDetails, prevKpi, loading, head, anomaly } = props;
   const toast = useToast();
   const dispatch = useDispatch();
   const { colorMode } = useColorMode();
   const handleMetaSuccess = (
      data: string,
      payload?: VisblePayload | PinPayload,
   ) => {
      dispatch(
         setUpdatedPins(
            `${payload?.category || data} - ${payload?.kpis[0] || data}` ||
               data,
         ),
      );
   };

   const { mutate: updatePinned, errorMessage: pinnedErr } = useApiMutation({
      queryKey: [KPIQueryKeys.kpiPinned],
      mutationFn: kpiService.updatePinned,
      onSuccessHandler: handleMetaSuccess,
   });
   const handleAdd = (pinStatus: boolean) => {
      const kpi = kpiDetails.allData[0];
      updatePinned({
         clientId: LocalStorageService.getItem(Keys.ClientId) || '',
         category: kpi.category,
         kpis: [kpi.kpi_names],
         pinned: !pinStatus,
      });
      kpiDetails.pinned = !pinStatus;
      dispatch(setUpdatedPins(kpi.kpi_names + kpi.category));
   };
   if (pinnedErr) {
      toast({
         title: 'Error updating added KPI"s',
         description: pinnedErr,
         status: 'error',
         duration: 5000,
         isClosable: true,
      });
   }
   const imageStyles: object = {
      height: kpiDetails.category == 'amazon_ads' ? '16px' : '20px',
      width: kpiDetails.category == 'amazon_ads' ? '25px' : '20px',
      position: kpiDetails.category == 'amazon_ads' ? 'relative' : '',
      top: kpiDetails.category == 'amazon_ads' ? '3px' : '',
   };

   const value = anomaly;
   const { percentage, color, direction } = calculateHelper(
      kpiDetails.kpi_names,
      kpiDetails.totalVal,
      prevKpi.totalVal,
   );
   const arrow = direction === 'is up' ? '↑' : '↓';

   return (
      <WrapItem
         minWidth={100}
         background={
            value === true
               ? '#36B37E1A'
               : value === false
                 ? '#FF56301A'
                 : colorMode === 'dark'
                   ? 'gray.800'
                   : 'white'
         }
         boxShadow={
            colorMode === 'dark'
               ? '1px 1px 10px 1px #00000033'
               : '1px 1px 10px 1px #cccccc33'
         }
         padding={5}
         borderRadius={5}
         className='kpi-item'
         position={'relative'}
         onMouseOver={() => setShowRightIcons(true)}
         onMouseLeave={() => setShowRightIcons(false)}
      >
         <VStack width='100%' alignItems={'flex-start'} flexGrow={1}>
            <div className='anomaly'>
               {value === true && <MdCheckCircle size={26} color='#36B37E' />}

               {value === false && <IoIosWarning size={26} color='#FF5630' />}
            </div>
            {showRightIcons && (
               <>
                  <div className='right-items'>
                     <TooltipContent
                        placement='top'
                        hasArrow
                        kpi={kpiDetails.allData[0].kpi_names}
                     >
                        <GrCircleQuestion />
                     </TooltipContent>
                     <Tooltip
                        placement='top'
                        hasArrow
                        label={kpiDetails.pinned ? 'Unpin KPI' : 'Pin KPI'}
                     >
                        <div
                           className='pin-item'
                           onClick={() => handleAdd(!!kpiDetails.pinned)}
                        >
                           {getPinIcon(kpiDetails.pinned)}
                        </div>
                     </Tooltip>
                  </div>
               </>
            )}

            <Flex width={'100%'}>
               <Flex
                  direction={'column'}
                  alignItems={'flex-start'}
                  textAlign={'left'}
                  maxWidth={'40%'}
               >
                  <Value
                     totalVal={kpiDetails.totalVal}
                     kpi={kpiDetails.allData[0]}
                  />

                  <Text
                     fontSize={11}
                     marginY={1}
                     fontWeight={600}
                     color={colorMode === 'dark' ? 'gray.400' : 'gray'}
                     maxWidth={'6rem'}
                     display={'flex'}
                     gap={2}
                  >
                     {head == 'pinned' && (
                        <KPIImage
                           style={imageStyles}
                           kpiCat={kpiDetails.category}
                        />
                     )}
                     {kpiDetails.displayName}
                  </Text>
                  {prevKpi?.allData?.length &&
                     (!loading?.last ? (
                        <h6 style={{ color }}>
                           <b>{percentage && `${percentage}% ${arrow}`}</b>
                        </h6>
                     ) : (
                        <Skeleton width='30px' height='10px' />
                     ))}
               </Flex>
               <LineChart kpiDetails={kpiDetails} value={value} />
            </Flex>
            <ViewDetails
               kpiDetails={kpiDetails}
               prevKpi={prevKpi}
               isAnomaly={value}
            />
         </VStack>
      </WrapItem>
   );
}

const getPinIcon = (pinned: boolean | undefined) => {
   return pinned ? <BsPinAngleFill /> : <BsPinAngle />;
};

// const getUpDownArrow = (up: boolean) => {
//    const { colorMode } = useColorMode();
//    return up ? (
//       <IoMdArrowUp
//          color={colorMode === 'dark' ? '#4ADE80' : '#0E9F6E'}
//          height={'12px'}
//       />
//    ) : (
//       <IoMdArrowDown
//          color={colorMode === 'dark' ? '#F87171' : 'red'}
//          height={'12px'}
//       />
//    );
// };
export default KPICard;
