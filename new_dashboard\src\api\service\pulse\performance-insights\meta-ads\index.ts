import { AxiosResponse } from 'axios';
import dashboardApiAgent from '../../../../agent';

/** MISCELLANEOUS **/

export interface KPIwiseAggregate {
   [key: string]: number | string;
}

export interface DateWiseAgg {
   [key: string]: KPIwiseAggregate;
}

export interface DaywiseKPIs {
   [key: string]: Record<string, number>;
}

export interface CampaignWithBudgetSpend {
   budget: number;
   channel: string;
   currency: string;
   kpi_name: string;
   client_id: string;
   date_time: string;
   kpi_value: number | 'NaN';
   load_date: string;
   objective: string;
   campaign_id: string;
   kpi_current: number;
   kpi_previous: number;
   time_in_days: number;
   campaign_name: string;
   campaign_status: string;
}

export interface CampaignKPIs {
   kpi_date: string;
   kpi_name: string;
   kpi_value: number | 'NaN';
   kpi_current: number;
   kpi_previous: number;
   campaign_status: string;
}

export interface DaywiseCampaignKPIs {
   campaign_id: string;
   campaign_name: string;
   recent_campaign_status: 'ACTIVE' | 'PAUSED';
   recent_currency: string;
   kpis: CampaignKPIs[];
}

export interface DaywiseCampaignKPIsCalculated {
   campaign_id: string;
   campaign_name: string;
   recent_campaign_status: 'ACTIVE' | 'PAUSED';
   recent_currency: string;
   kpis: CampaignKPIs[];
   daywise_kpis: DaywiseKPIs;
   prev_kpi_val: KPIwiseAggregate | null;
   current_kpi_val: KPIwiseAggregate;
}

export interface AdsetKPIs {
   kpi_name: string;
   kpi_previous: number | null;
   kpi_current: number;
   audience_interests: string;
   audience_behaviors: string;
}

export interface DaywiseAdsetKPIs {
   adset_id: string;
   kpi_name: string;
   age_value: string;
   client_id: string;
   date_time: string;
   kpi_value: number | 'NaN';
   load_time: string;
   objective: string;
   adset_name: string;
   campaign_id: string;
   kpi_current: number;
   adset_status: 'ACTIVE' | 'PAUSED';
   gender_value: string | null;
   kpi_previous: number;
   region_value: string;
   time_in_days: number;
   age_targeting: string;
   campaign_name: string;
   country_value: string | null;
   placement_value: string;
   placement_targeting: string;
   latest_audience_behavior: string;
   latest_audience_interest: string;
}

export interface DaywiseAdsetKPIsCalculated {
   adset_id: string;
   adset_name: string;
   adset_status: 'ACTIVE' | 'PAUSED';
   audience_behavior: string;
   audience_interest: string;
   performance_category: {
      category: string;
      insights: string;
   };
   kpis: AdsetKPIs[];
}

export interface AdKPIs {
   kpi_name: string;
   kpi_previous: number | null;
   kpi_current: number;
}

export interface DaywiseAdKPIs {
   client_id: string;
   objective: string;
   campaign_id: string;
   campaign_name: string;
   adset_id: string;
   adset_name: string;
   ad_id: string;
   ad_name: string;
   ad_status: 'ACTIVE' | 'PAUSED';
   kpi_name: string;
   date_time: string;
   kpi_current: number;
   kpi_previous: number;
   kpi_value: number | 'NaN';
   age_targeting: string;
   age_value: string;
   placement_targting: string;
   placement_value: string;
   region_value: string;
   time_in_days: number;
   load_date: string;
   gender_value: string | null;
   country_value: string | null;
}

export interface AdMetaData {
   client_id: string;
   ad_id: string;
   creative_id: string;
   caption: string;
   title: string;
   creative_status: 'ACTIVE' | 'PAUSED';
   ad_status: 'ACTIVE' | 'PAUSED';
   creative_type: string;
   call_to_action_type: string;
   instagram_permalink_url: string;
   video_thumbnail: string | null;
   redirect_link: string;
   image_hash: string;
   images_in_carousel: string;
   image_link: string;
   'pixel_dimension_(height_width)': string | null;
   updated_time: string;
   created_time: string;
}

export interface DaywiseAdKPIsCalculated {
   ad_id: string;
   ad_name: string;
   ad_status: 'ACTIVE' | 'PAUSED';
   kpis: AdKPIs[];
   meta_data: AdMetaData;
}

export interface TargetingKPIs {
   kpi_name: string;
   kpi_value: number | 'NaN';
}

export interface DaywiseTargetingKPIs {
   adset_id: string;
   campaign_id: string;
   client_id: string;
   date: string;
   kpi_name: string;
   kpi_value: number | 'NaN';
   load_date: string;
   targeting_key: string;
   targeting_type: string;
}

export interface DaywiseTargetingKPIsCalculated {
   adset_id: string;
   targeting_key: string;
   targeting_type: string;
   kpis: TargetingKPIs[];
}

export enum TargetingType {
   age = 'age',
   gender = 'gender',
   placement = 'placement',
   region = 'region',
   country = 'country',
}

export interface TargetingKPIsTable {
   age: DaywiseTargetingKPIsCalculated[];
   gender: DaywiseTargetingKPIsCalculated[];
   placement: DaywiseTargetingKPIsCalculated[];
   region: DaywiseTargetingKPIsCalculated[];
   country: DaywiseTargetingKPIsCalculated[];
}

/** PAYLOADS **/

export interface FetchCampaignsWithBudgetSpendPayload {
   client_id: string;
   campaign_id: string;
   objective: string;
   start_date: string;
   end_date: string;
}

export interface FetchCampaignDaywisePayload {
   client_id: string;
   objective: string;
   kpis: string[];
   start_date: string;
   end_date: string;
   prev_start_date: string;
   prev_end_date: string;
}

export interface FetchAdsetDaywisePayload {
   client_id: string;
   campaign_id: string;
   objective: string;
   start_date: string;
   end_date: string;
   prev_start_date: string;
   prev_end_date: string;
}

export interface FetchAdsDaywisePayload {
   client_id: string;
   campaign_id: string;
   adset_id: string;
   objective: string;
   start_date: string;
   end_date: string;
   prev_start_date: string;
   prev_end_date: string;
}

export interface FetchTargetingDaywisePayload {
   client_id: string;
   campaign_id: string;
   objective: string;
   adset_id: string;
   start_date: string;
   end_date: string;
}

export interface FetchChartInsightsPayload {
   client_id: string;
   timeframe: string[];
   chartData: {
      [key: string]: CampaignWithBudgetSpend[];
      spend: CampaignWithBudgetSpend[];
   };
}

export interface FetchBenchmarkInsightsPayload {
   client_id: string;
   timeframe: string[];
   chartData: {
      [key: string]: CampaignWithBudgetSpend[];
      spend: CampaignWithBudgetSpend[];
   };
   adsets: DaywiseAdsetKPIsCalculated[];
}

export interface FetchAdsetInsightsPayload {
   client_id: string;
   timeframe: string[];
   chartData: {
      [key: string]: CampaignWithBudgetSpend[];
      spend: CampaignWithBudgetSpend[];
   };
   adsets: DaywiseAdsetKPIsCalculated[];
}

export interface FetchTargetingInsightsPayload {
   client_id: string;
   campaign_id: string;
   adset_id: string;
   objective: string;
   targeting_kpis: TargetingKPIsTable;
}

/** QUERY RESULTS **/

export interface FetchMetaObjectivesQueryResult {
   fn_get_client_objectives: string[] | null;
}

export interface TrackedCampaignKPIsQueryResult {
   client_id: string;
   objective: string;
   campaign_id: string;
   kpi_name: string;
   tracked: boolean;
   channel: string;
}

export interface FetchCampaignsWithBudgetSpendQueryResult {
   fn_campaign_with_spend_budget: CampaignWithBudgetSpend | null;
}

export interface FetchCampaignDaywiseKPIsQueryResult {
   fn_campaign_daywise_with_multi_kpi_value_get: DaywiseCampaignKPIs[];
}

export interface FetchAdsetsDaywiseKPIsQueryResult {
   fn_adset_daywise_with_multi_kpi_value_get: DaywiseAdsetKPIs[];
}

export interface FetchAdsDaywiseKPIsQueryResult {
   fn_ad_daywise_with_multi_kpi_value_get: DaywiseAdsetKPIs[];
}

export interface FetchAdDaywiseKPIsQueryResult {
   fn_ad_daywise_with_multi_kpi_value_get: DaywiseAdKPIs[];
}

export interface FetchTargetingDaywiseKPIsQueryResult {
   fn_targeting_daywise_with_multi_kpi_value_get: DaywiseTargetingKPIs[];
}

/** RESPONSES **/

export interface FetchObjectivesResponse {
   data: string[] | null;
}

export interface FetchTrackedCampaignKPIsResponse {
   data: TrackedCampaignKPIsQueryResult[] | null;
}

export interface FetchCampaignWithBudgetSpendResponse {
   data: {
      [key: string]: CampaignWithBudgetSpend[];
   } | null;
}

export interface FetchCampaignDaywiseResponse {
   data: DaywiseCampaignKPIsCalculated[] | null;
}

export interface FetchAdsetsDaywiseKPIsResponse {
   data: DaywiseAdsetKPIsCalculated[] | null;
}

export interface FetchAdsDaywiseKPIsResponse {
   data: DaywiseAdKPIsCalculated[] | null;
}

export interface FetchTargetingDaywiseKPIsResponse {
   data: TargetingKPIsTable | null;
}

export interface FetchChartInsightsResponse {
   recommendation: string;
}

export interface FetchBenchmarkInsightsResponse {
   recommendation: string;
}

export interface FetchAdsetInsightsResponse {
   recommendation: string;
}

export interface FetchTargtingInsightsResponse {
   age: string;
   gender: string;
   placement: string;
   region: string;
   country: string;
}

/** ENDPOINTS **/

interface Endpoints {
   fetchMetaObjectives: (payload: {
      client_id: string;
   }) => Promise<AxiosResponse<FetchObjectivesResponse>>;

   fetchMetaTrackedCampaignKPIs: (payload: {
      client_id: string;
   }) => Promise<AxiosResponse<FetchTrackedCampaignKPIsResponse>>;

   fetchCampaignWithBudgetSpend: (
      payload: FetchCampaignsWithBudgetSpendPayload,
   ) => Promise<AxiosResponse<FetchCampaignWithBudgetSpendResponse>>;

   fetchMetaDaywiseCampaigns: (
      payload: FetchCampaignDaywisePayload,
   ) => Promise<AxiosResponse<FetchCampaignDaywiseResponse>>;

   fetchMetaAdsetsDaywise: (
      payload: FetchAdsetDaywisePayload,
   ) => Promise<AxiosResponse<FetchAdsetsDaywiseKPIsResponse>>;

   fetchMetaAdsDaywise: (
      payload: FetchAdsDaywisePayload,
   ) => Promise<AxiosResponse<FetchAdsDaywiseKPIsResponse>>;

   fetchMetaTargetingDaywise: (
      payload: FetchTargetingDaywisePayload,
   ) => Promise<AxiosResponse<FetchTargetingDaywiseKPIsResponse>>;

   fetchChartInsights: (
      payload: FetchChartInsightsPayload,
   ) => Promise<AxiosResponse<FetchChartInsightsResponse>>;

   fetchBenchmarkInsights: (
      payload: FetchBenchmarkInsightsPayload,
   ) => Promise<AxiosResponse<FetchBenchmarkInsightsResponse>>;

   fetchAdsetInsights: (
      payload: FetchAdsetInsightsPayload,
   ) => Promise<AxiosResponse<FetchAdsetInsightsResponse>>;

   fetchTargetingInsights: (
      payload: FetchTargetingInsightsPayload,
   ) => Promise<AxiosResponse<FetchTargtingInsightsResponse>>;
}

const pulseMetaEndpoints: Endpoints = {
   fetchMetaObjectives: (payload) =>
      dashboardApiAgent.get('performance-insights/meta/objectives', {
         params: { client_id: payload.client_id },
      }),

   fetchMetaTrackedCampaignKPIs: (payload) =>
      dashboardApiAgent.get('performance-insights/meta/campaigns/tracked', {
         params: { client_id: payload.client_id },
      }),

   fetchCampaignWithBudgetSpend: (payload) =>
      dashboardApiAgent.get(
         '/performance-insights/meta/campaigns/budget-spend',
         {
            params: payload,
         },
      ),

   fetchMetaDaywiseCampaigns: (payload) =>
      dashboardApiAgent.get('/performance-insights/meta/campaigns', {
         params: payload,
      }),

   fetchMetaAdsetsDaywise: (payload) =>
      dashboardApiAgent.get('/performance-insights/meta/adsets', {
         params: payload,
      }),

   fetchMetaAdsDaywise: (payload) =>
      dashboardApiAgent.get('/performance-insights/meta/ads', {
         params: payload,
      }),

   fetchMetaTargetingDaywise: (payload) =>
      dashboardApiAgent.get('/performance-insights/meta/targeting', {
         params: payload,
      }),

   fetchChartInsights: (payload) =>
      dashboardApiAgent.post(
         '/performance-insights/meta/chart-insights',
         payload,
      ),

   fetchBenchmarkInsights: (payload) =>
      dashboardApiAgent.post(
         '/performance-insights/meta/benchmark-insights',
         payload,
      ),

   fetchAdsetInsights: (payload) =>
      dashboardApiAgent.post(
         '/performance-insights/meta/adset-insights',
         payload,
      ),

   fetchTargetingInsights: (payload) =>
      dashboardApiAgent.post(
         '/performance-insights/meta/targeting-insights',
         payload,
      ),
};

export default pulseMetaEndpoints;
