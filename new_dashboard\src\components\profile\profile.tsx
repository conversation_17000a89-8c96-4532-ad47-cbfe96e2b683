import {
   Button,
   Flex,
   Text,
   Divider,
   useToast,
   Popover,
   PopoverTrigger,
   Portal,
   PopoverContent,
   PopoverBody,
   Avatar,
   PopoverArrow,
   useColorModeValue,
} from '@chakra-ui/react';
import { AiOutlineUser } from 'react-icons/ai';
import { FiLogOut } from 'react-icons/fi';
import { AiOutlineUsergroupAdd } from 'react-icons/ai';
import { MdOutlineSwitchAccount } from 'react-icons/md';
import { useNavigate } from 'react-router-dom';
import { Keys, LocalStorageService } from '../../utils/local-storage';
import {
   logoutAction,
   useAppDispatch,
   useAppSelector,
} from '../../store/store';
import { AuthUser } from '../../types/auth';
import { profileStrings } from '../../utils/strings/profile-strings';
import { useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';

function ProfilePopup() {
   const userDetails = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   );

   const navigate = useNavigate();
   const dispatch = useAppDispatch();
   const toast = useToast();
   const queryClient = useQueryClient();

   const initRef = useRef(null);

   const { generalSettings } = useAppSelector((state) => state.settings);

   const getUserName = () => {
      if (!userDetails) return '';

      return userDetails.email.split('@')[0];
   };

   const handleManageUsers = (onClose: () => void) => {
      onClose();
      navigate('/user/manage-users');
   };

   const handleClearCache = () => {
      queryClient.clear();
      sessionStorage.clear();
      dispatch(logoutAction());
   };

   const handleSwitchAccount = (onClose: () => void) => {
      onClose();
      handleClearCache();
      LocalStorageService.removeItem(Keys.FlableUserDetails);
      LocalStorageService.removeItem(Keys.ClientId);
      navigate('/auth/choose-profile');
   };

   const handleLogout = () => {
      handleClearCache();
      localStorage.clear();
      navigate('/auth/login');
      toast({
         title: 'Logout successful.',
         description: 'You have successfully logged out.',
         status: 'success',
         duration: 5000,
         isClosable: true,
      });
   };

   return (
      <>
         <Popover closeOnBlur placement='bottom-end' initialFocusRef={initRef}>
            {({ onClose }) => (
               <>
                  <PopoverTrigger>
                     <Avatar
                        size='sm'
                        src={
                           `${generalSettings?.profile_image}` ||
                           'https://bit.ly/broken-link'
                        }
                     />
                  </PopoverTrigger>
                  <Portal>
                     <PopoverContent width='200px'>
                        <PopoverArrow />
                        <PopoverBody>
                           <Flex
                              direction='column'
                              align='center'
                              alignContent='center'
                              alignItems='center'
                           >
                              <Flex align='center' mb={2}>
                                 <AiOutlineUser size={40} color='gray' />
                              </Flex>
                              <Flex align='center' mb={2}>
                                 <Text
                                    fontWeight='bold'
                                    color={useColorModeValue('black', 'white')}
                                 >
                                    {getUserName()}
                                 </Text>
                              </Flex>
                              <Divider color='gray.700' />
                              <Flex
                                 align='center'
                                 mt={2}
                                 direction='column'
                                 gap={2}
                              >
                                 {userDetails?.user_role === 'Admin' && (
                                    <Button
                                       width='100%'
                                       leftIcon={<AiOutlineUsergroupAdd />}
                                       colorScheme='gray'
                                       variant='outline'
                                       onClick={() =>
                                          handleManageUsers(onClose)
                                       }
                                       justifyContent='start'
                                    >
                                       {profileStrings.manageUsers}
                                    </Button>
                                 )}
                                 <Button
                                    width='100%'
                                    leftIcon={<MdOutlineSwitchAccount />}
                                    colorScheme='gray'
                                    variant='outline'
                                    onClick={() => handleSwitchAccount(onClose)}
                                    justifyContent='start'
                                 >
                                    {profileStrings.switchAccount}
                                 </Button>
                                 <Button
                                    width='100%'
                                    leftIcon={<FiLogOut />}
                                    colorScheme='gray'
                                    variant='outline'
                                    onClick={handleLogout}
                                    justifyContent='start'
                                 >
                                    {profileStrings.logout}
                                 </Button>
                              </Flex>
                           </Flex>
                        </PopoverBody>
                     </PopoverContent>
                  </Portal>
               </>
            )}
         </Popover>
      </>
   );
}

export default ProfilePopup;
