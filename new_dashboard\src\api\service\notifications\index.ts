import { AxiosResponse } from 'axios';
import dashboardApiAgent from '../../agent';

/** MISCELLANEOUS */

export interface Notification {
   id: string;
   client_id: string;
   user_id: string;
   notification_title: string;
   notification_message: string;
   notification_type: string;
   notification_data: {
      session_id: string;
      chat_id: string;
      mode: 'data-analyst' | 'cmo';
   };
   is_read: boolean;
   read_at: Date | null;
   created_at: Date;
   updated_at: Date;
}

/** PAYLOADS **/

export interface FetchAllNotificationsByUserIDPayload {
   user_id: string;
   client_id: string;
}

export interface CreateNotificationPayload {
   client_id: string;
   user_id: string;
   notification_title: string;
   notification_message: string;
   notification_type: string;
   notification_data: {
      session_id: string;
      chat_id: string;
      mode: 'data-analyst' | 'cmo';
   };
}

export interface MarkNotificationAsReadPayload {
   user_id: string;
   client_id: string;
   id: string;
}

interface Endpoints {
   fetchAllNotificationsByUserID: (
      payload: FetchAllNotificationsByUserIDPayload,
   ) => Promise<AxiosResponse<Notification[]>>;

   createNotification: (
      payload: CreateNotificationPayload,
   ) => Promise<AxiosResponse<void>>;

   markNotificationAsRead: (
      payload: MarkNotificationAsReadPayload,
   ) => Promise<AxiosResponse<void>>;
}

const notificationsEndpoints: Endpoints = {
   fetchAllNotificationsByUserID: (payload) =>
      dashboardApiAgent.get(
         `notifications/${payload.client_id}/${payload.user_id}/notifications`,
      ),

   createNotification: (payload) =>
      dashboardApiAgent.post(
         `notifications/${payload.client_id}/${payload.user_id}/notifications`,
         payload,
      ),

   markNotificationAsRead: (payload) =>
      dashboardApiAgent.put(
         `notifications/${payload.client_id}/${payload.user_id}/notifications/${payload.id}/read`,
      ),
};

export default notificationsEndpoints;
