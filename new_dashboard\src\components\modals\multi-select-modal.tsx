import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { useAppSelector } from '../../store/store';
import { useDispatch } from 'react-redux';
import { closeModal } from '../../store/reducer/modal-reducer';

interface MultiSelectModalProps {
  title?: string;
  options: Record<string, string>;
  onSelect: (selectedValues: string[] | null) => void;
  confirmButtonText?: string;
  showCancelButton?: boolean;
  minSelections?: number;
}

const MultiSelectModal: React.FC = () => {
  const dispatch = useDispatch();
  const { payload } = useAppSelector((state) => state.modal);

  const {
    title = 'Select Items',
    options = {},
    onSelect,
    confirmButtonText = 'Save',
    showCancelButton = true,
    minSelections = 1,
  } = (payload?.modalProps || {}) as MultiSelectModalProps;

  const [selectedValues, setSelectedValues] = useState<string[]>([]);
  const [error, setError] = useState<string>('');

  const handleCheckboxChange = (value: string, checked: boolean) => {
    let newSelectedValues: string[];

    if (checked) {
      newSelectedValues = [...selectedValues, value];
    } else {
      newSelectedValues = selectedValues.filter(v => v !== value);
    }

    setSelectedValues(newSelectedValues);

    // Clear error if minimum selections are met
    if (error && newSelectedValues.length >= minSelections) {
      setError('');
    }
  };

  const handleConfirm = () => {
    if (selectedValues.length < minSelections) {
      setError(`Please select at least ${minSelections} item${minSelections > 1 ? 's' : ''}`);
      return;
    }

    onSelect(selectedValues);
    dispatch(closeModal());
  };

  const handleCancel = () => {
    onSelect(null);
    dispatch(closeModal());
  };

  return (
    <Dialog open onOpenChange={() => handleCancel()}>
      <DialogContent className="max-w-md bg-white dark:bg-zinc-900 shadow-lg rounded-lg z-50">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>

        <div className="space-y-2 max-h-60 overflow-y-auto border rounded-md px-3 py-2">
          {Object.entries(options).map(([value, label]) => (
            <div
              key={value}
              className={`flex items-center space-x-2 px-3 py-2 rounded-md hover:bg-muted cursor-pointer ${
                selectedValues.includes(value) ? 'bg-blue-50 dark:bg-blue-900/20' : ''
              }`}
              onClick={() => handleCheckboxChange(value, !selectedValues.includes(value))}
            >
              <Checkbox
                id={value}
                checked={selectedValues.includes(value)}
                onCheckedChange={(checked) => handleCheckboxChange(value, checked as boolean)}
              />
              <label
                htmlFor={value}
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer flex-1"
              >
                {label}
              </label>
            </div>
          ))}
        </div>

        {error && (
          <p className="text-sm text-red-500 mt-1">
            ⚠ {error}
          </p>
        )}

        <DialogFooter>
          {showCancelButton && (
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
          )}
          <Button
            onClick={handleConfirm}
            disabled={selectedValues.length < minSelections}
          >
            {confirmButtonText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default MultiSelectModal;
