import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { useAppSelector } from '../../store/store';
import { useDispatch } from 'react-redux';
import { closeModal } from '../../store/reducer/modal-reducer';

interface MultiSelectModalProps {
  title?: string;
  options: Record<string, string>;
  onSelect: (selectedValues: string[] | null) => void;
  confirmButtonText?: string;
  showCancelButton?: boolean;
  minSelections?: number;
}

const MultiSelectModal: React.FC = () => {
  const dispatch = useDispatch();
  const { payload } = useAppSelector((state) => state.modal);

  const {
    title = 'Select Items',
    options = {},
    onSelect,
    confirmButtonText = 'Save',
    showCancelButton = true,
    minSelections = 1,
  } = (payload?.modalProps || {}) as MultiSelectModalProps;

  const [selectedValues, setSelectedValues] = useState<string[]>([]);
  const [error, setError] = useState<string>('');

  const handleCheckboxChange = (value: string, checked: boolean) => {
    let newSelectedValues: string[];

    if (checked) {
      newSelectedValues = [...selectedValues, value];
    } else {
      newSelectedValues = selectedValues.filter(v => v !== value);
    }

    setSelectedValues(newSelectedValues);

    // Clear error if minimum selections are met
    if (error && newSelectedValues.length >= minSelections) {
      setError('');
    }
  };

  const handleConfirm = () => {
    if (selectedValues.length < minSelections) {
      setError(`Please select at least ${minSelections} item${minSelections > 1 ? 's' : ''}`);
      return;
    }

    onSelect(selectedValues);
    dispatch(closeModal());
  };

  const handleCancel = () => {
    onSelect(null);
    dispatch(closeModal());
  };

  return (
    <Dialog open onOpenChange={() => handleCancel()}>
      <DialogContent className="max-w-lg bg-white dark:bg-zinc-900 shadow-2xl rounded-xl border-0 z-50 p-0 overflow-hidden">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-zinc-800 dark:to-zinc-700 px-6 py-4 border-b border-gray-100 dark:border-zinc-600">
          <DialogHeader className="space-y-0">
            <DialogTitle className="text-xl font-semibold text-gray-900 dark:text-white">
              {title}
            </DialogTitle>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Select {minSelections > 1 ? `at least ${minSelections} items` : 'one or more items'}
            </p>
          </DialogHeader>
        </div>

        <div className="px-6 py-6">
          <div className="space-y-1 max-h-80 overflow-y-auto border border-gray-200 dark:border-zinc-600 rounded-lg bg-gray-50 dark:bg-zinc-800/50 p-2">
            {Object.entries(options).map(([value, label]) => (
              <div
                key={value}
                className={`flex items-center space-x-3 px-4 py-3 rounded-lg cursor-pointer transition-all duration-200 border-2 ${
                  selectedValues.includes(value)
                    ? 'bg-blue-500 border-blue-500 text-white shadow-md transform scale-[1.02]'
                    : 'bg-white dark:bg-zinc-800 border-gray-200 dark:border-zinc-600 hover:border-blue-300 hover:bg-blue-50 dark:hover:bg-zinc-700 text-gray-900 dark:text-white'
                }`}
                onClick={() => handleCheckboxChange(value, !selectedValues.includes(value))}
              >
                <div className="flex-shrink-0">
                  <Checkbox
                    id={value}
                    checked={selectedValues.includes(value)}
                    onCheckedChange={(checked) => handleCheckboxChange(value, checked as boolean)}
                    className={`w-5 h-5 ${
                      selectedValues.includes(value)
                        ? 'border-white data-[state=checked]:bg-white data-[state=checked]:text-blue-500'
                        : 'border-gray-300 dark:border-zinc-500'
                    }`}
                  />
                </div>
                <label
                  htmlFor={value}
                  className={`text-sm font-medium leading-relaxed cursor-pointer flex-1 ${
                    selectedValues.includes(value)
                      ? 'text-white'
                      : 'text-gray-900 dark:text-white'
                  }`}
                >
                  {label}
                </label>
                {selectedValues.includes(value) && (
                  <div className="flex-shrink-0">
                    <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {selectedValues.length > 0 && (
            <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-blue-800 dark:text-blue-300">
                  {selectedValues.length} item{selectedValues.length !== 1 ? 's' : ''} selected
                </span>
                <button
                  onClick={() => setSelectedValues([])}
                  className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 font-medium underline"
                >
                  Clear all
                </button>
              </div>
            </div>
          )}

          {error && (
            <div className="flex items-center space-x-2 mt-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
              <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-white text-xs font-bold">!</span>
              </div>
              <p className="text-sm text-red-700 dark:text-red-400 font-medium">
                {error}
              </p>
            </div>
          )}
        </div>

        <div className="px-6 py-4   dark:border-zinc-600">
          <DialogFooter className="flex justify-end space-x-3">
            {showCancelButton && (
              <Button
                variant="outline"
                onClick={handleCancel}
                className="px-6 py-2 border-gray-300 dark:border-zinc-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-zinc-700 transition-colors duration-200"
              >
                Cancel
              </Button>
            )}
            <Button
              onClick={handleConfirm}
              disabled={selectedValues.length < minSelections}
              className={`px-6 py-2 font-medium transition-all duration-200 ${
                selectedValues.length < minSelections
                  ? 'bg-gray-300 dark:bg-zinc-600 text-gray-500 dark:text-zinc-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'
              }`}
            >
              {confirmButtonText} {selectedValues.length > 0 && `(${selectedValues.length})`}
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default MultiSelectModal;
