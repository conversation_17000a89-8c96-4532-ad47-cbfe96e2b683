import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";

export default function MultiSelectModal() {
  const options = { "316278400": "karmikh [316278400]","3162784800": "karmikh [316278400]" };

  return (
    <Dialog open>
      <DialogContent className="max-w-md bg-white dark:bg-zinc-900 shadow-lg rounded-lg z-50">
        <DialogHeader>
          <DialogTitle>Select GA Properties</DialogTitle>
        </DialogHeader>

        <div className="space-y-2 max-h-60 overflow-y-auto border rounded-md px-3 py-2">
          {Object.entries(options).map(([value, label]) => (
            <div
              key={value}
              className="flex items-center space-x-2 px-3 py-2 rounded-md hover:bg-muted"
            >
              <Checkbox id={value} />
              <label
                htmlFor={value}
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                {label}
              </label>
            </div>
          ))}
        </div>

        {/* Error message */}
        <p className="text-sm text-red-500 mt-1">
          ⚠ Please add at least one ads account
        </p>

        <DialogFooter>
          <Button variant="outline">Cancel</Button>
          <Button>Save</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
