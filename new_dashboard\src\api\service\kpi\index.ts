import { EndPoints } from '../../../pages/dashboard/utils/interface';
import dashboardApiAgent from '../../agent';

const endPoints: EndPoints = {
   getKpiData: async (payload) => {
      const data = await dashboardApiAgent.post('/kpi/data', payload);
      return data;
   },
   getKpiSummary: async (payload) => {
      const summary = await dashboardApiAgent.post('/kpi/summary', payload);
      return summary;
   },
   getKpiMeta: async (clientId) => {
      const metaData = await dashboardApiAgent.get(`/kpi/meta/${clientId}`);
      return metaData;
   },
   updatePinned: async (payload) => {
      const res = await dashboardApiAgent.post('/kpi/pinned', payload);
      return res;
   },
   updateVisible: async (payload) => {
      const res = await dashboardApiAgent.post('/kpi/visible', payload);
      return res;
   },
   updatePinVisibleOrder: async (payload) => {
      const res = await dashboardApiAgent.post('/kpi/order', payload);
      return res;
   },
   getAnomalyCause: async (payload) => {
      const res = await dashboardApiAgent.post(
         '/kpi/anomaly-root-cause',
         payload,
      );
      return res;
   },
};

export default endPoints;
