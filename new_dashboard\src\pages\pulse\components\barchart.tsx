import React, { useEffect, useState } from 'react';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import { getChartDateLabel } from '../../dashboard/utils/helpers';
import { TrackKPI } from './interface';
import { useColorMode } from '@chakra-ui/react';
import { StatusTypes } from '../utils/helper';

interface ChartProp {
   kpiDetails: {
      displayName: string;
      allData: TrackKPI[];
      stat: string;
   };
}

const BarChart: React.FC<ChartProp> = ({ kpiDetails }) => {
   const chartColor = [StatusTypes.ACTIVE, StatusTypes.ENABLED].includes(
      kpiDetails.stat,
   )
      ? '#15994A'
      : undefined;
   const categories = getChartDateLabel(kpiDetails.allData, 'day');
   const colorMode = useColorMode().colorMode;
   const [chartData, setChartData] = useState({
      options: {
         chart: {
            id: kpiDetails.displayName,
            toolbar: {
               show: true,
            },
         },
         xaxis: {
            categories: categories,
            labels: {
               show: true,
               format: 'dd MMM',
               style: {
                  colors: colorMode === 'dark' ? '#fff' : '#000',
               },
            },
            tickAmount: 30,
         },
         yaxis: {
            labels: {
               show: true,
               style: {
                  colors: colorMode === 'dark' ? '#fff' : '#000',
               },
            },
         },
         plotOptions: {
            bar: {
               borderRadius: 4,
               horizontal: false,
            },
         },
         dataLabels: {
            enabled: false,
         },
         grid: {
            show: false,
         },
         colors: chartColor ? [chartColor] : undefined,
      },
      series: [
         {
            name: kpiDetails.displayName,
            data: kpiDetails.allData.map((x: TrackKPI) => x.kpi_value),
         },
      ],
   });

   useEffect(() => {
      setChartData({
         options: {
            chart: {
               id: kpiDetails.displayName,
               toolbar: {
                  show: false,
               },
            },
            xaxis: {
               categories: categories,
               labels: {
                  show: true,
                  format: 'dd MMM',
                  style: {
                     colors: colorMode === 'dark' ? '#fff' : '#000',
                  },
               },
               tickAmount: 30,
            },
            yaxis: {
               labels: {
                  show: true,
                  style: {
                     colors: colorMode === 'dark' ? '#fff' : '#000',
                  },
               },
            },
            plotOptions: {
               bar: {
                  borderRadius: 4,
                  horizontal: false,
               },
            },
            dataLabels: {
               enabled: false,
            },
            grid: {
               show: false,
            },
            colors: chartColor ? [chartColor] : undefined,
         },
         series: [
            {
               name: kpiDetails.displayName,
               data: kpiDetails.allData.map((x: TrackKPI) => x.kpi_value),
            },
         ],
      });
   }, [kpiDetails]);

   return (
      <Chart
         options={chartData.options as ApexOptions}
         series={chartData.series}
         type='bar'
         style={{ width: '100%', height: '100%' }}
         width='100%'
         height='100%'
      />
   );
};

export default BarChart;
