import {
   Attributes,
   SubscriptionRecord,
} from '@/pages/settings/subscription/types';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface InitialState {
   isActive: boolean | null;
   autoRenew: boolean;
   subscriptionId: string;
   planId: string;
   planName: string;
   rzrPay_SubsId: string;
   isYearly: boolean;
   status: string;
   endDate: string;
   amount: string;
   access: Partial<Attributes>;
   users: number | null;
}

const initialState: InitialState = {
   isActive: null,
   autoRenew: false,
   subscriptionId: '',
   planId: '',
   planName: '',
   rzrPay_SubsId: '',
   isYearly: false,
   status: '',
   endDate: '',
   amount: '',
   access: {},
   users: null,
};

const subscriptionSlice = createSlice({
   name: 'subscription',
   initialState,
   reducers: {
      setSubscription: (state, action: PayloadAction<SubscriptionRecord>) => {
         state.amount = action.payload.amount;
         state.isActive = action.payload.is_active;
         state.autoRenew = action.payload.auto_renew;
         state.subscriptionId = action.payload.id;
         state.planId = action.payload.plan_id;
         state.planName = action.payload.plan_name;
         state.rzrPay_SubsId = action.payload.razorpay_subscription_id;
         state.isYearly = action.payload.is_yearly;
         state.status = action.payload.status;
         state.access = action.payload.attribute;
         state.users = action.payload.max_users ?? null;
         state.endDate = action.payload.subs_end_dt;
      },
      clearSubscription: (state) => {
         Object.assign(state, initialState);
      },
   },
});
// console.log("TCL: initialState", initialState)

export const { setSubscription, clearSubscription } = subscriptionSlice.actions;

export default subscriptionSlice.reducer;
