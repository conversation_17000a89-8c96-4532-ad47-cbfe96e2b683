import Swal, { <PERSON>AlertResult } from 'sweetalert2';

/*export async function showMultiSelect(
   title: string,
   inputOptions: Record<string, string>,
): Promise<string[] | null> {
   const optionsHtml = Object.entries(inputOptions)
      .map(([value, label]) => `<option value="${value}">${label}</option>`)
      .join('\n');

   const result: SweetAlertResult<string[]> = await Swal.fire({
      title,
      html: `
      <select id="swal-multi-select" multiple size="6" style="width: 100%; padding: 4px; border-radius: 6px;">
        ${optionsHtml}
      </select>
    `,
      focusConfirm: false,
      //showCancelButton: true,
      confirmButtonText: 'Save',
      preConfirm: () => {
         const select = document.getElementById(
            'swal-multi-select',
         ) as HTMLSelectElement;
         const selectedValues = Array.from(select.selectedOptions).map(
            (opt) => opt.value,
         );
         if (selectedValues.length === 0) {
            Swal.showValidationMessage('Please select at least one property.');
            return;
         }
         return selectedValues;
      },
   });

   return result.isConfirmed ? (result.value ?? []) : null;
}*/
export async function showMultiSelect(
   title: string,
   inputOptions: Record<string, string>,
): Promise<string[] | null> {
   const optionsHtml = Object.entries(inputOptions)
      .map(
         ([value, label]) =>
            `<option value="${value}" style="padding: 8px;">${label}</option>`,
      )
      .join('\n');

   const result: SweetAlertResult<string[]> = await Swal.fire({
      title: `<div style="font-size: 1.2rem; margin-bottom: 10px;">${title}</div>`,
      html: `
      <div style="margin-top: 10px;">
        <select 
          id="swal-multi-select" 
          multiple 
          size="6" 
          style="
            width: 100%;
            height: 160px;
            padding: 10px;
            font-size: 1rem;
            border: 1px solid #ccc;
            border-radius: 8px;
            outline: none;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
          ">
          ${optionsHtml}
        </select>
      </div>
    `,
      background: '#fff',
      confirmButtonText: 'Save',
      confirmButtonColor: '#4f46e5',
      cancelButtonColor: '#d33',
      //showCancelButton: true,
      focusConfirm: false,
      customClass: {
         popup: 'custom-multi-select-modal',
      },
      preConfirm: () => {
         const select = document.getElementById(
            'swal-multi-select',
         ) as HTMLSelectElement;
         const selectedValues = Array.from(select.selectedOptions).map(
            (opt) => opt.value,
         );
         if (selectedValues.length === 0) {
            Swal.showValidationMessage('Please select at least one property.');
            return;
         }
         return selectedValues;
      },
   });

   return result.isConfirmed ? (result.value ?? []) : null;
}

/*export async function showAccountSelect(
   options: Record<string, string>,
): Promise<string | null> {
   const res = await Swal.fire({
      title: 'Select GA Account',
      input: 'select',
      inputOptions: options,
      inputPlaceholder: 'Choose an account',
      confirmButtonText: 'Next',
      //  showCancelButton: true,
   });

   return res.isConfirmed ? (res.value as string) : null;
}*/

export async function showAccountSelect(
   options: Record<string, string>,
): Promise<string | null> {
   const optionsHtml = Object.entries(options)
      .map(([value, label]) => `<option value="${value}">${label}</option>`)
      .join('');

   const result = await Swal.fire({
      title: 'Select GA Account',
      html: `
      <select id="account-select" style="
        width: 100%;
        padding: 10px;
        border-radius: 6px;
        font-size: 1rem;
        border: 1px solid #ccc;
      ">
        <option value="" disabled selected>Select an account</option>
        ${optionsHtml}
      </select>
    `,
      focusConfirm: false,
      showCancelButton: true,
      confirmButtonText: 'Next',
      preConfirm: () => {
         const selectEl = document.getElementById(
            'account-select',
         ) as HTMLSelectElement;
         const selected = selectEl?.value;
         if (!selected) {
            Swal.showValidationMessage('Please select an account');
            return;
         }
         return selected;
      },
   });

   return result.isConfirmed ? (result.value as string) : null;
}
