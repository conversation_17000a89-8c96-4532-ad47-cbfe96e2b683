import subsEndPoints from '@/api/service/subscription';
import { useApiQuery } from '@/hooks/react-query-hooks';
import { UserDetails } from '@/pages/socialwatch/interface';
import { setSubscription } from '@/store/reducer/subscriptionReducer';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { useDispatch } from 'react-redux';

const Subscription = () => {
   const dispatch = useDispatch();

   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);

   const { data: subscriptionRecord } = useApiQuery({
      queryKey: ['getTopupRecords', userDetails?.client_id],
      queryFn: () =>
         subsEndPoints.getSubscriptionContractRecords(userDetails?.client_id),
      enabled: !!userDetails?.client_id,
      refetchOnWindowFocus: true,
   });

   if (
      subscriptionRecord &&
      subscriptionRecord.success &&
      subscriptionRecord.subscription &&
      subscriptionRecord.subscription.length
   ) {
      dispatch(setSubscription(subscriptionRecord.subscription[0]));
   }
};

export default Subscription;
