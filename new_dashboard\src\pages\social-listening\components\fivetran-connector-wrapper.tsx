import React, { useEffect, useState } from 'react';
import Swal from 'sweetalert2';
import Card from './Card';
import endPoints, { FivetranSourceOut } from '../apis/agent';
import { useApiMutation, useApiQuery } from '../../../hooks/react-query-hooks';
import { channelNames, FiVETRAN_CONNECTORS } from '../utils/constant';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { useToast } from '@chakra-ui/react';
import { dialogMessage } from '../../../utils/strings/content-manager';
import { AuthUser } from '../../../types/auth';
import { AxiosResponse } from 'axios';
import { ApiError } from './facebook-ads-form';
import { useAppDispatch } from '../../../store/store';
import { closeModal, openModal } from '../../../store/reducer/modal-reducer';
import { modalTypes } from '../../../components/modals/modal-types';
import { useLocation } from 'react-router-dom';

interface FivetranConnectorWrapperProps {
   channelType: keyof typeof FiVETRAN_CONNECTORS;
   heading: string;
   imageSrc: string;
   connectToSentimentFn: (
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      props: any,
   ) => Promise<AxiosResponse<{ message: string }>>;
   modalData: { heading: string; content: string };
}

const integrationPrefix = 'integration_';
const FivetranConnectorWrapper: React.FC<FivetranConnectorWrapperProps> = ({
   channelType,
   heading,
   imageSrc,
   connectToSentimentFn,
   modalData,
}) => {
   const dispatch = useAppDispatch();
   const location = useLocation();

   const authUser = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   );
   const [isDisconnecting, setIsDisconnecting] = useState(false);
   const [isVendor, setIsVendor] = useState(false);
   const toast = useToast();

   const redirectPathName = location.pathname.includes('onboarding')
      ? 'onboarding'
      : 'integrations';

   // Get channel-specific details
   const channelName = channelNames[channelType];
   const sourceType = FiVETRAN_CONNECTORS[channelType];

   // Fetch connection details
   const { data, isLoading, errorMessage } = useApiQuery({
      queryKey: [`${channelType}ConnectionDetails`],
      queryFn: () =>
         endPoints.checkConnectionDetails({
            client_id: authUser?.client_id || '',
            channel_name: channelName,
         }),
   });

   const {
      is_active = false,
      actual_account_name = '',
      meta_data,
   } = data?.details || {};

   // Mutations for integration steps
   const {
      mutate: createConnectCard,
      isPending: isConnectCardPending,
      errorMessage: cardError,
   } = useApiMutation({
      mutationFn: endPoints.createConnectCardFivetran,
      onSuccessHandler: ({ uri }) => {
         if (uri) {
            const newTab = window.open('', '_self');
            if (newTab) newTab.location.href = uri;
         }
      },
   });

   const {
      mutate: createSource,
      isPending: isCreatingSource,
      errorMessage: sourceError,
   } = useApiMutation({
      mutationFn: () =>
         endPoints.createSourceFivetran({
            clientId: authUser?.client_id?.toLowerCase() || '',
            sourceType,
            metaData: { isVendor: isVendor },
         }),
      onSuccessHandler: ({ sourceDetails }) =>
         handleConnectionSuccess(sourceDetails),
   });

   const { mutate: connectToSentiment, isPending: isConnectingToSentiment } =
      useApiMutation({
         mutationFn: connectToSentimentFn,
         onSuccessHandler: (data) => {
            console.log('sentiment connection added ', data);
            setTimeout(() => {
               window.location.href = `${window.location.origin}/${redirectPathName}`;
            }, 1000);
         },
      });

   const handleConnectionSuccess = (sourceDetails: FivetranSourceOut) => {
      const { id, ok, msg } = sourceDetails;
      if (ok)
         createConnectCard({
            sourceId: id!,
            redirectPathName: redirectPathName,
         });
      else showToast('Connection failed', msg, 'error');
   };

   const onConnectClick = async (): Promise<void> => {
      try {
         if (sourceType === 'amazon_selling_partner') {
            const result = await Swal.fire({
               title: 'Is this account a vendor?',
               text: 'Please confirm before continuing.',
               icon: 'question',
               showDenyButton: true,
               showCancelButton: false,
               confirmButtonColor: '#3085d6',
               confirmButtonText: 'Yes, Vendor',
               denyButtonText: 'No, Not a Vendor',
               allowOutsideClick: false,
               allowEscapeKey: true,
            });

            if (result.isConfirmed) {
               setIsVendor(true);
               handleConnect();
            } else if (result.isDenied) {
               setIsVendor(false);
               handleConnect();
            }
            return;
         }

         handleConnect();
      } catch (err) {
         console.error('Error in onConnectClick:', err);
      }
   };

   const handleConnect = () => {
      // Remove the existing channel from local storage
      LocalStorageService.removeKeys(integrationPrefix);

      // need to save the channel name in local storage, so that we can connect to the channel
      LocalStorageService.setItem(
         `${integrationPrefix}${channelName}` as Keys,
         true,
      );
      createSource({});
   };

   const handleDisconnect = async () => {
      const result = await Swal.fire({
         title: dialogMessage.delete.title,
         text: dialogMessage.delete.description,
         icon: 'warning',
         showCancelButton: true,
         confirmButtonColor: '#3085d6',
         cancelButtonColor: '#d33',
         confirmButtonText: dialogMessage.delete.buttonMessage,
      });
      if (result.isConfirmed && meta_data) {
         try {
            setIsDisconnecting(true);
            await endPoints.deleteConnector({ sourceId: meta_data.source_id });
            connectToSentiment({
               channel_name: channelName,
               client_id: authUser?.client_id,
               isConnect: false,
            });
         } catch (err) {
            const error = err as ApiError;
            const msg = error.response.data.message;
            showToast('Could not disconnect', msg!, 'error');
         } finally {
            setIsDisconnecting(false);
         }
      }
   };

   const showToast = (
      title: string,
      description: string,
      status: 'success' | 'error',
   ) => {
      toast({ title, description, status, duration: 2000, isClosable: true });
   };

   useEffect(() => {
      const sourceId = new URLSearchParams(window.location.search).get('id');
      const isChannel = Boolean(
         LocalStorageService.getItem(
            `${integrationPrefix}${channelName}` as Keys,
         ),
      );
      if (sourceId && authUser && isChannel === true) {
         connectToSentiment({
            channel_name: channelName,
            client_id: authUser.client_id,
            source_id: sourceId,
         });
      }
   }, [authUser?.client_id]);

   // show a modal::
   useEffect(() => {
      if (isCreatingSource || isConnectCardPending) {
         dispatch(
            openModal({
               modalType: modalTypes.FIVETRAN_CONNECTOR_MODAL,
               modalProps: {
                  heading: modalData.heading,
                  content: modalData.content,
               },
            }),
         );
      } else {
         if (cardError || sourceError) dispatch(closeModal());
      }
   }, [isCreatingSource, isConnectCardPending]);

   return (
      <Card
         heading={actual_account_name || heading}
         src={imageSrc}
         isFetching={isLoading}
         isConnected={is_active}
         isConnecting={
            isCreatingSource || isConnectCardPending || isConnectingToSentiment
         }
         error={errorMessage}
         isDisconnecting={isDisconnecting}
         onButtonClick={
            is_active
               ? handleDisconnect
               : () => {
                    void onConnectClick();
                 }
         }
      />
   );
};

export default FivetranConnectorWrapper;
