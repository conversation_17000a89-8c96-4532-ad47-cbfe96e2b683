import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>heckCircleFill } from 'react-icons/pi';
import { Button } from '@/components/ui/button';
import {
   Popover,
   PopoverContent,
   PopoverTrigger,
} from '@/components/ui/popover';
import {
   useFetchAllNotificationsByUserID,
   useMarkNotificationAsReadMutation,
} from './notifications-apis';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { AuthUser } from '@/types/auth';
import { cn } from '@/utils';
import { setCurrentAgent } from '@/store/reducer/marco-reducer';
import { useNavigate } from 'react-router';
import {
   setCurrentMode,
   setCurrentSessionID,
} from '@/store/reducer/analytics-agent-reducer';
import { useAppDispatch } from '@/store/store';

const NotificationsPopover = () => {
   const navigate = useNavigate();
   const dispatch = useAppDispatch();

   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) ?? {};

   const { data: notifications, refetch: refetchNotifications } =
      useFetchAllNotificationsByUserID();

   const { mutateAsync: markAsRead } = useMarkNotificationAsReadMutation();

   const handleViewAnalysis = async (
      session_id: string,
      id: string,
      mode: 'data-analyst' | 'cmo',
   ) => {
      navigate('/marco/analytics-agent');
      dispatch(setCurrentAgent('analytics-agent'));
      dispatch(setCurrentMode(mode));
      dispatch(setCurrentSessionID(session_id));

      await markAsRead({
         client_id: client_id || '',
         user_id: user_id || '',
         id: id || '',
      });

      await refetchNotifications();
   };

   const handleMarkAsRead = async (id: string) => {
      await markAsRead({
         client_id: client_id || '',
         user_id: user_id || '',
         id: id || '',
      });

      await refetchNotifications();
   };

   return (
      <Popover>
         <PopoverTrigger>
            <Button variant='outline' className='relative'>
               <PiBell />
               {notifications &&
                  notifications.length > 0 &&
                  notifications.filter((n) => !n.is_read).length > 0 && (
                     <span className='absolute top-[-5px] right-0 rounded-full bg-blue-500 text-white text-xs px-1'>
                        {notifications.filter((n) => !n.is_read).length}
                     </span>
                  )}
            </Button>
         </PopoverTrigger>
         <PopoverContent
            side='bottom'
            className='w-100 max-h-[600px] overflow-y-auto bg-white overflow-x-auto'
         >
            <div className='p-1 font-semibold text-lg'>Notifications</div>
            {notifications && notifications.length > 0 ? (
               notifications.map((notification) => (
                  <div
                     key={notification.id}
                     className={cn(
                        'border-b rounded-sm last:border-0 h-24 mt-1',
                        {
                           'bg-[#E4F2FF]': !notification.is_read,
                        },
                     )}
                  >
                     <div className='px-1 py-2 flex items-start gap-2'>
                        <div>
                           <PiCheckCircleFill
                              size={24}
                              style={{
                                 color: '#ffffff',
                                 fill: '#3C76E1',
                                 marginTop: '6px',
                              }}
                           />
                        </div>
                        <div className='w-[85%] flex flex-col gap-1'>
                           <div className='font-semibold text-md'>
                              {notification.notification_title}
                           </div>
                           <div className='text-sm text-muted-foreground truncate ellipsis overflow-hidden'>
                              {notification.notification_message}
                           </div>
                           <div className='text-xs text-muted-foreground flex justify-between gap-1'>
                              <div className='flex items-center justify-start gap-6'>
                                 <Button
                                    variant='ghost'
                                    className='py-1 px-0 text-[#3C76E1]'
                                    onClick={() =>
                                       void handleViewAnalysis(
                                          notification.notification_data
                                             .session_id,
                                          notification.id,
                                          notification.notification_data.mode,
                                       )
                                    }
                                 >
                                    View Analysis
                                 </Button>
                                 {!notification.is_read && (
                                    <Button
                                       variant='ghost'
                                       className='py-1 px-0 text-[#3C76E1]'
                                       onClick={() =>
                                          void handleMarkAsRead(notification.id)
                                       }
                                    >
                                       Mark as Read
                                    </Button>
                                 )}
                              </div>
                              <div className='py-1 px-0 text-muted-foreground'>
                                 <Button
                                    disabled
                                    variant='ghost'
                                    className='py-1 px-0 text-xs'
                                 >
                                    {new Date(
                                       notification.created_at,
                                    ).toLocaleTimeString([], {
                                       hour: '2-digit',
                                       minute: '2-digit',
                                       hour12: false,
                                    })}
                                 </Button>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               ))
            ) : (
               <div className='text-sm text-muted-foreground text-center'>
                  No notifications
               </div>
            )}
         </PopoverContent>
      </Popover>
   );
};

export default NotificationsPopover;
