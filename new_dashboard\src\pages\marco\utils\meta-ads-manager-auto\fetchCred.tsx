import metaAdsManagerEndPoints from '../../../../api/service/agentic-workflow/meta-ads-manager'; // adjust path
import { LocalStorageService, Keys } from '../../../../utils/local-storage'; // adjust path
import { AxiosError } from 'axios';

export const fetchMetaCredentials = async (): Promise<boolean | null> => {
   try {
      const client_id = LocalStorageService.getItem(Keys.ClientId) as string;
      if (!client_id) return null;

      const response = await metaAdsManagerEndPoints.fetchCredentials({
         client_id,
      });
      const data = response?.data?.data;

      if (!data?.meta_access_token || !data?.page_id || !data?.ad_account_id) {
         return false;
      }

      return true;
   } catch (error) {
      const axiosError = error as AxiosError;

      if (axiosError.response?.status === 404) {
         return false;
      }

      console.error('Error fetching credentials:', axiosError.message);
      return null;
   }
};
