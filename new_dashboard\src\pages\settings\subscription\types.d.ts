import { AxiosResponse } from 'axios';

interface SubscriptionEndpoints {
   fetchPlans: () => Promise<AxiosResponse<Plans[]>>;
   fetchAgents: () => Promise<AxiosResponse<Agents[]>>;
   createRazorpayCustomer: (payload: {
      name: string;
      contact?: string | null;
      gstin?: string | null;
      client_id: string;
      email: string;
      billing_address: string;
   }) => Promise<
      AxiosResponse<{ success: boolean; message: string; customer_id?: string }>
   >;

   subscribeToPlan: (payload: {
      plan_id: string;
      plan_name: string;
      client_id: string;
      client_Phone: string | null;
      client_Email: string;
      rzrPay_PlanId: string;
      rzrPay_ClientId: string;
      isYearly: boolean;
      amount: string;
      attribute: Attributes;
      autoRenew: boolean;
      extra_connectors: number;
      max_users: number;
      currency: string;
   }) => Promise<
      AxiosResponse<{ success: boolean; message: string; subs_id?: string }>
   >;

   cancelSubscription: (
      client_id: string,
   ) => Promise<AxiosResponse<{ success: boolean; message: string }>>;

   getSubscriptionContractRecords: (client_id: string) => Promise<
      AxiosResponse<{
         success: boolean;
         message: string;
         subscription: SubscriptionRecord[] | null;
      }>
   >;
   getSubscriptionHistoryRecords: (client_id: string) => Promise<
      AxiosResponse<{
         success: boolean;
         message: string;
         history: SubscriptionHistoryRecord[] | null;
      }>
   >;
   purchaseTopup: (payload: {
      client_id: string;
      amount: number;
      currency: string;
      topup: boolean;
      agents: AgentTopupPayload[];
   }) => Promise<
      AxiosResponse<{ success: boolean; message: string; order_id?: string }>
   >;
   getPaymentRecords: (client_id: string) => Promise<
      AxiosResponse<{
         success: boolean;
         message: string;
         payments: PaymentRecords[] | null;
      }>
   >;
   getTopupRecords: (client_id: string) => Promise<
      AxiosResponse<{
         success: boolean;
         message: string;
         topups: TopupRecord[] | null;
      }>
   >;
   getAgentUsages: (client_id: string) => Promise<
      AxiosResponse<{
         success: boolean;
         message: string;
         agentUsages: AgentUsageRecord[] | null;
      }>
   >;
   getClientAllInvoiceRecord: (client_id: string) => Promise<
      AxiosResponse<{
         success: boolean;
         message: string;
         invoices: InvoiceRecords[];
      }>
   >;
}

export interface Notes {
   [key: string]: string | boolean;
}

export interface Agent {
   id: string;
   access: boolean;
   value: number;
}

export interface DashboardIntegrations {
   meta_ads: boolean;
   amazon_ads: boolean;
   google_Ads: boolean;
   unicommerce: boolean;
   social_media: {
      whatsapp: boolean;
   };
   hubspot_store: boolean;
   shopify_store: boolean;
   google_search_console: boolean;
   amazon_selling_partner: boolean;
}

export interface Attributes {
   agents: {
      alerting_agent: Agent;
      diagnostic_agent: Agent;
      analytics_agent: Agent;
      'CMO Mode (AI Recos)': Agent;
   };
   dashboard_connectors: DashboardIntegrations;
   pulse: boolean;
   'Weekly Business Report': {
      level: string;
      access: boolean;
   };
   '360 degree Dashboard': boolean;
   'Extra Agent Usages (PAYG)': boolean;
   'Pulse Insights (Deep Ad Analysis)': boolean;
   user_limit: number;
}

export interface Plans {
   id: string;
   name: string;
   tier: number;
   monthly_price: string;
   yearly_price: string;
   plan_info: string;
   plan_desc: string;
   plan_feature: string[];
   action_label: string;
   monthly_ad_spend: string;
   monthly_revenue: string;
   agentic_fit: string;
   attribute: Attributes;
   period: number;
   currency: string;
   razorpay_monthly_planid: string;
   razorpay_yearly_planid: string;
   extra_connectors: number;
   max_users: number;
}

export type Agents = {
   agent_id: string;
   agent_name: string;
   agent_desc: string;
   pricepertoken: number;
   agent_features: string[];
   currency: string;
};

export interface SubscriptionRecord {
   max_users: number | null;
   id: string;
   client_id: string;
   plan_id: string;
   plan_name: string;
   razorpay_subscription_id: string;
   currency: string;
   amount: string;
   auto_renew: boolean;
   is_active: boolean;
   is_yearly: boolean;
   status: string;
   notes: Notes;
   attribute: Attributes;
   subs_start_dt: string;
   subs_end_dt: string;
   created_at: string;
}

export interface AgentTopupPayload {
   id: string;
   name: string;
   tokens: number;
   pricePerToken: number;
   totalPrice: number;
}

export interface AgentUsageRecord {
   agent_id: string;
   agent_name: string;
   total_tokens: number;
   total_tokens_used: number;
}

interface PaymentRecords {
   id: string;
   client_id: string;
   subscription_id: string;
   plan_name: string;
   razorpay_subscription_id: string;
   razorpay_order_id: string;
   razorpay_payment_id: string;
   currency: string;
   amount: number;
   method: string;
   status: string;
   label: string;
   notes?: {
      vpa: string;
      desc: string;
      email: string;
      contact: string;
      token_id?: string;
   };
   created_at: string;
}

interface TopupRecord {
   id: string;
   client_id: string;
   subscription_id: string;
   plan_id: string;
   plan_name: string;
   razorpay_subscription_id: string;
   razorpay_order_id: string;
   razorpay_payment_id: string;
   currency: string;
   amount: number;
   status: string;
   label: string;
   created_at: string;
}

interface TopupItem {
   amount: string;
   agent_id: string;
   currency: string;
   tokens_added: number;
}

interface TopupRecord {
   id: string;
   razorpay_order_id: string;
   razorpay_payment_id: string;
   currency: string;
   amount: number;
   status: string;
   label: string;
   created_at: string;
   items: TopupItem[];
}

interface AgentUsage {
   agent_id: string;
   agent_name: string;
   is_expired: boolean;
   token_total: number;
   token_used: number;
   user_id: number;
}

export interface HistoryAttributes extends Attributes {
   topups: TopupRecord[];
   agent_usages: AgentUsage[];
   autoRenew: boolean;
   isYearly: boolean;
   notes: Notes;
}

export interface SubscriptionHistoryRecord {
   id: string;
   client_id: string;
   subscription_id: string;
   plan_id: string;
   plan_name: string;
   razorpay_subscription_id: string;
   currency: string;
   amount: string;
   status: string;
   remarks: string;
   subs_start_dt: string;
   subs_end_dt: string;
   created_at: string;
   attribute: HistoryAttributes;
}

export interface InvoiceRecords {
   id: string;
   client_id: string;
   subscription_id: string;
   plan_id: string;
   plan_name: string;
   is_topup: boolean;
   topup_id?: string;
   razorpay_subscription_id: string;
   razorpay_order_id: string;
   razorpay_payment_id: string;
   currency: string;
   amount: number;
   status: string;
   url: string;
   billing_start_dt: string;
   billing_end_dt: string;
   label: string;
   created_dt: string;
}

export interface PricingCardProps extends Plans {
   isYearly: boolean;
}

export interface RazorpayOptions {
   key: string;
   // amount: number;
   currency: string;
   name: string;
   description?: string;
   // image?: string;
   // order_id?: string;
   // subscription_id?: string;
   prefill?: {
      name?: string;
      email?: string;
      contact?: string;
   };
   notes?: Record<string, string>;
   theme?: {
      color?: string;
   };
   handler?: (response: RazorpayResponse) => void;
   modal?: {
      ondismiss?: () => void;
   };
}

export interface RazorpayTopupOptions extends RazorpayOptions {
   amount: number;
   currency: string;
   name: string;
   description: string;
   prefill: {
      email: string;
      contact: string;
   };
   notes: Record<string, string>;
}

export interface RazorpayTopupResponse extends RazorpayResponse {
   razorpay_payment_id: string;
   razorpay_order_id: string;
   razorpay_signature: string;
}

export interface RazorpayInstance {
   open(): void;
   close(): void;
}

export interface RazorpayResponse {
   razorpay_payment_id: string;
   razorpay_order_id: string;
   razorpay_signature: string;
}

export type CollapsibleSections = {
   agents: boolean;
   integrations: boolean;
   socialMedia: boolean;
   pulse: boolean;
   users: boolean;
};

export type PlansTableProps = {
   plansData: Plans[];
   extraOptions: string[];
   supportOptions: string[];
};

export interface PaymentModalProps {
   plan_id: string;
   rzrPay_PlanId: string;
   plan_name: string;
   client_id: string;
   client_Phone: string | null;
   client_Email: string;
   isYearly: boolean;
   currency: string;
   amount: string;
   attribute: Attributes;
   extra_connectors: number;
   max_users: number;
   autoRenew: boolean;
}
