import { Box, Flex, Heading, Collapse, Text } from '@chakra-ui/react';
import { costSettingOptions } from '../../../utils/strings/settings-strings';
import { useEffect, useRef, useState } from 'react';
import { CSModes } from '../interface';
import CSImage from './cs-image';
import { FaChevronDown, FaChevronUp } from 'react-icons/fa';
import { MdClose, MdDone } from 'react-icons/md';
import './cost-setting.scss';
import { useOutsideClick } from '@chakra-ui/react';
import COGS from './cogs';
import Shipping from './shipping';
import { useAppSelector } from '../../../store/store';
import PaymentGateway from './payment-gateway';
import Customexpenses from './custom-expenses';
import { useApiQuery } from '../../../hooks/react-query-hooks';
import KPIQueryKeys, { CFOKeys } from '../../dashboard/utils/query-keys';
import cfoService, { PaymentMethod } from '../../../api/service/cfo/index';
import kpiService from '../../../api/service/kpi/index';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { useDispatch } from 'react-redux';
import {
   setCogsFixedRate,
   setFixedExpenses,
   setPaymentMethods,
   setshippingCost,
   setshippingCostByOrderId,
   setShippingProfiles,
   setVariableExpenses,
} from '../../../store/reducer/cfo-reducer';
import { setKpiMeta } from '../../../store/reducer/kpi-reducer';
function CostSettings() {
   const [optionOpen, setoptionOpen] = useState<CSModes>('');
   const optionsRef = useRef(null);
   const dispatch = useDispatch();
   const currentModal = useAppSelector((state) => state.modal);
   const {
      cogsFixedRate,
      paymentMethod,
      shippingCost,
      shippingProfiles,
      shippingCostByOrderId,
      fixedExpenses,
      variableExpenses,
   } = useAppSelector((state) => state.cfo);
   const [optAdded, setoptAdded] = useState<{
      [key: string]: boolean;
   }>({
      cogs: false,
      shipping: false,
      paymentgate: false,
      customexpenses: true,
   });
   const { refetch: getCogsData } = useApiQuery({
      queryKey: [CFOKeys.getCogs],
      queryFn: () =>
         cfoService.getCogsData(
            LocalStorageService.getItem(Keys.ClientId) as string,
         ),
      enabled: false,
   });
   const { refetch: getShippingCosts } = useApiQuery({
      queryKey: [CFOKeys.getShippingCosts],
      queryFn: () =>
         cfoService.getShippingCosts(
            LocalStorageService.getItem(Keys.ClientId) as string,
         ),
      enabled: false,
   });
   const { refetch: getShippingCostsByOrderId } = useApiQuery({
      queryKey: [CFOKeys.getShippingCostsByOrderId],
      queryFn: () =>
         cfoService.getShippingCostByOrderId(
            LocalStorageService.getItem(Keys.ClientId) as string,
         ),
      enabled: false,
   });
   const { refetch: getPaymentMethods } = useApiQuery({
      queryKey: [CFOKeys.getPaymentMethods],
      queryFn: () =>
         cfoService.getPaymentMethod(
            LocalStorageService.getItem(Keys.ClientId) as string,
         ),
      enabled: false,
   });
   const { refetch: getShippingProfiles } = useApiQuery({
      queryKey: [CFOKeys.getShippingProfiles],
      queryFn: () =>
         cfoService.getShippingProfiles(
            LocalStorageService.getItem(Keys.ClientId) as string,
         ),
      enabled: false,
   });
   const { refetch: getVariableExpenses } = useApiQuery({
      queryKey: [CFOKeys.getVariableExpenses],
      queryFn: () =>
         cfoService.getVariableExpenses(
            LocalStorageService.getItem(Keys.ClientId) as string,
         ),
      enabled: false,
   });
   const { refetch: getFixedExpenses } = useApiQuery({
      queryKey: [CFOKeys.getFixedExpenses],
      queryFn: () =>
         cfoService.getFixedExpenses(
            LocalStorageService.getItem(Keys.ClientId) as string,
         ),
      enabled: false,
   });
   const { refetch: getMetaData } = useApiQuery({
      queryKey: [KPIQueryKeys.kpiMeta],
      queryFn: () =>
         kpiService.getKpiMeta(
            LocalStorageService.getItem(Keys.ClientId) as string,
         ),
      getInitialData: () => [],
      enabled: false,
   });
   useEffect(() => {
      getCogsData()
         .then((data) => {
            if (data.data) {
               const fixedPercent = data.data[0];
               dispatch(
                  setCogsFixedRate(
                     fixedPercent || {
                        fixed_percent: '',
                        id: null,
                     },
                  ),
               );
               setoptAdded((prev) => {
                  return { ...prev, cogs: !!fixedPercent?.fixed_percent };
               });
            }
         })
         .catch(console.log);
      getPaymentMethods()
         .then((data) => {
            if (data.data) {
               dispatch(setPaymentMethods(data.data));
               setoptAdded((prev) => {
                  return {
                     ...prev,
                     paymentgate: data.data.some((pm) => pm.fee || pm.cost),
                  };
               });
            }
         })
         .catch(console.log);
      getShippingCosts()
         .then((data) => {
            if (data.data) {
               dispatch(
                  setshippingCost(
                     data.data[0] || {
                        is_default: null,
                        id: null,
                        clientId: LocalStorageService.getItem(
                           Keys.ClientId,
                        ) as string,
                     },
                  ),
               );
            }
         })
         .catch(console.log);
      getShippingProfiles()
         .then((data) => {
            if (data.data) {
               dispatch(setShippingProfiles(data.data));
            }
         })
         .catch(console.log);
      getShippingCostsByOrderId()
         .then((data) => {
            if (data.data) {
               dispatch(setshippingCostByOrderId(data.data || []));
            }
         })
         .catch(console.log);
      getVariableExpenses()
         .then((data) => {
            if (data.data) {
               dispatch(setVariableExpenses(data.data));
            }
         })
         .catch(console.log);
      getFixedExpenses()
         .then((data) => {
            if (data.data) {
               dispatch(setFixedExpenses(data.data));
            }
         })
         .catch(console.log);
      getMetaData()
         .then((data) => {
            if (data.data) dispatch(setKpiMeta(data.data));
         })
         .catch(console.log);
   }, []);

   useEffect(() => {
      const isShippingAdded = () => {
         if (shippingCost.is_default !== null) {
            if (!shippingCost.is_default) return true;
            if (shippingProfiles.length > 0 || shippingCostByOrderId.length > 0)
               return true;
            return false;
         }
         return false;
      };
      setoptAdded((prev) => {
         return {
            ...prev,
            cogs: !!cogsFixedRate.fixed_percent,
            paymentgate: paymentMethod.some(
               (pm: PaymentMethod) => pm.fee || pm.cost,
            ),
            shipping: isShippingAdded(),
            customexpenses:
               fixedExpenses.length !== 0 || variableExpenses.length !== 0,
         };
      });
   }, [
      cogsFixedRate.fixed_percent,
      paymentMethod,
      shippingCost,
      variableExpenses,
      fixedExpenses,
      shippingCostByOrderId,
      shippingProfiles,
   ]);
   useOutsideClick({
      ref: optionsRef,
      handler: () => {
         if (!currentModal.show) setoptionOpen('');
      },
   });
   const handleOptionOpen = (opt: CSModes): void => {
      if (optionOpen == opt) setoptionOpen('');
      else setoptionOpen(opt);
   };
   return (
      <Box p={4} width={'100%'} className='cost-setting'>
         <Heading
            fontSize={'20px'}
            pb={4}
            fontWeight={'500'}
            borderBottom={'1px solid #EBEBEB'}
         >
            Cost
         </Heading>
         <Flex ref={optionsRef} flexDirection={'column'} gap={4} py={4}>
            {costSettingOptions.map((option) => {
               return (
                  <Flex
                     flexDirection={'column'}
                     key={option.key}
                     border={'1px solid #C2CBD4'}
                     borderRadius={'8px'}
                     p={3}
                  >
                     <Flex
                        onClick={() => handleOptionOpen(option.key)}
                        justifyContent={'space-between'}
                        cursor={'pointer'}
                     >
                        <Heading
                           fontSize={'16px'}
                           display={'flex'}
                           justifyContent={'center'}
                           alignItems={'center'}
                           gap={2}
                           fontWeight={'400'}
                        >
                           <CSImage csCat={option.key} /> {option.title}
                        </Heading>
                        <Flex alignItems={'center'} gap={4}>
                           <GetOptionAdd added={!!optAdded[option.key]} />{' '}
                           {optionOpen === option.key ? (
                              <FaChevronUp />
                           ) : (
                              <FaChevronDown />
                           )}
                        </Flex>
                     </Flex>
                     <Collapse
                        unmountOnExit
                        in={optionOpen === option.key}
                        animateOpacity
                        className='children'
                     >
                        {<RenderOption option={option.key} />}
                     </Collapse>
                  </Flex>
               );
            })}
         </Flex>
      </Box>
   );
}

function GetOptionAdd(props: { added: boolean }) {
   if (props.added)
      return (
         <Text
            className='add-text '
            border={'1px solid #C0EED2'}
            backgroundColor={'#C0EED2'}
            color={'#15994A'}
         >
            <MdDone /> Added
         </Text>
      );
   return (
      <Text
         className='add-text panel'
         border={'1px solid #FFD2D2'}
         backgroundColor={'#FFD2D2'}
         color={'#B93C3C'}
      >
         <MdClose /> Not Added
      </Text>
   );
}

function RenderOption(props: { option: string }) {
   switch (props.option) {
      case 'cogs':
         return <COGS />;
      case 'shipping':
         return <Shipping />;
      case 'paymentgate':
         return <PaymentGateway />;
      case 'customexpenses':
         return <Customexpenses />;
   }
   return '';
}

export default CostSettings;
