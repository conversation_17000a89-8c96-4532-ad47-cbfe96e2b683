import { ResponseTargetingAnalysis } from '../../../../api/service/agentic-workflow/meta-ads-manager';

export type TableSection = {
   sectionTitle: string;
   rows: { [key: string]: string }[];
};

export function extractTargetingTables(
   targeting_analysis: ResponseTargetingAnalysis,
): TableSection[] {
   return Object.entries(targeting_analysis)
      .filter(([, value]) => Array.isArray(value) && value.length > 0)
      .map(([key, value]) => ({
         sectionTitle:
            key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, ' '),
         rows: value as { [key: string]: string }[],
      }));
}
