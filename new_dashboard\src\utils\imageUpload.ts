import socialWatchEndpoints from '../api/service/social-watch/apis';

export interface ImageUploadResponse {
   uri: string;
   success: boolean;
   error?: string;
}

export const uploadImageToAzure = async (
   imageFile: File,
): Promise<ImageUploadResponse> => {
   try {
      const formData = new FormData();
      formData.append('media', imageFile);

      const uploadResponse =
         await socialWatchEndpoints.uploadMediaToAzure(formData);

      if (!uploadResponse?.data?.uri) {
         throw new Error('No URI received from upload');
      }

      const decodeResponse = await socialWatchEndpoints.decodeAzureMediaUrl({
         image_url: uploadResponse.data.uri,
      });

      if (!decodeResponse?.data?.uri) {
         throw new Error('Failed to decode image URL');
      }
      return {
         uri: decodeResponse.data.uri,
         success: true,
      };
   } catch (error) {
      console.error('Image upload error:', error);
      return {
         uri: '',
         success: false,
         error:
            error instanceof Error ? error.message : 'Failed to upload image',
      };
   }
};

/*export const uploadImageUrlToAzure = async (
   imageUrl: string,
   fileName = 'image.jpg',
): Promise<ImageUploadResponse> => {
   try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();

      let ext = 'jpg';
      if (blob.type === 'image/png') ext = 'png';
      else if (blob.type === 'image/webp') ext = 'webp';
      else if (blob.type === 'image/jpeg') ext = 'jpg';
      const file = new File(
         [blob],
         fileName.endsWith('.' + ext)
            ? fileName
            : `${fileName.split('.')[0]}.${ext}`,
         { type: blob.type },
      );
      return await uploadImageToAzure(file);
   } catch (error) {
      console.error('Failed to upload image from URL:', error);
      return {
         uri: '',
         success: false,
         error:
            error instanceof Error
               ? error.message
               : 'Failed to upload image from URL',
      };
   }
};*/
