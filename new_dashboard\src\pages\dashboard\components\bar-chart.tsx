import { useEffect, useState } from 'react';
import Chart from 'react-apexcharts';
import { ChartProp, KPIData } from '../utils/interface';
import { ApexOptions } from 'apexcharts';
import { useAppSelector } from '../../../store/store';
import { getChartDateLabel, getFormattedVal, toHHMMSS } from '../utils/helpers';
import { noZeroKPI } from '../../../utils/strings/kpi-constants';
import { useColorMode } from '@chakra-ui/react';

function BarChart(props: ChartProp) {
   const { kpiDetails } = props;
   const { groupBy } = useAppSelector((state) => state.kpi);
   const { colorMode } = useColorMode();

   const categories = getChartDateLabel(kpiDetails.allData, groupBy);
   const [chartData, setchartData] = useState({
      options: {
         chart: {
            id: kpiDetails.displayName,
            toolbar: {
               show: false,
            },
         },
         xaxis: {
            //type: groupBy == 'day' ? 'datetime' : 'string',
            categories: categories,
            tickAmount: 30,
            labels: {
               show: true,
               rotate: -45,
               rotateAlways: true,
               maxHeight: 150,
               style: {
                  colors: colorMode === 'dark' ? '#fff' : '#000',
               },
            },
         },

         yaxis: {
            //type: groupBy == 'day' ? 'datetime' : 'string',

            labels: {
               style: {
                  colors: colorMode === 'dark' ? '#fff' : '#000',
               },
            },
         },
         tooltip: {
            y: {
               formatter: function (value: number) {
                  if (
                     !value &&
                     noZeroKPI.includes(kpiDetails.allData[0].kpi_names)
                  )
                     return 'N/A';
                  return kpiDetails.unit == 'time'
                     ? toHHMMSS(value)
                     : getFormattedVal(Math.round(value * 100) / 100);
               },
            },
         },
         stroke: {
            curve: 'smooth',
         },
         dataLabels: {
            enabled: false,
         },
         grid: {
            show: false,
         },
      },
      series: [
         {
            name: kpiDetails.displayName,
            data: kpiDetails.allData.map((x: KPIData) =>
               Number(x.kpi_value?.toFixed(2)),
            ),
         },
      ],
   });
   useEffect(() => {
      setchartData({
         options: {
            chart: {
               id: kpiDetails.displayName,
               toolbar: {
                  show: false,
               },
            },
            xaxis: {
               //type: groupBy == 'day' ? 'datetime' : 'string',
               categories: categories,
               tickAmount: 30,
               labels: {
                  show: true,
                  rotate: -45,
                  rotateAlways: true,
                  maxHeight: 150,
                  style: {
                     colors: colorMode === 'dark' ? '#fff' : '#000',
                  },
               },
            },

            yaxis: {
               //type: groupBy == 'day' ? 'datetime' : 'string',

               labels: {
                  style: {
                     colors: colorMode === 'dark' ? '#fff' : '#000',
                  },
               },
            },
            tooltip: {
               y: {
                  formatter: function (value: number) {
                     if (
                        !value &&
                        noZeroKPI.includes(kpiDetails.allData[0].kpi_names)
                     )
                        return 'N/A';
                     return kpiDetails.unit == 'time'
                        ? toHHMMSS(value)
                        : getFormattedVal(Math.round(value * 100) / 100);
                  },
               },
            },
            stroke: {
               curve: 'smooth',
            },
            dataLabels: {
               enabled: false,
            },
            grid: {
               show: false,
            },
         },
         series: [
            {
               name: kpiDetails.displayName,
               data: kpiDetails.allData.map((x: KPIData) =>
                  Number(x.kpi_value?.toFixed(2)),
               ),
            },
         ],
      });
   }, [kpiDetails.allData]);

   return (
      <>
         <Chart
            options={chartData.options as ApexOptions}
            series={chartData.series}
            type='bar'
            width='100%'
            height='400px'
         />
      </>
   );
}

export default BarChart;
