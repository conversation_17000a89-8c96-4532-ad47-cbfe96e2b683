import { SpinnerIcon } from '@chakra-ui/icons';
import './Card.scss';
import { useLocation } from 'react-router-dom';

interface CardProps {
   src: string;
   heading: string;
   subHeading?: string;
   onButtonClick?: () => void;
   isConnected?: boolean;
   isFetching?: boolean;
   isDisconnecting?: boolean;
   isConnecting?: boolean;
   children?: React.ReactNode;
   commingSoon?: boolean;
   showBanner?: boolean;
   error?: string | null;
   darkTheme?: boolean;
}

const Card: React.FC<CardProps> = ({
   src,
   error,
   heading,
   subHeading,
   onButtonClick,
   isConnected,
   isFetching,
   isDisconnecting,
   isConnecting,
   children,
   commingSoon = false,
   showBanner = false,
}) => {
   const location = useLocation();

   const renderButtonContent = () => {
      if (isConnecting) return 'Connecting...';
      if (isFetching) return 'Fetching...';
      if (isDisconnecting) return 'Disconnecting...';
      if (commingSoon) return 'Coming soon';
      return isConnected ? 'Disconnect' : 'Add';
   };

   const renderButton = () => (
      <button
         className={`btn-card ${isConnected && !isFetching ? 'disconnect' : ''} ${commingSoon ? 'soon' : ''}`}
         onClick={onButtonClick}
         disabled={commingSoon || isFetching || isConnecting || isDisconnecting}
         style={{
            cursor:
               commingSoon || isFetching || isConnecting || isDisconnecting
                  ? 'not-allowed'
                  : 'pointer',
         }}
      >
         {(isFetching || isConnecting || isDisconnecting) && <SpinnerIcon />}
         {renderButtonContent()}
      </button>
   );

   return (
      <div className='IntCardWrapper'>
         <div
            className={`card ${location.pathname === '/onboarding' ? 'onboarding' : ''}`}
         >
            <img
               className={`avatar ${
                  location.pathname === '/onboarding' ? 'onboarding' : ''
               } ${src.includes('linkedin') ? 'linkedin-avatar' : ''} ${src.includes('hubs') ? 'hubspot-avatar' : ''} ${src.includes('Amazon Ads') ? 'linkedin-avatar' : ''}`}
               src={src}
               alt='Avatar'
            />
            <div
               className={`card-container ${location.pathname === '/onboarding' ? 'onboarding' : ''}`}
            >
               <h4>
                  <b className='integrationCardHeading'>{heading}</b>
               </h4>
               {subHeading && <p>{subHeading}</p>}
               {error && <p className='error'>{error}</p>}
            </div>
            {children || renderButton()}
         </div>
         {showBanner && (
            <div className='banner'>
               <span>Integrated 1 channel</span>
            </div>
         )}
      </div>
   );
};

export default Card;
