import {
   <PERSON>,
   Tab,
   <PERSON><PERSON><PERSON><PERSON>,
   <PERSON>b<PERSON>anel,
   Tab<PERSON>ane<PERSON>,
   Tabs,
   Text,
} from '@chakra-ui/react';

import { ChevronLeftIcon } from '@chakra-ui/icons';
import { useAppDispatch, useAppSelector } from '../../store/store';
import { setSelectedTab } from '../../store/reducer/user-details-reducer';
import { socialLabel } from '../../utils/strings/socialwatch-strings';
import useIntegrationConnectionDetails from '../../hooks/use-integration-connection-details';
import { useEffect } from 'react';
import { TabContent } from '..';

interface TabValue {
   label: string;
   value: string;
   children?: React.ReactNode;
   tabContent?: React.ReactNode;
}
const tabValues: TabValue[] = [
   {
      label: 'Twitter',
      value: 'twitter',
      children: <TabContent />,
   },
   {
      label: 'Linkedin',
      value: 'linkedin',
      children: <TabContent />,
   },
   {
      label: 'Instagram',
      value: 'instagram',
      children: <div>Instagram</div>,
   },
];

interface TabEffectProps {
   handleBack: () => void;
}

function TabEffect({ handleBack }: TabEffectProps) {
   const { mediaTypes } = useIntegrationConnectionDetails();

   const { selectedTab } = useAppSelector((state) => state.media);
   const dispatch = useAppDispatch();

   function handleTabClick(tab: string) {
      dispatch(setSelectedTab(tab));
   }

   useEffect(() => {
      if (!mediaTypes.includes(selectedTab)) {
         dispatch(setSelectedTab(mediaTypes[0]));
      }
   }, []);

   return (
      <Box border={2} borderColor='#A0A4A888' borderRadius='md'>
         <Box
            justifyContent='flex-start'
            display='flex'
            alignItems='center'
            mb={4}
         >
            <ChevronLeftIcon />
            <Text onClick={handleBack} style={{ cursor: 'pointer' }}>
               Back
            </Text>
         </Box>
         <Tabs
            index={mediaTypes.findIndex(
               (mediaType) => mediaType === selectedTab,
            )}
            style={{
               width: '100%',
               boxSizing: 'border-box',
               border: '1px solid #A0A4A888',
               borderRadius: '4px',
               resize: 'none',
            }}
         >
            <TabList p={2} pb={0}>
               {mediaTypes.map((tab) => (
                  <Tab
                     onClick={() => handleTabClick(tab)}
                     key={tab}
                     fontSize={'20px'}
                  >
                     {socialLabel[tab] || tab}
                  </Tab>
               ))}
            </TabList>
            <TabPanels>
               {tabValues.map((tab) => (
                  <TabPanel key={tab.value}>{tab.children}</TabPanel>
               ))}
            </TabPanels>
         </Tabs>
      </Box>
   );
}

export default TabEffect;
