import React, { useEffect, useState } from 'react';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import { useColorMode, useColorModeValue } from '@chakra-ui/react';
import { getChartDateLabel } from '../../dashboard/utils/helpers';

interface KPIData {
   date: string;
   kpi_value: number;
}

interface ChartProp {
   kpiDetails: {
      displayName: string;
      allData: KPIData[];
      stat: string;
   };
}

const BarChartRange: React.FC<ChartProp> = ({ kpiDetails }) => {
   const { colorMode } = useColorMode();
   const textColor = useColorModeValue('black', 'white');
   const bgColor = useColorModeValue('white', 'gray.800');

   const chartColor =
      kpiDetails.stat.toLowerCase() === 'active' ? '#15994A' : undefined;

   const categories = getChartDateLabel(kpiDetails.allData, '');

   const [chartData, setChartData] = useState({
      options: {
         chart: {
            id: kpiDetails.displayName,
            toolbar: {
               show: false,
            },
            background: bgColor,
         },
         xaxis: {
            type: 'string',
            categories: categories,
            labels: {
               show: true,
               rotate: -45,
               rotateAlways: true,
               maxHeight: 150,
               style: {
                  colors: colorMode === 'dark' ? '#fff' : '#000',
               },
            },
            tickAmount: 30,
         },
         yaxis: {
            labels: {
               show: true,
               style: {
                  colors: colorMode === 'dark' ? '#fff' : '#000',
               },
            },
         },
         plotOptions: {
            bar: {
               borderRadius: 4,
               horizontal: false,
            },
         },
         dataLabels: {
            enabled: false,
         },
         grid: {
            show: false,
         },
         colors: chartColor ? [chartColor] : undefined,
      },
      series: [
         {
            name: kpiDetails.displayName,
            data: kpiDetails.allData.map((x: KPIData) => x.kpi_value),
         },
      ],
   });

   useEffect(() => {
      setChartData((prevData) => ({
         ...prevData,
         options: {
            ...prevData.options,
            chart: {
               ...prevData.options.chart,
               background: bgColor,
            },
            xaxis: {
               ...prevData.options.xaxis,
               labels: {
                  ...prevData.options.xaxis.labels,
                  style: {
                     colors: colorMode === 'dark' ? '#fff' : '#000',
                  },
               },
            },
            yaxis: {
               ...prevData.options.yaxis,
               labels: {
                  ...prevData.options.yaxis.labels,
                  style: {
                     colors: colorMode === 'dark' ? '#fff' : '#000',
                  },
               },
            },
         },
      }));
   }, [colorMode, bgColor, textColor, kpiDetails]);

   return (
      <Chart
         options={chartData.options as ApexOptions}
         series={chartData.series}
         type='bar'
         style={{ width: '100%', height: '100%' }}
         // width='100%'
         // height='100%'
      />
   );
};

export default BarChartRange;
