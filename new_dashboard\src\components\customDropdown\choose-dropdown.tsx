import React, { useState, useEffect } from 'react';
import './choose.scss';

type DropdownOption = {
   value: string;
   label: string;
};

interface DropdownProps {
   options: DropdownOption[];
   onSelect: (value: string) => void;
   initialValue?: string;
   width?: number;
   id?: string;
   loading?: boolean;
}

const Dropdown: React.FC<DropdownProps> = ({
   options,
   onSelect,
   initialValue,
   width,
   id,
   loading,
}) => {
   const [selectedOption, setSelectedOption] = useState<string>(
      initialValue || options[0]?.value,
   );

   useEffect(() => {
      setSelectedOption(initialValue || options[0]?.value);
   }, [initialValue, options]);

   const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
      const value = event.target.value;
      setSelectedOption(value);
      onSelect(value);
   };

   return (
      <select
         id={id}
         value={selectedOption}
         onChange={handleChange}
         className='dropdown1'
         style={{ width: width ? `${width}px` : '' }}
      >
         {options.map((option) => (
            <option
               key={option.value}
               value={option.value}
               disabled={loading && option.value !== selectedOption}
               style={{ cursor: loading ? 'not-allowed' : 'pointer' }}
            >
               {option.label}
            </option>
         ))}
      </select>
   );
};

export default Dropdown;
