import 'react-date-range/dist/styles.css'; // main css file
import 'react-date-range/dist/theme/default.css'; // theme css file
import './date-select.scss';
import {
   Button,
   Flex,
   PopoverCloseButton,
   Portal,
   Select,
   useColorMode,
} from '@chakra-ui/react';
import { ChangeEvent, useEffect, useState } from 'react';
import { DateRangePicker, RangeKeyDict } from 'react-date-range';
import { FaChevronDown, FaRegCalendarAlt } from 'react-icons/fa';

import {
   Popover,
   PopoverTrigger,
   PopoverContent,
   PopoverBody,
   PopoverFooter,
} from '@chakra-ui/react';

import {
   defaultPreviousRanges,
   defaultStaticRanges,
   defaultPulseStaticRanges,
} from '../utils/default-ranges';
import {
   addDays,
   addMilliseconds,
   endOfDay,
   format,
   startOfDay,
} from 'date-fns';
import {
   getLabel,
   getPrevPeriod,
   getValidGroupBy,
   isDisabledGroupBy,
} from '../utils/helpers';
import { KPIStoreRange, RangeState } from '../utils/interface';
import {
   setdateRange,
   setGroupBy,
   setprevRange,
} from '../../../store/reducer/kpi-reducer';
import { useDispatch } from 'react-redux';
import { useAppSelector } from '../../../store/store';
import { GROUP_BY_RANGE } from '../utils/default-variables';
interface DateRangeSelectProps {
   dateId?: string;
   rangeId?: string;
   groupByEnabled?: boolean;
   pulse?: boolean;
}
const DateRangeSelect: React.FC<DateRangeSelectProps> = ({
   dateId,
   rangeId,
   groupByEnabled,
   pulse,
}) => {
   const dispatch = useDispatch();
   const { colorMode } = useColorMode();

   const { dateRange, prevRange, groupBy } = useAppSelector(
      (state) => state.kpi,
   );

   const [initialRange, setInitialRange] = useState<RangeState>({
      show: false,
      dateRange: [
         {
            startDate: new Date(
               format(
                  startOfDay(new Date(dateRange.start)),
                  "yyyy-MM-dd'T'HH:mm:ss",
               ),
            ),
            endDate: new Date(
               format(
                  endOfDay(new Date(dateRange.end)),
                  "yyyy-MM-dd'T'HH:mm:ss",
               ),
            ),
            key: 'selection',
         },
      ],
   });

   const [comparedTo, setcomparedTo] = useState<RangeState>({
      show: false,
      dateRange: [
         {
            startDate: new Date(
               format(
                  startOfDay(new Date(prevRange.start)),
                  "yyyy-MM-dd'T'HH:mm:ss",
               ),
            ),
            endDate: new Date(
               format(
                  endOfDay(new Date(prevRange.end)),
                  "yyyy-MM-dd'T'HH:mm:ss",
               ),
            ),
            key: 'selection',
         },
      ],
   });

   const handleDateRange = (item: RangeKeyDict) => {
      const startDate = new Date(
         format(
            startOfDay(item.selection.startDate || new Date()),
            "yyyy-MM-dd'T'HH:mm:ss",
         ),
      );
      const endDate = new Date(
         format(
            endOfDay(item.selection.endDate || new Date()),
            "yyyy-MM-dd'T'HH:mm:ss",
         ),
      );
      const validGroupBy = getValidGroupBy(endDate, startDate, groupBy);
      if (groupBy != validGroupBy) dispatch(setGroupBy(validGroupBy));
      setInitialRange((prev) => {
         return {
            ...prev,
            dateRange: [
               {
                  key: 'selection',
                  startDate: startDate,
                  endDate: endDate,
               },
            ],
         };
      });
      setcomparedTo((prev) => {
         const prevPeriod = getPrevPeriod({
            start: startDate,
            end: endDate,
         });
         return {
            ...prev,
            dateRange: [
               {
                  startDate: prevPeriod.start,
                  endDate: prevPeriod.end,
                  key: 'selection',
               },
            ],
         };
      });
   };

   const updateRange = () => {
      const newDateRange: KPIStoreRange = {
         start: format(
            startOfDay(initialRange.dateRange[0].startDate),
            "yyyy-MM-dd'T'HH:mm:ss",
         ),
         end: format(
            endOfDay(initialRange.dateRange[0].endDate),
            "yyyy-MM-dd'T'HH:mm:ss",
         ),
      };
      const newPrevRange: KPIStoreRange = {
         start: format(
            startOfDay(comparedTo.dateRange[0].startDate),
            "yyyy-MM-dd'T'HH:mm:ss",
         ),
         end: format(
            endOfDay(comparedTo.dateRange[0].endDate),
            "yyyy-MM-dd'T'HH:mm:ss",
         ),
      };
      if (
         newDateRange.start === dateRange.start &&
         newPrevRange.start === prevRange.start &&
         newDateRange.end === dateRange.end &&
         newPrevRange.end === prevRange.end
      )
         return;
      dispatch(setdateRange(newDateRange));
      dispatch(setprevRange(newPrevRange));
   };

   const updateCompareBy = (item: RangeKeyDict) => {
      const startDate = new Date(
         format(
            startOfDay(item.selection.startDate || new Date()),
            "yyyy-MM-dd'T'HH:mm:ss",
         ),
      );
      const endDate = new Date(
         format(
            endOfDay(item.selection.endDate || new Date()),
            "yyyy-MM-dd'T'HH:mm:ss",
         ),
      );
      setcomparedTo((prev) => {
         return {
            ...prev,
            dateRange: [
               {
                  key: 'selection',
                  startDate: startDate,
                  endDate: endDate,
               },
            ],
         };
      });
   };

   const handleGroupBy = (e: ChangeEvent<HTMLSelectElement>) => {
      if (!e.target.value) return;
      dispatch(setGroupBy(e.target.value));
   };

   useEffect(() => {
      setInitialRange((prev) => {
         return {
            ...prev,
            dateRange: [
               {
                  key: 'selection',
                  startDate: new Date(
                     format(
                        startOfDay(new Date(dateRange.start)),
                        "yyyy-MM-dd'T'HH:mm:ss",
                     ),
                  ),
                  endDate: new Date(
                     format(
                        endOfDay(new Date(dateRange.end)),
                        "yyyy-MM-dd'T'HH:mm:ss",
                     ),
                  ),
               },
            ],
         };
      });
   }, [dateRange]);

   return (
      <Flex gap={3} alignItems='center' className='kpi-date-filter'>
         <div id={dateId}>
            <Popover
               placement='bottom-end'
               closeOnBlur={true}
               onClose={updateRange}
            >
               <PopoverTrigger>
                  <Button
                     color={colorMode === 'dark' ? '#437EEB' : '#2A69AC'}
                     className='btn data-label'
                     minWidth='fit-content'
                     display='flex'
                     alignItems='center'
                     gap={4}
                     bg={colorMode === 'dark' ? 'var(--controls)' : 'white'}
                     _hover={{
                        bg:
                           colorMode === 'dark'
                              ? 'var(--controls-hover)'
                              : 'gray.50',
                     }}
                  >
                     <FaRegCalendarAlt width='12px' />{' '}
                     <span> {getLabel(initialRange.dateRange[0])}</span>{' '}
                     <FaChevronDown width='12px' />
                  </Button>
               </PopoverTrigger>
               <Portal>
                  <PopoverContent
                     width='fit-content'
                     className={`date-select ${colorMode}-theme`}
                  >
                     <PopoverBody padding={0}>
                        <DateRangePicker
                           className={`date-picker-${colorMode}`}
                           onChange={handleDateRange}
                           moveRangeOnFirstSelection={false}
                           months={1}
                           ranges={initialRange.dateRange}
                           maxDate={new Date()}
                           minDate={addDays(new Date(), pulse ? -90 : -365)}
                           direction='horizontal'
                           staticRanges={
                              pulse
                                 ? defaultPulseStaticRanges
                                 : defaultStaticRanges
                           }
                           inputRanges={[]}
                        />
                     </PopoverBody>
                     <PopoverFooter
                        display='flex'
                        alignItems='center'
                        justifyContent='flex-start'
                        gap={2}
                     >
                        <PopoverCloseButton className='apply btn'>
                           Apply
                        </PopoverCloseButton>
                        <Button
                           className='compareby btn'
                           onClick={() =>
                              setcomparedTo((prev) => {
                                 return { ...prev, show: !comparedTo.show };
                              })
                           }
                        >
                           Compared to..
                        </Button>
                     </PopoverFooter>
                     {comparedTo.show && (
                        <DateRangePicker
                           className={`date-picker-${colorMode}`}
                           onChange={updateCompareBy}
                           moveRangeOnFirstSelection={false}
                           months={1}
                           maxDate={addMilliseconds(
                              initialRange.dateRange[0].startDate,
                              -1000,
                           )}
                           minDate={addDays(new Date(), -365)}
                           ranges={comparedTo.dateRange}
                           direction='horizontal'
                           staticRanges={defaultPreviousRanges({
                              start: initialRange.dateRange[0].startDate,
                              end: initialRange.dateRange[0].endDate,
                           })}
                           inputRanges={[]}
                        />
                     )}
                  </PopoverContent>
               </Portal>
            </Popover>
         </div>
         {groupByEnabled && (
            <Select
               id={rangeId}
               onChange={handleGroupBy}
               cursor='pointer'
               placeholder='Group by'
               height='32px'
               fontSize='13px'
               value={groupBy}
               bg={colorMode === 'dark' ? 'gray.800' : 'white'}
               color={colorMode === 'dark' ? 'gray.100' : 'gray.800'}
               borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.200'}
               className={`range-select-${colorMode}`}
               _hover={{
                  borderColor: 'blue.400',
               }}
               _focus={{
                  borderColor: 'blue.400',
                  boxShadow: '0 0 0 1px #4299E1',
               }}
            >
               {Object.entries(GROUP_BY_RANGE).map(([key, label]) => (
                  <option
                     key={key}
                     value={key}
                     disabled={isDisabledGroupBy(
                        initialRange.dateRange[0],
                        key,
                     )}
                  >
                     {label}
                  </option>
               ))}
            </Select>
         )}
      </Flex>
   );
};

export default DateRangeSelect;
