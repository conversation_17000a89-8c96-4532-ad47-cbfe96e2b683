import { Box, Heading, Text, Button, useToast } from '@chakra-ui/react';

import { integrationsStep } from '../../../../utils/strings/onboarding-strings';
import {
   AmazonAds,
   AmazonSellerPartner,
   FacebookAds,
   GoogleAds,
   Integration,
   Shopify,
   Hubspot,
} from '../../../social-listening/components';
import './integrations-step.scss';
import {
   useApiMutation,
   useApiQuery,
} from '../../../../hooks/react-query-hooks';
import { LocalStorageService, Keys } from '../../../../utils/local-storage';
import onboardingEndpoints from '../../../../api/service/onboarding';
import { Outlet, useLocation } from 'react-router-dom';
import { OptimisationStatusKeys } from '../../../../layouts/app-layout';
import keys from '../../../../utils/strings/query-keys';
import Unicommerce from '@/pages/social-listening/components/Unicommerce';

const IntegrationsStep = () => {
   const toast = useToast();
   const location = useLocation();

   const integrations: {
      title: string;
      componentKey: OptimisationStatusKeys;
      components: React.FC[];
      completed: boolean;
   }[] = [
      {
         title: 'CRM, Channels & Marketplace',
         componentKey: 'channels_marketplace',
         components: [Shopify, Hubspot, AmazonSellerPartner, Unicommerce],
         completed: true,
      },
      {
         title: 'Ads Account',
         components: [GoogleAds, FacebookAds, AmazonAds],
         componentKey: 'ads_account',
         completed: true,
      },
   ];

   const { data: socialAnalyticsData } = useApiQuery({
      queryKey: ['userSocialDetails'],
      queryFn: () =>
         onboardingEndpoints.getUserSocialDetails({
            client_id: LocalStorageService.getItem(Keys.ClientId) as string,
         }),
      enabled: true,
      refetchOnWindowFocus: false,
   });

   const { mutate, isPending } = useApiMutation({
      mutationFn: onboardingEndpoints.updateRegisterProgress,
      onError(msg) {
         toast({
            title: 'Error',
            description: msg,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
      invalidateCacheQuery: [keys.fetchUserDetails],
   });

   const handleSkipOrNext = () => {
      mutate({
         email_address: LocalStorageService.getItem(Keys.UserName) as string,
         client_id: LocalStorageService.getItem(Keys.ClientId) as string,
         register_progress: 'Step 4',
      });
   };

   return (
      <Box
         width='100%'
         height='100%'
         className='integrations-step'
         display='flex'
         alignItems='center'
         justifyContent='start'
         flexDirection='column'
      >
         <Box width='85%'>
            <Box
               width='100%'
               marginTop='20px'
               marginBottom='20px'
               display='flex'
               flexDirection='column'
               alignItems='center'
               justifyContent='center'
               gap='10px'
               className='integrations-step-heading'
            >
               <Heading fontSize='32px' fontWeight='500' color='#424242'>
                  {integrationsStep.title}
               </Heading>
               <Text
                  fontSize='14px'
                  fontWeight='400'
                  color='gray'
                  textAlign='center'
                  width='60%'
               >
                  {integrationsStep.info}
               </Text>
            </Box>
            {location.pathname === '/onboarding/shopify' ? (
               <Outlet />
            ) : (
               <>
                  <Box width='100%' height='100%' className='widget-container'>
                     {integrations.map((integration, index) => (
                        <Integration
                           id={`integrationCategory${index + 1}`}
                           key={index}
                           components={integration.components}
                           title={integration.title}
                           completed={integration.completed}
                           componentKey={integration.componentKey}
                        />
                     ))}
                  </Box>
                  <Box
                     width='100%'
                     marginBottom='20px'
                     display='flex'
                     flexDirection='column'
                     alignItems='center'
                     justifyContent='center'
                     gap='10px'
                  >
                     <Box bottom={10} display='flex' gap={5}>
                        {socialAnalyticsData &&
                        socialAnalyticsData?.details?.length > 0 ? (
                           <Button
                              padding='12px 20px 12px 20px'
                              backgroundColor='#437EEB'
                              color='#fff'
                              onClick={handleSkipOrNext}
                              isLoading={isPending}
                           >
                              <Text fontSize='16px' fontWeight={500}>
                                 Next
                              </Text>
                           </Button>
                        ) : (
                           <Button
                              color='#424242'
                              onClick={handleSkipOrNext}
                              isLoading={isPending}
                              colorScheme='gray'
                              opacity={0.8}
                           >
                              <Text fontSize='14px' fontWeight={500}>
                                 Skip
                              </Text>
                           </Button>
                        )}
                     </Box>
                  </Box>
               </>
            )}
         </Box>
      </Box>
   );
};

export default IntegrationsStep;
