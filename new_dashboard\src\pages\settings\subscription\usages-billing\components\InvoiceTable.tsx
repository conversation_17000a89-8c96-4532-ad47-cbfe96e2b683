import subsEndPoints from '@/api/service/subscription';
import DefaultCard from '@/components/DefaultCard';
import { Badge } from '@/components/ui/badge';
import { useApiQuery } from '@/hooks/react-query-hooks';
import { UserDetails } from '@/pages/socialwatch/interface';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { Skeleton } from '@/components/ui/skeleton';
import { format } from 'date-fns';
import { InvoiceRecords } from '@/pages/settings/subscription/types';

export default function InvoiceTable() {
   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);

   const { data: invoiceRecords, isLoading: isInvoiceRecordsLoading } =
      useApiQuery<{
         success: boolean;
         message: string;
         invoices: InvoiceRecords[];
      }>({
         queryKey: ['getClientAllInvoiceRecord', userDetails?.client_id],
         queryFn: () =>
            subsEndPoints.getClientAllInvoiceRecord(userDetails?.client_id),
         enabled: !!userDetails?.client_id,
         refetchOnWindowFocus: false,
      });

   return (
      <div className='w-full border border-gray-200 bg-white shadow-sm rounded-lg overflow-hidden'>
         <div className='overflow-x-auto min-h-[400px] max-h-[400px] overflow-y-auto scrollable'>
            <table className='w-full min-w-[1200px]'>
               <thead className='bg-gray-50 sticky top-0 z-20'>
                  <tr className='sticky top-0 z-20 bg-navy text-white'>
                     <th
                        colSpan={9}
                        className='py-3 px-6 text-lg font-bold text-left tracking-wide sticky top-0 z-20 bg-navy text-white'
                     >
                        Invoices
                     </th>
                  </tr>
                  <tr className='border-b border-gray-200 bg-gray-50 sticky top-[48px] z-10'>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-left'>
                        Plan
                     </th>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-left'>
                        Currency
                     </th>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-left'>
                        Amount
                     </th>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-left'>
                        Status
                     </th>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-left'>
                        Billing Period
                     </th>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-left'>
                        Top-up
                     </th>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-left'>
                        Label
                     </th>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-left'>
                        Created At
                     </th>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-center'>
                        Invoice
                     </th>
                  </tr>
               </thead>
               <tbody>
                  {isInvoiceRecordsLoading ? (
                     Array.from({ length: 4 }).map((_, idx) => (
                        <tr key={idx} className='border-b border-gray-100'>
                           <td className='py-4 px-6'>
                              <Skeleton className='h-4 w-24' />
                           </td>
                           <td className='py-4 px-6'>
                              <Skeleton className='h-4 w-16' />
                           </td>
                           <td className='py-4 px-6'>
                              <Skeleton className='h-4 w-16' />
                           </td>
                           <td className='py-4 px-6'>
                              <Skeleton className='h-6 w-16 rounded-full' />
                           </td>
                           <td className='py-4 px-6'>
                              <Skeleton className='h-4 w-32' />
                           </td>
                           <td className='py-4 px-6'>
                              <Skeleton className='h-6 w-12 rounded-full' />
                           </td>
                           <td className='py-4 px-6'>
                              <Skeleton className='h-4 w-20' />
                           </td>
                           <td className='py-4 px-6'>
                              <Skeleton className='h-4 w-28' />
                           </td>
                           <td className='py-4 px-6 text-center'>
                              <Skeleton className='h-8 w-8 rounded-full mx-auto' />
                           </td>
                        </tr>
                     ))
                  ) : invoiceRecords?.invoices?.length ? (
                     invoiceRecords.invoices.map((invoice, index) => (
                        <tr
                           key={invoice.id}
                           className={`border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150 ${
                              index % 2 === 0 ? 'bg-white' : 'bg-gray-25'
                           }`}
                        >
                           <td className='py-4 px-6 font-medium text-gray-900'>
                              {invoice.plan_name}
                           </td>
                           <td className='py-4 px-6 text-gray-700'>
                              {invoice.currency}
                           </td>
                           <td className='py-4 px-6 font-semibold text-gray-900'>
                              ₹{invoice.amount.toLocaleString()}
                           </td>
                           <td className='py-4 px-6'>
                              <Badge
                                 variant={
                                    invoice.status === 'created'
                                       ? 'success'
                                       : invoice.status === 'failed'
                                         ? 'destructive'
                                         : 'outline'
                                 }
                                 className='capitalize font-medium px-3 py-1'
                              >
                                 {invoice.status}
                              </Badge>
                           </td>
                           <td className='py-4 px-6 text-gray-700 text-sm'>
                              {invoice.billing_start_dt &&
                              invoice.billing_end_dt
                                 ? `${format(new Date(invoice.billing_start_dt), 'dd MMM yyyy')} - ${format(new Date(invoice.billing_end_dt), 'dd MMM yyyy')}`
                                 : '-'}
                           </td>
                           <td className='py-4 px-6'>
                              <Badge
                                 variant={
                                    invoice.is_topup ? 'success' : 'destructive'
                                 }
                                 className='capitalize rounded-sm font-medium px-3 py-1'
                              >
                                 {invoice.is_topup ? 'Yes' : 'No'}
                              </Badge>
                           </td>
                           <td className='py-4 px-6 text-gray-700'>
                              {invoice.label || '-'}
                           </td>
                           <td className='py-4 px-6 text-gray-700 text-sm'>
                              {invoice.created_dt
                                 ? format(
                                      new Date(invoice.created_dt),
                                      'dd MMM yyyy, hh:mm a',
                                   )
                                 : '-'}
                           </td>
                           <td className='py-4 px-6 text-center'>
                              {invoice.url ? (
                                 <a
                                    href={invoice.url}
                                    target='_blank'
                                    rel='noopener noreferrer'
                                    className='inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-50 hover:bg-blue-100 transition-colors duration-150 group'
                                    title='View PDF'
                                 >
                                    <svg
                                       xmlns='http://www.w3.org/2000/svg'
                                       width='16'
                                       height='16'
                                       fill='none'
                                       viewBox='0 0 24 24'
                                       stroke='currentColor'
                                       strokeWidth={2}
                                       className='w-4 h-4 text-blue-600 group-hover:text-blue-700'
                                    >
                                       <path
                                          strokeLinecap='round'
                                          strokeLinejoin='round'
                                          d='M15 12a3 3 0 11-6 0 3 3 0 016 0z'
                                       />
                                       <path
                                          strokeLinecap='round'
                                          strokeLinejoin='round'
                                          d='M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z'
                                       />
                                    </svg>
                                 </a>
                              ) : (
                                 <span className='text-gray-400 text-sm'>
                                    -
                                 </span>
                              )}
                           </td>
                        </tr>
                     ))
                  ) : (
                     <tr>
                        <td colSpan={9} className='py-8 px-6'>
                           <DefaultCard
                              banner='inactivePlan'
                              imgClassName='w-28'
                              title='No Invoice History Found'
                              titleClassName='head6'
                           />
                        </td>
                     </tr>
                  )}
               </tbody>
            </table>
         </div>
      </div>
   );
}
