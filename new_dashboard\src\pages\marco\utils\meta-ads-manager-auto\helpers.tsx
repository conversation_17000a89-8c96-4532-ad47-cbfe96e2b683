import {
   parseISO,
   isToday,
   isYesterday,
   isAfter,
   subDays,
   subMonths,
   subYears,
} from 'date-fns';
import { MetaAdsAutoAgentChat } from '../../../../api/service/agentic-workflow/meta-ads-manager';

type GroupedAutoAgentHistory = {
   today: MetaAdsAutoAgentChat[];
   yesterday: MetaAdsAutoAgentChat[];
   last7Days: MetaAdsAutoAgentChat[];
   lastMonth: MetaAdsAutoAgentChat[];
   lastYear: MetaAdsAutoAgentChat[];
   older: MetaAdsAutoAgentChat[];
};

export const groupAutoAgentChatsByDate = (
   chatList: MetaAdsAutoAgentChat[],
): GroupedAutoAgentHistory => {
   const grouped: GroupedAutoAgentHistory = {
      today: [],
      yesterday: [],
      last7Days: [],
      lastMonth: [],
      lastYear: [],
      older: [],
   };

   const now = new Date();

   for (const chat of chatList) {
      const date = parseISO(chat.updated_at);

      if (isToday(date)) {
         grouped.today.push(chat);
      } else if (isYesterday(date)) {
         grouped.yesterday.push(chat);
      } else if (isAfter(date, subDays(now, 7))) {
         grouped.last7Days.push(chat);
      } else if (isAfter(date, subMonths(now, 1))) {
         grouped.lastMonth.push(chat);
      } else if (isAfter(date, subYears(now, 1))) {
         grouped.lastYear.push(chat);
      } else {
         grouped.older.push(chat);
      }
   }

   for (const key in grouped) {
      grouped[key as keyof GroupedAutoAgentHistory].sort((a, b) => {
         const dateA = parseISO(a.updated_at).getTime();
         const dateB = parseISO(b.updated_at).getTime();
         return dateB - dateA;
      });
   }

   return grouped;
};
