import { useApiMutation, useApiQuery } from '@/hooks/react-query-hooks';
import { useAppSelector } from '@/store/store';
import { AuthUser } from '@/types/auth';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import metaAdsManagerEndPoints, {
   MetaAdsAutoAgentChat,
} from '../../../api/service/agentic-workflow/meta-ads-manager';

export const useFetchMetaAdsAutoAgentHistoryQuery = () => {
   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) || {};

   const { currentAgent } = useAppSelector((state) => state.marco);

   return useApiQuery<MetaAdsAutoAgentChat[]>({
      queryKey: ['meta-ads-auto-agent-history', currentAgent],
      queryFn: () =>
         metaAdsManagerEndPoints.fetchAllAutoAgentHistory({
            client_id: client_id || '',
            user_id: user_id || '',
         }),
      enabled:
         !!client_id && !!user_id && currentAgent === 'meta-ads-manager-auto',
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
   });
};

export const useFetchMetaAdsAutoAgentHistoryBySessionQuery = () => {
   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) || {};

   const { currentAgent } = useAppSelector((state) => state.marco);
   const { sessionId } = useAppSelector((state) => state.metaAdsAutoAgent);

   return useApiQuery<MetaAdsAutoAgentChat[]>({
      queryKey: [
         'meta-ads-auto-agent-session-history',
         String(sessionId),
         currentAgent,
      ],
      queryFn: () =>
         metaAdsManagerEndPoints.fetchAutoAgentHistoryBySession({
            client_id: client_id || '',
            user_id: user_id || '',
            session_id: sessionId || '',
         }),
      enabled: !!sessionId && currentAgent === 'meta-ads-manager-auto',
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
   });
};

export const useSaveMetaAdsAutoAgentHistoryMutation = () => {
   return useApiMutation({
      queryKey: ['save-meta-ads-auto-agent-history'],
      mutationFn: metaAdsManagerEndPoints.saveAutoAgentHistory,
   });
};
