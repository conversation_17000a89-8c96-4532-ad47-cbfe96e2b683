import DefaultCard from '@/components/DefaultCard';
import Plan from './Plans';
import Topups from './Topups';
import { tabAttribute } from '../../constants';

const PlanTabContent = ({ attribute = 'plan' }: { attribute: string }) => {
   const content = tabAttribute[attribute as keyof typeof tabAttribute];

   return (
      <section className='bg-white p-6 border !border-fog rounded-md space-y-6 overflow-auto max-h-[72vh] w-full'>
         <h2 className='head5 text-jet'>{content.title}</h2>
         <hr className='h-[1px] w-full bg-fog' />
         <p className='para4 text-charcoal'>{content.desc}</p>
         {content.title === 'Plans' && <Plan />}
         {content.title === 'Top-up Tokens' && <Topups />}
         <DefaultCard
            title='Still Have Questions?'
            desc='Our dedicated team is ready to assist you. Reach out for more information or explore our resources.'
            actionLabel='Contact Us'
         />
      </section>
   );
};

export default PlanTabContent;
