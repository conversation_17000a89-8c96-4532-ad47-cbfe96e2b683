import { Flex, Stack, Text, Card, Box } from '@chakra-ui/react';
import { FaChartLine, FaLightbulb, FaUsers } from 'react-icons/fa';

import { useAppSelector } from '../../../../store/store';
import { CampaignInsights } from '../../../../api/service/agentic-workflow/meta-ads-manager';

export const AdsetInsights = () => {
   const { currentChat, campaignInsights } = useAppSelector(
      (state) => state.metaAdsManager,
   );

   const currentMessage = currentChat?.[currentChat.length - 1];

   let messageInsights: CampaignInsights | Record<string, string> = {};
   try {
      messageInsights =
         (currentMessage?.campaignInsights as CampaignInsights) ??
         (typeof campaignInsights === 'string'
            ? (JSON.parse(campaignInsights || '{}') as CampaignInsights)
            : (campaignInsights as CampaignInsights));
   } catch (error) {
      console.error('Error parsing campaignInsights:', error);
      messageInsights = {};
   }

   if (!messageInsights || Object.keys(messageInsights).length === 0) {
      return <Text>No Adset insights available</Text>;
   }

   return (
      <Card
         p={4}
         border='1px solid #e2e8f0'
         borderRadius='md'
         mt={2}
         mb={5}
         width='100%'
         bg='#f8f9fa'
      >
         <Text fontSize='lg' fontWeight='bold' mb={6}>
            Adset Insights
         </Text>
         <Stack spacing={6}>
            <Box>
               <Flex align='center' mb={2}>
                  <Box bg='blue.50' p={2} borderRadius='md' mr={3}>
                     <FaUsers color='#3182CE' size={20} />
                  </Box>
                  <Text size='sm' color='blue.600'>
                     Best Performing Audience
                  </Text>
               </Flex>

               <Text ml={12} color='gray.700'>
                  {(messageInsights as CampaignInsights)
                     ?.best_performing_audience || 'No audience data available'}
               </Text>
            </Box>

            <Box>
               <Flex align='center' mb={2}>
                  <Box bg='green.50' p={2} borderRadius='md' mr={3}>
                     <FaChartLine color='#38A169' size={20} />
                  </Box>
                  <Text size='sm' color='green.600'>
                     Engagement Trends
                  </Text>
               </Flex>

               <Text ml={12} color='gray.700'>
                  {(messageInsights as CampaignInsights)?.engagement_trends ||
                     'No engagement data available'}
               </Text>
            </Box>

            <Box>
               <Flex align='center' mb={2}>
                  <Box bg='purple.50' p={2} borderRadius='md' mr={3}>
                     <FaLightbulb color='#805AD5' size={20} />
                  </Box>
                  <Text size='sm' color='purple.600'>
                     Optimization Tip
                  </Text>
               </Flex>
               <Text ml={12} color='gray.700'>
                  {(messageInsights as CampaignInsights)?.optimization_tip ||
                     'No optimization tips available'}
               </Text>
            </Box>
         </Stack>
      </Card>
   );
};
