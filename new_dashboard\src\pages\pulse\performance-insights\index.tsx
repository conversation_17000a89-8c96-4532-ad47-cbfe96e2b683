import '../pulse.scss';

import React from 'react';
import { useAppSelector } from '../../../store/store';
import IntegrateInfo from '../../../components/integrate-info/integrate-info';
import { integrationInfoStrings } from '../../../utils/strings/integrate-info-strings';
import { Navigate } from 'react-router-dom';

const Performance: React.FC = () => {
   const { optimisationsStatus } = useAppSelector((state) => state.onboarding);

   if (!optimisationsStatus.complete && !optimisationsStatus.ads_account) {
      return (
         <IntegrateInfo
            feature={integrationInfoStrings.performanceInsights.title}
            text={integrationInfoStrings.performanceInsights.description}
         />
      );
   }

   return <Navigate to='/pulse/performance-insights/overview' />;
};

export default Performance;
