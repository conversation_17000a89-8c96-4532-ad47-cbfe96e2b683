import Chart from 'react-apexcharts';
import { KPIData, MultiLineChartProp, OptionsMore } from '../utils/interface';
import { ApexOptions } from 'apexcharts';
import { useAppSelector } from '../../../store/store';
import {
   getChartDateLabel,
   getFormattedVal,
   getFullData,
   toHHMMSS,
} from '../utils/helpers';
import { noZeroKPI } from '../../../utils/strings/kpi-constants';
import { useColorMode } from '@chakra-ui/react';

function MultiLineChart(props: MultiLineChartProp) {
   const { kpiDetails, prevDetails, startLabel } = props;
   if (!prevDetails?.allData?.length) return null;
   const { dateRange, prevRange, groupBy } = useAppSelector(
      (state) => state.kpi,
   );
   const currFull = getFullData(dateRange, kpiDetails.allData, groupBy);
   const prevFull = getFullData(prevRange, prevDetails.allData, groupBy);
   const { colorMode } = useColorMode();
   const categories = getChartDateLabel(currFull, groupBy);
   const categories2 = getChartDateLabel(prevFull, groupBy);
   const getLegendHeight = (labels: string[]): number => {
      return labels[0].length > 25 ? 40 : 0;
   };
   const chartData = {
      options: {
         chart: {
            id: kpiDetails.displayName,
            toolbar: {
               show: false,
            },
            zoom: {
               enabled: false,
            },
            height: 300,
         },

         stroke: {
            curve: 'smooth',
            width: [4, 3],
            dashArray: [0, 6],
         },
         xaxis: {
            tickAmount: 30,
            labels: {
               show: true,
               rotate: -45,
               rotateAlways: true,
               maxHeight: 200,
               style: {
                  colors: colorMode ? '#FFFFFF' : '#000000',
               },
            },
         },
         colors: ['#2E93fA', '#FF9800'],
         labels: categories,

         yaxis: {
            //type: groupBy == 'day' ? 'datetime' : 'string',

            labels: {
               style: {
                  colors: colorMode === 'dark' ? '#fff' : '#000',
               },
            },
         },
         legend: {
            horizontalAlign: 'left',
            offsetX: 0,
            // offsetY: getOffsetY(categories),
            labels: {
               colors: colorMode === 'dark' ? '#FFFFFF' : '#000000',
            },
            height: getLegendHeight(categories),
            offsetY: Math.floor(getLegendHeight(categories) / 2.5),
         },
         tooltip: {
            y: [
               {
                  formatter: function (value: number) {
                     if (!value && noZeroKPI.includes(currFull[0].kpi_names))
                        return 'N/A';
                     return kpiDetails.unit == 'time'
                        ? toHHMMSS(value)
                        : getFormattedVal(Math.round(value * 100) / 100);
                  },
               },
               {
                  formatter: function (value: number) {
                     if (!value && noZeroKPI.includes(prevFull[0].kpi_names))
                        return 'N/A';
                     return prevDetails.unit == 'time'
                        ? toHHMMSS(value)
                        : getFormattedVal(Math.round(value * 100) / 100);
                  },
               },
            ],
            x: {
               formatter: function (_v: number, x: OptionsMore) {
                  return `${categories[x.dataPointIndex]} vs ${categories2[x.dataPointIndex]}`;
               },
            },
         },
         dataLabels: {
            enabled: false,
         },
      },
      series: [
         {
            name: startLabel,
            data: currFull.map((x: KPIData) => Number(x.kpi_value?.toFixed(2))),
         },
         {
            name: 'Preceding Period',
            data: prevFull.map((x: KPIData) => Number(x.kpi_value?.toFixed(2))),
         },
      ],
   };

   return (
      <>
         <Chart
            options={chartData.options as ApexOptions}
            series={chartData.series}
            type='line'
            width='100%'
            height='400px'
         />
      </>
   );
}

export default MultiLineChart;
