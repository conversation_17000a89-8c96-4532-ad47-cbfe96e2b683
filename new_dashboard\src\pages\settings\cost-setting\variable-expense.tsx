import {
   Button,
   Checkbox,
   Input,
   InputGroup,
   InputRightElement,
   NumberInput,
   NumberInputField,
   Table,
   TableCaption,
   TableContainer,
   Tbody,
   Td,
   Th,
   Thead,
   Tooltip,
   Tr,
   useColorModeValue,
} from '@chakra-ui/react';
import { LiaPenAltSolid } from 'react-icons/lia';
import { MdDeleteOutline, MdOutlinePercent } from 'react-icons/md';
import { useAppSelector } from '../../../store/store';

import DatePicker from 'react-datepicker';
import { openModal } from '../../../store/reducer/modal-reducer';
import { modalTypes } from '../../../components/modals/modal-types';
import { VariableExpenseData } from '../interface';
import { useDispatch } from 'react-redux';
function VariableExpenses() {
   const { variableExpenses } = useAppSelector((state) => state.cfo);
   if (variableExpenses.length == 0) return null;
   const dispatch = useDispatch();
   const handleDelete = (record: VariableExpenseData) => {
      dispatch(
         openModal({
            modalType: modalTypes.DELETE_RECORD,
            modalProps: {
               data: {
                  name: record.name,
                  campaign: record.campaign,
                  source: record.source,
                  percent: record.percent,
                  categories: JSON.parse(record.categories) as string[],
                  startDate: record.start_date,
                  endDate: record.end_date,
                  id: record.id,
                  metric: record.metric,
                  selCategory: record.sel_category,
                  adSpend: record.ad_spend,
               },
               type: 'variable',
            },
         }),
      );
   };
   const handleEdit = (record: VariableExpenseData) => {
      dispatch(
         openModal({
            modalType: modalTypes.VARIABLE_EXPENSE_MODAL,
            modalProps: {
               name: record.name,
               campaign: record.campaign ?? '',
               source: record.source,
               percent: record.percent,
               categories: JSON.parse(record.categories) as string[],
               startDate: record.start_date,
               endDate: record.end_date,
               id: record.id,
               metric: record.metric ?? '',
               selCategory: record.sel_category,
               adSpend: record.ad_spend,
            },
         }),
      );
   };
   return (
      <TableContainer
         className='ce-table'
         my={5}
         mx={4}
         border={'none !important'}
      >
         <Table variant='simple'>
            <TableCaption
               textAlign={'left'}
               color={useColorModeValue('black', 'white')}
               fontSize={'16px'}
               px={0}
               placement='top'
            >
               Variable Expenses
            </TableCaption>
            <Thead>
               <Tr>
                  <Th>Name</Th>
                  <Th>Category</Th>
                  <Th>Metric</Th>
                  <Th>Source</Th>
                  <Th>Percent</Th>
                  <Th> Start Date</Th>
                  <Th> End Date</Th>

                  <Th>Ad Spend</Th>
                  <Th></Th>
               </Tr>
            </Thead>
            <Tbody>
               {variableExpenses.map((ve) => {
                  const startDate = ve.start_date
                     ? new Date(ve.start_date as string)
                     : null;
                  const endDate = ve.end_date
                     ? new Date(ve.end_date as string)
                     : null;
                  return (
                     <Tr key={ve.id}>
                        <Td>{ve.sel_category}</Td>
                        <Td>{ve.name}</Td>
                        <Td>
                           {' '}
                           <Tooltip label={ve.metric} hasArrow gutter={20}>
                              <Input
                                 defaultValue={ve.metric}
                                 type='text'
                                 disabled
                              />
                           </Tooltip>
                        </Td>
                        <Td>
                           {' '}
                           <Tooltip label={ve.source} hasArrow gutter={20}>
                              <Input
                                 defaultValue={ve.source}
                                 type='text'
                                 disabled
                              />
                           </Tooltip>
                        </Td>
                        <Td>
                           <InputGroup
                              backgroundColor={useColorModeValue(
                                 'white',
                                 'var(--controls)',
                              )}
                           >
                              <InputRightElement pointerEvents='none'>
                                 {' '}
                                 <MdOutlinePercent />
                              </InputRightElement>
                              <NumberInput
                                 width={'100%'}
                                 defaultValue={ve.percent}
                                 isDisabled
                              >
                                 <NumberInputField
                                    pl={8}

                                    // value={cogsPerc}
                                    // onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
                                    //    setcogsPerc(event.target.value)
                                    // }
                                 />
                              </NumberInput>
                           </InputGroup>
                        </Td>
                        <Td>
                           {' '}
                           <DatePicker
                              dateFormat='MMM d, yyyy'
                              disabled
                              selected={startDate}
                              onChange={console.log}
                           />
                        </Td>
                        <Td>
                           <DatePicker
                              dateFormat='MMM d, yyyy'
                              disabled
                              selected={endDate}
                              onChange={console.log}
                           />
                        </Td>

                        <Td>
                           <Checkbox
                              isDisabled
                              defaultChecked={ve.ad_spend}
                              height={'100%'}
                              width={'100%'}
                           ></Checkbox>
                        </Td>

                        <Td width={0}>
                           <Button
                              onClick={() => handleEdit(ve)}
                              height={'30px'}
                              width={'30px'}
                              mr={3}
                              background={'none'}
                              p={0}
                              border={'1px solid #C2CBD4'}
                              borderRadius={'5px'}
                           >
                              <LiaPenAltSolid />
                           </Button>
                           <Button
                              onClick={() => handleDelete(ve)}
                              height={'30px'}
                              width={'30px'}
                              background={'none'}
                              p={0}
                              border={'1px solid #C2CBD4'}
                              borderRadius={'5px'}
                           >
                              <MdDeleteOutline />
                           </Button>
                        </Td>
                     </Tr>
                  );
               })}
            </Tbody>
         </Table>
      </TableContainer>
   );
}

export default VariableExpenses;
