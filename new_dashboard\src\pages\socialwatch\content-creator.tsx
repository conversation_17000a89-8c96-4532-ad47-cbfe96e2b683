import { ReactNode } from 'react';
import {
   Box,
   Text,
   Stack,
   Divider,
   Flex,
   Image,
   useColorMode,
} from '@chakra-ui/react';
import WriteContent from '../../assets/image/writecontent.svg';
import GenerateContent from '../../assets/image/contentgenration.svg';
import ContentIdentation from '../../assets/image/contentidentation.svg';
import './contentcreator.scss';

import {
   sessionKey,
   sessionValue,
   contentCreationCardWriteHere,
   contentCreationCardContentGeneration,
   contentCreationCardContentIndentation,
} from '../../utils/strings/content-manager';
import introJs from 'intro.js';
import { TooltipPosition } from 'intro.js/src/packages/tooltip';
import { useAppDispatch, useAppSelector } from '../../store/store';
import { componentNames, setFlag } from '../../store/reducer/tour-reducer';
import { useEffect } from 'react';
import {
   ContentGeneration,
   ContentViewer,
   TabEffect,
   DateCard,
} from '../../components';
import ContentIdeation from './content-ideation';
import { handleStep } from '../../store/reducer/configReducer';

interface CardProp {
   id: number;
   title: string;
   tourId: string;
   description: string;
   path: string;
   image: string;
}

const Card: CardProp[] = [
   {
      id: 1,
      tourId: 'contentCreationSection1',
      title: contentCreationCardWriteHere.title,
      description: contentCreationCardWriteHere.description,
      path: '/aisuggestions',
      image: WriteContent,
   },
   {
      id: 2,
      tourId: 'contentCreationSection2',
      title: contentCreationCardContentGeneration.title,
      description: contentCreationCardContentGeneration.description,
      path: '/contentgeneration',
      image: GenerateContent,
   },
   {
      id: 3,
      title: contentCreationCardContentIndentation.title,
      tourId: 'contentCreationSection3',
      description: contentCreationCardContentIndentation.description,
      path: '/contentgeneration',
      image: ContentIdentation,
   },
];

function ContentViewerWrapper({ handleBack }: { handleBack: () => void }) {
   return (
      <Box className='create-content'>
         <Stack direction={'row'} gap={0} height={'100%'}>
            <Box w='60%' border={1} borderColor='#A0A4A888' p={4} pl={8}>
               <TabEffect handleBack={handleBack} />
               <DateCard />
            </Box>
            <Divider
               orientation='vertical'
               color={'#A0A4A8'}
               opacity={'1'}
               borderLeftWidth={'1.5px'}
            />
            <Box w='40%'>
               <ContentViewer />
            </Box>
         </Stack>
      </Box>
   );
}

function ContentCreator() {
   const { contentCreator } = useAppSelector((state) => state.tour);
   const { step } = useAppSelector((state) => state.config);

   const dispatch = useAppDispatch();

   const intro = introJs();
   function handleBack() {
      dispatch(handleStep(0));
   }
   function handleBackToWrapper() {
      dispatch(handleStep(1));
   }

   const componentsMap: Record<number, ReactNode | null> = {
      1: <ContentViewerWrapper handleBack={handleBack} />,
      2: (
         <ContentGeneration
            handleBack={handleBack}
            handleBackToWrapper={handleBackToWrapper}
         />
      ),
      3: (
         <ContentIdeation
            handleBack={handleBack}
            handleBackToWrapper={handleBackToWrapper}
         />
      ),
   };

   function getComponent(step: number) {
      return componentsMap[step] || null;
   }

   const handleMenuClick = (id: number): void => {
      if (id === 1)
         sessionStorage.setItem(sessionKey.aiButton, sessionValue.aiButton);
      else if (id === 2) sessionStorage.setItem('aibutton', 'Regenerate');

      dispatch(handleStep(id));

      return;
   };
   const steps: {
      element: string;
      intro: string;
      position: TooltipPosition;
   }[] = [
      {
         element: '#contentCreationSection1',
         intro: 'Dropdown',
         position: 'top',
      },
      {
         element: '#contentCreationSection2',
         intro: 'With Content Generation, you can effortlessly create AI-powered content tailored to your ideas. Simply input your concept, and our tool will craft engaging text that aligns with your vision. It’s a quick and easy way to produce high-quality content without the hassle.',
         position: 'top',
      },
      {
         element: '#contentCreationSection3',
         intro: 'Content Ideation helps you generate fresh ideas by tracking industry trends and competitor posts. Stay ahead with relevant content that keeps you competitive and on-trend.',
         position: 'top',
      },
   ];

   const startTour = () => {
      intro.setOptions({ steps });
      void intro.start();

      dispatch(
         setFlag({
            componentName: componentNames.CONTENT_CREATOR,
            flag: false,
         }),
      );
   };

   useEffect(() => {
      if (contentCreator) startTour();
   }, [contentCreator]);
   return (
      <>
         {!step && (
            <Flex
               justify='center'
               align='center'
               style={{ paddingTop: '150px' }}
               justifyContent='space-evenly'
            >
               <Stack direction={{ md: 'column', lg: 'row' }} spacing={2}>
                  {Card.map((item) => (
                     <ContentCard
                        key={item.id}
                        item={item}
                        handleMenuClick={handleMenuClick}
                     />
                  ))}
               </Stack>
            </Flex>
         )}
         {getComponent(step)}
      </>
   );
}

const ContentCard = ({
   item,
   handleMenuClick,
}: {
   item: CardProp;
   handleMenuClick: (id: number) => void;
}) => {
   const { colorMode } = useColorMode();

   return (
      <Box
         id={item.tourId}
         maxW={{ base: 'full', sm: 'sm', md: 'md', lg: 'lg' }}
         borderWidth='1px'
         borderRadius='lg'
         overflow='hidden'
         onClick={() => handleMenuClick(item.id)}
         mr={{ base: 0, sm: 2, md: 4 }}
         width={{ base: '100%', sm: '300px' }}
      >
         <Flex justify='center' align='center' mt={4}>
            <Image
               src={item.image}
               alt='content'
               width={{ base: '50px', sm: '60px', md: '80px', lg: '100px' }}
               filter={colorMode === 'dark' ? 'invert(1)' : 'none'}
               transition='filter 0.2s ease-in-out'
            />
         </Flex>
         <Flex direction='column' justify='center' align='center' p='6'>
            <Box>
               <Text
                  fontWeight='semibold'
                  letterSpacing='wide'
                  fontSize={{ sm: 'xs', md: 'sm', lg: 'md' }}
               >
                  {item.title}
               </Text>
            </Box>
            <Box>
               <Text mt='1' fontSize={{ sm: 'xs', md: 'sm' }} align={'center'}>
                  {item.description}
               </Text>
            </Box>
         </Flex>
      </Box>
   );
};

export default ContentCreator;
