import React from 'react';
import { SimpleGrid, Box, Skeleton, SkeletonText } from '@chakra-ui/react';

interface SkeletonLoaderCampaignsProps {
   spacing: number;
   length: number;
}

const SkeletonLoaderCampaigns: React.FC<SkeletonLoaderCampaignsProps> = ({
   spacing,
   length,
}) => {
   return (
      <Box p={5}>
         <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={spacing}>
            {Array.from({ length }).map((_, index) => (
               <Box
                  key={index}
                  p={5}
                  boxShadow='md'
                  borderWidth='1px'
                  borderRadius='md'
                  overflow='hidden'
               >
                  <Skeleton height='20px' width='50%' mb={2} />
                  <Skeleton height='20px' width='70%' mb={2} />
                  <SkeletonText
                     mt='4'
                     noOfLines={4}
                     spacing='4'
                     skeletonHeight='2'
                  />
                  <Skeleton height='20px' width='20%' mt={4} />
               </Box>
            ))}
         </SimpleGrid>
      </Box>
   );
};

export default SkeletonLoaderCampaigns;
