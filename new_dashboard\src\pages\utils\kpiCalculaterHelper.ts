import { useColorMode } from '@chakra-ui/react';
import { formatValue } from '../pulse/utils/helper';

export const specialMetrics = [
   'cpa',
   'cpp',
   'cpc',
   'cpm',
   'cpl',
   'cpv',
   'cost_per_lead',
   'total_spent', //facebook total ad spen kpi (reverse)
   'google_cpa',
   'google_cpc',
   'google_cpm',
   'google_cpp',
   'google_cpl',
   'google_cpv',
];
export const calculateHelper = (
   metric: string,
   currentKPIValue: string | number | null | undefined,
   previousKPIValue: string | number | null | undefined,
) => {
   const { colorMode } = useColorMode();
   const kpiCurrent = parseFloat(String(currentKPIValue));
   const kpiPrevious = parseFloat(String(previousKPIValue));

   let percentage: string | null = null;
   let direction: string | null = null;
   let color: string = 'red';
   let currentValue =
      currentKPIValue != null && Number.isFinite(currentKPIValue)
         ? formatValue(currentKPIValue)
         : 'N/A';
   const prevValue =
      previousKPIValue != null && Number.isFinite(previousKPIValue)
         ? formatValue(previousKPIValue)
         : 'N/A';

   if (!isNaN(kpiCurrent) && !isNaN(kpiPrevious)) {
      const calculatedPercentage =
         ((kpiCurrent - kpiPrevious) / kpiPrevious) * 100;
      const formattedPercentage = Math.abs(calculatedPercentage).toFixed(2);
      direction = calculatedPercentage > 0 ? 'is up' : 'is down';
      percentage = formattedPercentage;

      if (kpiPrevious === 0) {
         percentage = '100';
         direction = 'is up';
      }
      if (kpiPrevious === 0 && kpiCurrent === 0) {
         percentage = null;
         direction = null;
      }
      const isSpecial = specialMetrics.includes(metric);
      const isUp = direction === 'is up';

      color = isSpecial
         ? isUp
            ? colorMode === 'dark'
               ? '#F87171'
               : 'red' // special: up = bad
            : colorMode === 'dark'
              ? '#4ADE80'
              : 'green' // special: down = good
         : isUp
           ? colorMode === 'dark'
              ? '#4ADE80'
              : 'green' // normal: up = good
           : colorMode === 'dark'
             ? '#F87171'
             : 'red'; // normal: down = bad
   }

   if (isNaN(kpiCurrent)) {
      currentValue = 'N/A';
   }

   return { percentage, color, direction, currentValue, prevValue };
};
