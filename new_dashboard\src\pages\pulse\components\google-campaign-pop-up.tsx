import React, { useState, useEffect } from 'react';
import './popup.scss';
import { Link } from 'react-router-dom';
import { useAppSelector } from '../../../store/store';
import {
   cap,
   toUpperCase,
   extractLinesFromRecommendation,
   formatValue,
   toShowCurrency,
   sortAdGroupsByNestedKPI,
   getDateRange,
   REQUIRED_KPIS_GOOGLE_ADS,
} from '../utils/helper';

import {
   GooglePayloadGenAdgroup,
   GooglePayloadGenChart,
} from '../utils/payloadGen';
import pulseBackendEndpoints, {
   GogoleKPiwiseData,
   SummaryResponse,
   Campaign,
   GoogleAdgroupsData,
} from '../../../api/service/pulse';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import MultiChart from './campaign-insights-chart';
import { Box, Flex, SkeletonText } from '@chakra-ui/react';
import { LinkIcon } from '@chakra-ui/icons';
import GoogleAdgroupPopup from './google-ad-group-pop-up';
import { UserDetails } from './interface';
import { calculateHelper } from '../../utils/kpiCalculaterHelper';
import { LuArrowDownUp, LuArrowUp, LuArrowDown } from 'react-icons/lu';
import { useApiQuery } from '@/hooks/react-query-hooks';
import { OverviewQueryKeys } from '@/pages/dashboard/utils/query-keys';

interface PopupProps {
   isOpen: boolean;
   onClose: () => void;
   details: string;
   allCampaigns: Campaign[];
   data: Campaign;
   // adgroupData: GoogleAdgroupsData[];
}

const GooglePopup: React.FC<PopupProps> = ({
   isOpen,
   onClose,
   allCampaigns,
   data,
   // adgroupData,
}) => {
   const { channel, metric, objective, metricsOptions } = useAppSelector(
      (state) => state.dropdown,
   );

   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);
   const { dateRange, prevRange } = useAppSelector((state) => state.kpi);

   const [sortedAdGroups, setSortedAdGroups] = useState<GoogleAdgroupsData[]>();
   const [isNestedOpen, setIsNestedOpen] = useState(false);
   const [nestedDetails, setNestedDetails] = useState(0);
   const [independentAdgroupData, setIndependentAdgroupData] =
      useState<GoogleAdgroupsData>();
   const [chartsummary, setChartsummary] = useState<SummaryResponse>({});
   const [adgroupsummary, setAdgroupsummary] = useState<SummaryResponse>({});
   const [isChartSummaryLoading, setisChartSummaryLoading] =
      useState<boolean>(true);
   const [isAdgroupSummaryLoading, setisAdgroupSummaryLoading] =
      useState<boolean>(true);
   const [sortDetails, setSortDetails] = useState<{
      metric: string;
      order: string;
   }>({
      metric: 'conversions',
      order: 'desc',
   });
   const [campaignKpiData, setCampaignKpiData] = useState<GogoleKPiwiseData[]>(
      [],
   );
   const [selectedMetric, setmetric] = useState(metric);
   const { start_date, end_date, prev_end_date, prev_start_date, days } =
      getDateRange(dateRange, prevRange);
   const GoogleKpisNames = metricsOptions.map((option) => option.value);

   if (!isOpen) return null;

   useEffect(() => {
      if (isOpen) {
         const fetchKpiData = async () => {
            const GoogleKpisNames = metricsOptions.map(
               (option) => option.value,
            );
            const googleadsKpiwiseCampaignPayload = {
               client_id: userDetails.client_id,
               campaign_id: String(data.campaign_id),
               channel_type: objective,
               kpis: [...GoogleKpisNames, ...REQUIRED_KPIS_GOOGLE_ADS],
               start_date: start_date,
               end_date: end_date,
            };
            const kpiData =
               await pulseBackendEndpoints.fetchGoogleAdsKpiwiseData(
                  googleadsKpiwiseCampaignPayload,
               );
            setCampaignKpiData(
               kpiData.data[0].fn_googleads_campaign_kpi_wise_data_get,
            );
         };
         void fetchKpiData();
      }
   }, [isOpen]);

   const industryBenchmarkKPIs = Array.from(
      new Set(data.kpis.map((kpi) => kpi?.kpi_name).filter(Boolean)),
   ).sort((a, b) => a.localeCompare(b));

   //chart data summary
   useEffect(() => {
      setisChartSummaryLoading(true);
      const fetchSummary = async () => {
         const payload = GooglePayloadGenChart(
            allCampaigns,
            data.campaign_id,
            metricsOptions,
            selectedMetric,
            campaignKpiData[0].kpi_wise_data,
         );
         const response = await pulseBackendEndpoints.fetchCampaignChartSummary(
            {
               ...payload,
               timeframe: [start_date, end_date, String(days)],
               campaign_type: objective,
               // currency: data.currency,
               // ad_groups: adgroupData,
            },
         );
         if (response) {
            setChartsummary(response.data);
            setisChartSummaryLoading(false);
         }
      };
      if (campaignKpiData && Object.keys(campaignKpiData).length > 0) {
         void fetchSummary();
      }
   }, [selectedMetric, isOpen, campaignKpiData]);

   const adgroupPayload = {
      client_id: userDetails?.client_id,
      campaign_id: data?.campaign_id,
      channel_type: objective,
      kpis: [...GoogleKpisNames, ...REQUIRED_KPIS_GOOGLE_ADS],
      start_date: start_date,
      end_date: end_date,
      prev_start_date: prev_start_date,
      prev_end_date: prev_end_date,
   };

   const { data: adgroupData, isLoading: adgroupDataLoading } = useApiQuery({
      queryKey: [
         OverviewQueryKeys?.adsets,
         String(days),
         metric,
         objective,
         `${data?.campaign_id}`,
      ],
      queryFn: () => pulseBackendEndpoints.fetchGoogleAdgroups(adgroupPayload),
      enabled: !!days && !!metric && !!objective && !!data.campaign_id,
      refetchOnWindowFocus: false,
   });

   const adgroupsData: GoogleAdgroupsData[] = adgroupData
      ? adgroupData?.[0]?.fn_googleads_adgroup_with_kpi_get
      : [];

   useEffect(() => {
      if (!isAdgroupSummaryLoading && adgroupsData.length > 0) {
         const sortedAdgroups = sortAdGroupsByNestedKPI(
            adgroupsData,
            'conversions',
            'desc',
         );
         setSortedAdGroups(sortedAdgroups);
      }
   }, [adgroupsData, isAdgroupSummaryLoading]);

   useEffect(() => {
      setisAdgroupSummaryLoading(true);
      const fetchSummary = async () => {
         const payload = GooglePayloadGenAdgroup(
            allCampaigns,
            data.campaign_id,
            metricsOptions,
         );
         const response =
            await pulseBackendEndpoints.fetchCampaignAdgroupSummary({
               ...payload,
               timeframe: [start_date, end_date, String(days)],
               campaign_type: objective,
               ad_groups: adgroupsData,
            });
         if (response) {
            setAdgroupsummary(response.data);
            setisAdgroupSummaryLoading(false);
         }
      };
      if (
         campaignKpiData &&
         Object.keys(campaignKpiData).length > 0 &&
         adgroupsData?.length > 0
      ) {
         void fetchSummary();
      }
   }, [isOpen, campaignKpiData, adgroupData]);

   const handleMetricChange = (newMetric: string) => {
      setmetric(newMetric);
   };

   const handleAdgroupClick = (details: number) => {
      const selectedAdgroup = adgroupsData?.find(
         (ad) => ad.ad_group_id === details,
      );
      setIndependentAdgroupData(selectedAdgroup);
      setNestedDetails(details);
      setIsNestedOpen(true);
   };

   const handleNestedClose = () => {
      setIsNestedOpen(false);
   };

   const handleAdGroupSorting = (metric: string) => {
      const order =
         sortDetails.metric === metric
            ? sortDetails.order === 'asc'
               ? 'desc'
               : 'asc'
            : 'asc';
      setSortDetails({ metric, order });

      const sortedAdGroups = sortAdGroupsByNestedKPI(
         adgroupsData || [],
         metric,
         order,
      );

      setSortedAdGroups([...sortedAdGroups]);
   };

   return (
      <div className='popup-overlay'>
         <div className='popup-content'>
            <div className='heading'>
               <h3>{data.campaign_name}</h3>
               <button className='close-button' onClick={onClose}>
                  x
               </button>
            </div>
            <hr className='divider' />
            <div className='top'>
               <button>{cap(channel)}</button>
               <button>{days} Days</button>
            </div>
            <h3 className='heading-bs'>Budget vs Spend</h3>
            <Flex direction={'column'} gap={2} className='details'>
               <MultiChart
                  chart_data={campaignKpiData[0]?.kpi_wise_data}
                  handleMetricChange={handleMetricChange}
                  selectedMetric={selectedMetric}
                  loading={isChartSummaryLoading}
               />

               <Flex className='recommendations' direction={'column'}>
                  <h3>Overall Campaign Recommendations</h3>
                  {isChartSummaryLoading ? (
                     <>
                        <SkeletonText mt={4} noOfLines={3} />
                     </>
                  ) : chartsummary?.chartData?.Recommendation ? (
                     <ul className='recommendations-list'>
                        {extractLinesFromRecommendation(
                           chartsummary?.chartData?.Recommendation,
                        )?.map((line, index) => <li key={index}>{line}</li>)}
                     </ul>
                  ) : (
                     <ul>No data available</ul>
                  )}
               </Flex>
            </Flex>
            <Flex className='industry-ben' direction='column'>
               <h3>Industry Benchmark</h3>
               <Flex direction='column' gap={2} className='industry-benchmark'>
                  <table className='adset-table'>
                     <thead>
                        <tr>
                           <th>KPI</th>
                           <th>Campaign Value</th>
                           <th>Benchmark Value</th>
                        </tr>
                     </thead>
                     <tbody>
                        {!campaignKpiData[0]?.total_val ? (
                           <>
                              {Array.from(
                                 { length: metricsOptions?.length },
                                 (_, i) => {
                                    return (
                                       <tr key={i}>
                                          {Array.from({ length: 3 }, (_, j) => {
                                             return (
                                                <td key={j}>
                                                   <SkeletonText
                                                      noOfLines={1}
                                                   />
                                                </td>
                                             );
                                          })}
                                       </tr>
                                    );
                                 },
                              )}
                           </>
                        ) : (
                           industryBenchmarkKPIs?.map((kpi, idx) => {
                              const currencyValue = toShowCurrency(
                                 kpi,
                                 data.currency,
                              );

                              return (
                                 <tr key={idx}>
                                    <td style={{ fontWeight: '500' }}>
                                       {toUpperCase(kpi)}
                                    </td>
                                    <td>
                                       {currencyValue}{' '}
                                       {campaignKpiData[0]?.total_val[kpi]
                                          ? formatValue(
                                               campaignKpiData[0]?.total_val[
                                                  kpi
                                               ],
                                            )
                                          : 'N/A'}
                                    </td>
                                    <td>N/A</td>
                                 </tr>
                              );
                           })
                        )}
                     </tbody>
                  </table>
               </Flex>
            </Flex>

            {adgroupsData && adgroupsData.length > 0 && (
               <div className='ad-set'>
                  <h3>Ad Group Comparison</h3>
                  <div className='adset-overview'>
                     <table className='adset-table'>
                        <thead>
                           <tr>
                              <th>
                                 Ad Groups <LinkIcon />
                              </th>
                              {adgroupsData &&
                                 adgroupsData[0]?.kpis
                                    ?.filter(
                                       (kpi) =>
                                          ![
                                             'cpm',
                                             'conversions_value',
                                             'video_views',
                                             'impressions',
                                             'interactions',
                                             'clicks',
                                             // 'spend',
                                          ].includes(kpi?.kpi),
                                    )
                                    .sort((a, b) => {
                                       const preferredOrder = [
                                          'conversion_rate',
                                          'conversions',
                                          'cpa',
                                          'roas',
                                          'ctr',
                                          'cpc',
                                          'clicks',
                                          'spend',
                                       ];
                                       return (
                                          preferredOrder.indexOf(a.kpi) -
                                          preferredOrder.indexOf(b.kpi)
                                       );
                                    })
                                    .map((kpi, index) => (
                                       <th key={index}>
                                          <Flex
                                             justifyContent='start'
                                             alignItems='center'
                                             cursor='pointer'
                                             onClick={() =>
                                                handleAdGroupSorting(kpi?.kpi)
                                             }
                                             role='group'
                                          >
                                             {kpi?.kpi === 'conversion_rate'
                                                ? 'CVR'
                                                : toUpperCase(kpi?.kpi)}
                                             {sortDetails.metric === kpi.kpi ? (
                                                sortDetails.order === 'asc' ? (
                                                   <LuArrowUp />
                                                ) : (
                                                   <LuArrowDown />
                                                )
                                             ) : (
                                                <Box
                                                   opacity={0}
                                                   transition='opacity 0.2s ease'
                                                   _groupHover={{ opacity: 1 }}
                                                   ml='4px'
                                                >
                                                   <LuArrowDownUp />
                                                </Box>
                                             )}
                                          </Flex>
                                       </th>
                                    ))}
                           </tr>
                        </thead>
                        <tbody>
                           {adgroupDataLoading ||
                           !sortedAdGroups ||
                           sortedAdGroups.length === 0
                              ? Array.from(
                                   { length: adgroupsData?.length },
                                   (_, i) => {
                                      return (
                                         <tr className='body-row' key={i}>
                                            <td className='bodyRow-column'>
                                               <SkeletonText noOfLines={1} />
                                            </td>
                                            {Array.from(
                                               {
                                                  length:
                                                     metricsOptions.length - 1,
                                               },
                                               (_, j) => {
                                                  return (
                                                     <td
                                                        className='bodyRow-column'
                                                        key={j}
                                                     >
                                                        <SkeletonText
                                                           py={6}
                                                           noOfLines={1}
                                                        />
                                                     </td>
                                                  );
                                               },
                                            )}
                                         </tr>
                                      );
                                   },
                                )
                              : sortedAdGroups.map((adgroup, index) => (
                                   <tr key={index}>
                                      <td>
                                         <Link
                                            to=''
                                            className='links'
                                            onClick={() =>
                                               handleAdgroupClick(
                                                  adgroup.ad_group_id,
                                               )
                                            }
                                         >
                                            {adgroup.ad_group_name}
                                         </Link>
                                      </td>

                                      {adgroup.kpis
                                         ?.filter(
                                            (kpi) =>
                                               ![
                                                  'cpm',
                                                  'conversions_value',
                                                  'video_views',
                                                  'impressions',
                                                  'interactions',
                                                  'clicks',
                                                  //   'spend',
                                               ].includes(kpi?.kpi),
                                         )
                                         .sort((a, b) => {
                                            const preferredOrder = [
                                               'conversion_rate',
                                               'conversions',
                                               'cpa',
                                               'roas',
                                               'ctr',
                                               'cpc',
                                               'clicks',
                                               'spend',
                                            ];
                                            return (
                                               preferredOrder.indexOf(a.kpi) -
                                               preferredOrder.indexOf(b.kpi)
                                            );
                                         })
                                         .map((kpi, kpiIndex) => {
                                            const {
                                               percentage,
                                               color,
                                               direction,
                                               currentValue,
                                            } = calculateHelper(
                                               kpi.kpi,
                                               kpi?.kpi_current,
                                               kpi?.kpi_previous,
                                            );
                                            const arrow =
                                               direction === 'is up'
                                                  ? '↑'
                                                  : '↓';

                                            const currencyValue =
                                               toShowCurrency(
                                                  kpi.kpi,
                                                  data.currency,
                                               );

                                            return (
                                               <td
                                                  key={kpiIndex}
                                                  className='adset-kpis'
                                               >
                                                  <div>
                                                     {currencyValue}{' '}
                                                     {currentValue}
                                                  </div>
                                                  <div>
                                                     {percentage &&
                                                        direction && (
                                                           <span
                                                              style={{
                                                                 color,
                                                                 display:
                                                                    'inline-block',
                                                              }}
                                                           >
                                                              {percentage}{' '}
                                                              {arrow}
                                                           </span>
                                                        )}
                                                  </div>
                                               </td>
                                            );
                                         })}
                                   </tr>
                                ))}
                        </tbody>
                     </table>

                     <Flex className='recommendations' direction={'column'}>
                        <h4 className='recommendations-title'>
                           Recommendations for Ad Groups
                        </h4>
                        {isAdgroupSummaryLoading ? (
                           <>
                              <SkeletonText mt={4} noOfLines={3} />
                           </>
                        ) : adgroupsummary?.ad_group?.Recommendation ? (
                           <ul className='recommendations-list'>
                              {extractLinesFromRecommendation(
                                 adgroupsummary?.ad_group?.Recommendation,
                              )?.map((line, index) => (
                                 <li key={index}>{line}</li>
                              ))}
                           </ul>
                        ) : (
                           <ul>No data available</ul>
                        )}
                     </Flex>
                  </div>
               </div>
            )}
         </div>
         {independentAdgroupData && isNestedOpen && (
            <GoogleAdgroupPopup
               data={data}
               adgroup={independentAdgroupData}
               isOpen={isNestedOpen}
               onBack={onClose}
               onClose={handleNestedClose}
               details={nestedDetails}
            />
         )}
      </div>
   );
};

export default GooglePopup;
