import DefaultCard from '@/components/DefaultCard';
import { RootState } from '@/store/store';
import { useSelector } from 'react-redux';
import SubsAnalyticsCard from './subs-analytics-card';
import TokenUsageCard from './token-usages-card';

const Usage = () => {
   const { isActive } = useSelector((state: RootState) => state.subscription);
   const subscribed = isActive !== null;
   return (
      <section className='bg-white p-8 border !border-cloud rounded-xl space-y-8 overflow-auto max-h-[72vh] w-full shadow-sm'>
         <div className='space-y-3'>
            <h2 className='head5 text-jet font-semibold'>Usages</h2>
            <hr className='h-[1px] w-full bg-fog' />
            <p className='para4 text-charcoal font-medium'>
               Get your all agent usages here.
            </p>
         </div>

         {subscribed && (
            <div
               id='subs-details'
               className='flex items-stretch justify-between gap-8'
            >
               <div id='leftsubsDetails' className='flex-shrink-0 basis-[60%]'>
                  <SubsAnalyticsCard />
               </div>
               <div id='rightsubsDetails' className='flex-shrink-0 basis-[35%]'>
                  <TokenUsageCard />
               </div>
            </div>
         )}

         <DefaultCard
            banner='inactivePlan'
            title={
               subscribed ? 'Still Have Questions?' : 'No Active Subscription'
            }
            desc={
               subscribed
                  ? 'Our dedicated team is ready to assist you. Reach out for more information or explore our resources.'
                  : 'Choose a plan that fits your needs and unlock full access to our platform.'
            }
            actionLabel={subscribed ? 'Contact Us' : 'Subscribe to a plan'}
            navigate='/settings?mode=plansTopups&tab=plans'
         />
      </section>
   );
};

export default Usage;
