import {
   Box,
   Text,
   Button,
   Accordion,
   AccordionItem,
   AccordionButton,
   AccordionPanel,
   AccordionIcon,
   FormControl,
   Input,
   FormErrorMessage,
   Tag,
   TagLeftIcon,
   TagLabel,
   Flex,
   useToast,
} from '@chakra-ui/react';
import { CopyIcon } from '@chakra-ui/icons';
import { useState } from 'react';

import { flablePixelStep } from '../../../../utils/strings/onboarding-strings';
import './flable-pixel-step.scss';
import { regex } from '../../../../utils/strings/auth-strings';
import { useAppDispatch, useAppSelector } from '../../../../store/store';
import { Keys, LocalStorageService } from '../../../../utils/local-storage';
import {
   useApiMutation,
   useApiQuery,
} from '../../../../hooks/react-query-hooks';
import keys from '../../../../utils/strings/query-keys';
import onboardingEndpoints from '../../../../api/service/onboarding';
import { setSnippet } from '../../../../store/reducer/onboarding-reducer';

interface Email {
   input: string;
   error: string;
}

interface CopyCode {
   color: string;
   text: string;
}

const FlabelPixelBody = () => {
   const toast = useToast();
   const dispatch = useAppDispatch();

   const [email, setEmail] = useState<Email>({
      input: '',
      error: '',
   });

   const [copyCode, setCopyCode] = useState<CopyCode>({
      color: 'gray',
      text: 'Copy code',
   });

   const { snippet } = useAppSelector((state) => state.onboarding);

   useApiQuery({
      queryKey: [keys.snippet, Keys.ClientId],
      queryFn: () =>
         onboardingEndpoints.fetchSnippet({
            client_id: LocalStorageService.getItem(Keys.ClientId) as string,
         }),
      selectHandler: (data) => {
         dispatch(setSnippet(data.snippet));
         return data;
      },
      enabled: true,
      refetchOnWindowFocus: false,
   });

   const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      const emailError = !regex.email_address.test(value)
         ? 'Please enter a valid email address'
         : '';
      setEmail((prevEmail) => ({
         ...prevEmail,
         error: emailError,
         [name]: value,
      }));
   };

   const { mutate } = useApiMutation({
      queryKey: [keys.sendSnippet],
      mutationFn: onboardingEndpoints.sendSnippetEmail,
      onSuccessHandler: () => {
         toast({
            title: 'Success',
            description: flablePixelStep.sendSnippetSuccessMessage,
            status: 'success',
            duration: 5000,
            isClosable: true,
         });
      },
      onError: () => {
         toast({
            title: 'Failed',
            description: flablePixelStep.sendSnippetFailedMessage,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
   });

   const { mutate: testConnection } = useApiMutation({
      queryKey: [keys.testConnection],
      mutationFn: onboardingEndpoints.testConnection,
      onSuccessHandler: () => {
         toast({
            title: 'Success',
            description: flablePixelStep.testConnectionSuccessMessage,
            status: 'success',
            duration: 5000,
            isClosable: true,
         });
      },
      onError: () => {
         toast({
            title: 'Failed',
            description: flablePixelStep.testConnectionFailedMessage,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
   });

   const handleSendSnippet = () => {
      const payload = {
         email_address_receiver: email.input,
         email_address_sender: LocalStorageService.getItem(
            Keys.UserName,
         ) as string,
         client_id: LocalStorageService.getItem(Keys.ClientId) as string,
      };

      mutate(payload);
   };

   const handleCopyCode = async () => {
      try {
         await navigator.clipboard.writeText(snippet);
         setCopyCode({
            color: 'green',
            text: 'Copied',
         });
         setTimeout(() => {
            setCopyCode({
               color: 'gray',
               text: 'Copy code',
            });
         }, 2000);
      } catch (err) {
         console.error('Failed to copy text: ', err);
      }
   };

   const handleTestConnection = () => {
      const payload = {
         email_address: LocalStorageService.getItem(Keys.UserName) as string,
      };

      testConnection(payload);
   };

   return (
      <Box width='80%' mt={5}>
         <Text
            fontSize='18px'
            fontWeight='700'
            color='#3F3D56'
            padding={2}
            className='panel'
         >
            Refer to this{' '}
            <a
               href='https://youtu.be/f8uhNdkspco'
               target='_blank'
               style={{ color: 'blue' }}
            >
               video
            </a>{' '}
            to know how to install the snippet.
         </Text>
         <Accordion width='100%' defaultIndex={[1]} allowToggle>
            <AccordionItem className='send-snippet'>
               <AccordionButton
                  display='flex'
                  alignItems='center'
                  justifyContent='space-between'
               >
                  <Box textAlign='left'>
                     <Text
                        fontSize='24px'
                        fontWeight='700'
                        color='#3F3D56'
                        className='panel'
                     >
                        {flablePixelStep.sendSnippet}
                     </Text>
                     <Text
                        fontSize='18px'
                        fontWeight='400'
                        color='#3F3D56'
                        className='panel'
                     >
                        {flablePixelStep.tidyInstructions}
                     </Text>
                  </Box>
                  <AccordionIcon />
               </AccordionButton>
               <AccordionPanel pb={4}>
                  <Box mt={2}>
                     <Text className='panel' mb={2}>
                        {flablePixelStep.sendingSnippet}{' '}
                     </Text>
                     <FormControl id='input' ml={1} isInvalid={!!email.error}>
                        <Input
                           className='email-input'
                           placeholder='Email Address *'
                           name='input'
                           value={email.input}
                           onChange={handleInputChange}
                        />
                        {email.error && (
                           <FormErrorMessage>{email.error}</FormErrorMessage>
                        )}
                     </FormControl>
                     <Button
                        className='SendSnippepBtn'
                        ml={1}
                        mt={2}
                        disabled={!email.input || !!email.error}
                        onClick={handleSendSnippet}
                     >
                        {flablePixelStep.buttonText}
                     </Button>
                  </Box>
               </AccordionPanel>
            </AccordionItem>
            <AccordionItem className='send-snippet'>
               <AccordionButton
                  display='flex'
                  alignItems='center'
                  justifyContent='space-between'
               >
                  <Box textAlign='left'>
                     <Text
                        fontSize='24px'
                        fontWeight='700'
                        color='#3F3D56'
                        className='panel'
                     >
                        {flablePixelStep.installSnippet}
                     </Text>
                     <Text
                        fontSize='18px'
                        fontWeight='400'
                        color='#3F3D56'
                        className='panel'
                     >
                        {flablePixelStep.pasteSnippet}
                     </Text>
                  </Box>
                  <AccordionIcon />
               </AccordionButton>
               <AccordionPanel pb={4}>
                  <Tag
                     float='right'
                     size='sm'
                     variant='subtle'
                     colorScheme={copyCode.color}
                     mb={1}
                     sx={{ cursor: 'pointer' }}
                     onClick={() => void handleCopyCode()}
                  >
                     <TagLeftIcon boxSize='12px' as={CopyIcon} />
                     <TagLabel>{copyCode.text}</TagLabel>
                  </Tag>
                  <pre className='snippet'>
                     <code>{snippet}</code>
                  </pre>
                  <Flex alignItems='center' justifyContent='center' mt={3}>
                     <Text
                        fontSize='16px'
                        fontWeight='400'
                        color='#3F3D56'
                        padding={2}
                        className='panel'
                     >
                        Test your connection if already installed the snippet on
                        your website. If you are still having trouble, contact
                        us at{' '}
                        <span style={{ fontWeight: 'bold' }}>
                           <EMAIL>
                        </span>
                        .
                     </Text>
                     <Button
                        size='xl'
                        className='SendSnippepBtn'
                        fontSize='14px'
                        colorScheme='blue'
                        padding={2}
                        width='min-content'
                        onClick={() => handleTestConnection()}
                     >
                        <Text
                           fontSize='16px'
                           fontWeight={500}
                           className='panel'
                        >
                           Test Connection
                        </Text>
                     </Button>
                  </Flex>
               </AccordionPanel>
            </AccordionItem>
         </Accordion>
      </Box>
   );
};

export default FlabelPixelBody;
