import {
   <PERSON><PERSON>,
   <PERSON><PERSON>utton,
   <PERSON><PERSON>se,
   Flex,
   Heading,
   Input,
   InputGroup,
   InputLeftElement,
   Radio,
   RadioGroup,
   Select,
   Spinner,
   Table,
   TableContainer,
   Tbody,
   Td,
   Text,
   Th,
   Thead,
   Tr,
   useToast,
} from '@chakra-ui/react';
import { useState } from 'react';
import { MdCurrencyRupee } from 'react-icons/md';
import { useAppSelector } from '../../../store/store';
import { useDispatch } from 'react-redux';
import {
   setFixedRate,
   setShippingProfiles,
   setWeightBased,
} from '../../../store/reducer/cfo-reducer';
import { useFieldArray, useForm } from 'react-hook-form';
import { ShippingProfileProps } from './profile-name';
import { useApiMutation } from '../../../hooks/react-query-hooks';
import { CFOKeys } from '../../../pages/dashboard/utils/query-keys';
import endPoints, { OutNewId } from '../../../api/service/cfo';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { closeModal } from '../../../store/reducer/modal-reducer';

function RateSetting(props: ShippingProfileProps) {
   const { setstep } = props;
   const modalProps = useAppSelector(
      (state) => state.modal.payload?.modalProps,
   ) as { edit: boolean };
   const { fixedRate, weightBased, name, zones } = useAppSelector(
      (state) => state.cfo.shippingProfile,
   );
   const { shippingProfiles } = useAppSelector((state) => state.cfo);

   const toast = useToast();
   const dispatch = useDispatch();
   const [rateSetting, setRateSetting] = useState<string>(
      weightBased.length > 0 ? '2' : '1',
   );
   const {
      register,
      control,
      trigger,
      formState: { errors },
      clearErrors,
      watch,
   } = useForm({
      defaultValues: {
         shippingProfile:
            weightBased.length > 0
               ? weightBased
               : [
                    {
                       rate: '',
                       minWeight: '',
                       maxWeight: '',
                       measure: 'gm',
                    },
                 ],
      },
      mode: 'onChange',
   });
   const {
      register: registerFR,
      trigger: triggerFR,
      formState: { errors: errorsFR },
      clearErrors: clearFRErr,
      getValues: getFR,
   } = useForm({
      defaultValues: { fixRate: fixedRate },
      mode: 'onChange',
   });
   const { fields, append, remove } = useFieldArray({
      control,
      name: 'shippingProfile',
   });
   const weights = watch('shippingProfile');
   const handleRateSetChange = (nextValue: string) => {
      setRateSetting(nextValue);
      clearErrors();
      clearFRErr();
   };
   const handleSuccess = (data: OutNewId[]) => {
      dispatch(closeModal());
      if (data[0].new_id && !modalProps?.edit) {
         dispatch(
            setShippingProfiles([
               ...shippingProfiles,
               {
                  profile_name: name,
                  id: data[0].new_id,
                  zone: Array.isArray(zones) ? JSON.stringify(zones) : zones,
                  is_fixed: rateSetting == '1',
                  min_weights: weightBased.map((d) => Number(d.minWeight)),
                  rates:
                     rateSetting == '1'
                        ? [Number(getFR('fixRate'))]
                        : weightBased.map((d) => Number(d.rate)),
                  max_weights: weightBased.map((d) => Number(d.maxWeight)),
                  measures: weightBased.map((d) => d.measure),
               },
            ]),
         );
      }
      toast({
         title: 'Shipping Profile Created',
         status: 'success',
         duration: 2000,
         isClosable: true,
      });
   };
   const { mutate: upsertShippingProfile, isPending: saveLoad } =
      useApiMutation({
         queryKey: [CFOKeys.upsertShippingProfile],
         mutationFn: endPoints.upsertShippingProfile,
         onSuccessHandler: handleSuccess,
      });
   const handleStep = (s?: string) => {
      if (s) return setstep(s);
      if (rateSetting == '1') {
         triggerFR()
            .then((valid) => {
               if (valid) {
                  dispatch(setFixedRate(getFR('fixRate')));
                  upsertShippingProfile({
                     clientId: LocalStorageService.getItem(
                        Keys.ClientId,
                     ) as string,
                     profileName: name,
                     zones: Array.isArray(zones)
                        ? zones.map((z) => {
                             return {
                                name: z.name,
                                continent: z.continent,
                             };
                          })
                        : 'worldwide',
                     fixedRate: getFR('fixRate'),
                     weightBased: [],
                  });
               }
            })
            .catch(console.log);
      } else {
         trigger()
            .then((valid) => {
               if (valid) {
                  dispatch(setWeightBased(weights));
                  upsertShippingProfile({
                     clientId: LocalStorageService.getItem(
                        Keys.ClientId,
                     ) as string,
                     profileName: name,
                     zones: Array.isArray(zones)
                        ? zones.map((z) => {
                             return {
                                name: z.name,
                                continent: z.continent,
                             };
                          })
                        : 'worldwide',
                     fixedRate: '',
                     weightBased: weights,
                  });
               }
            })
            .catch(console.log);
      }
   };
   return (
      <>
         <Flex direction={'column'} gap={4} width={'100%'}>
            <Text>Fulfillment Rate Setting</Text>

            <RadioGroup
               value={rateSetting}
               onChange={handleRateSetChange}
               display={'flex'}
               flexDirection={'column'}
               gap={3}
            >
               <Radio value='1'>Fixed Rate</Radio>
               <Radio value='2'>Order weight-based tiered rates</Radio>
            </RadioGroup>
            <Collapse in={rateSetting == '1'}>
               <Heading fontSize={'17px'} fontWeight={'400'} my={4}>
                  Shipping Rate
               </Heading>
               <InputGroup>
                  <InputLeftElement>
                     <MdCurrencyRupee />
                  </InputLeftElement>
                  <Input
                     type='number'
                     {...registerFR(`fixRate`, {
                        required: 'Shipping Rate is required',
                     })}
                  />
               </InputGroup>
               {errorsFR.fixRate && (
                  <p className='err-message' role='alert'>
                     {'  '}
                     {errorsFR.fixRate?.message}
                  </p>
               )}
            </Collapse>
            <Collapse in={rateSetting == '2'}>
               <form>
                  <TableContainer
                     className='shipping-rate-fields'
                     my={5}
                     mx={4}
                  >
                     <Table variant='simple'>
                        <Thead>
                           <Tr>
                              <Th>Shipping Rate</Th>
                              <Th>Min Weight</Th>
                              <Th> Max Weight</Th>
                              <Th></Th>
                              <Th></Th>
                           </Tr>
                        </Thead>
                        <Tbody>
                           {fields.map((field, idx) => (
                              <Tr key={field.id}>
                                 <Td>
                                    <InputGroup>
                                       <InputLeftElement>
                                          <MdCurrencyRupee />
                                       </InputLeftElement>
                                       <Input
                                          type='number'
                                          {...register(
                                             `shippingProfile.${idx}.rate`,
                                             {
                                                required:
                                                   'Shipping Rate is required',
                                             },
                                          )}
                                       />
                                    </InputGroup>
                                    {errors.shippingProfile?.[idx]?.rate && (
                                       <p className='err-message' role='alert'>
                                          {'  '}
                                          {
                                             errors.shippingProfile?.[idx]?.rate
                                                ?.message
                                          }
                                       </p>
                                    )}
                                 </Td>
                                 <Td>
                                    <InputGroup>
                                       <InputLeftElement>
                                          <MdCurrencyRupee />
                                       </InputLeftElement>
                                       <Input
                                          type='number'
                                          {...register(
                                             `shippingProfile.${idx}.minWeight`,
                                             {
                                                required:
                                                   'Minimum weight is required',
                                             },
                                          )}
                                       />
                                    </InputGroup>
                                    {errors.shippingProfile?.[idx]
                                       ?.minWeight && (
                                       <p className='err-message' role='alert'>
                                          {'  '}
                                          {
                                             errors.shippingProfile?.[idx]
                                                ?.minWeight?.message
                                          }
                                       </p>
                                    )}
                                 </Td>
                                 <Td>
                                    <InputGroup>
                                       <InputLeftElement>
                                          <MdCurrencyRupee />
                                       </InputLeftElement>
                                       <Input
                                          type='number'
                                          {...register(
                                             `shippingProfile.${idx}.maxWeight`,
                                             {
                                                required:
                                                   'Maximum weight is required',
                                             },
                                          )}
                                       />
                                    </InputGroup>
                                    {errors.shippingProfile?.[idx]
                                       ?.maxWeight && (
                                       <p className='err-message' role='alert'>
                                          {'  '}
                                          {
                                             errors.shippingProfile?.[idx]
                                                ?.maxWeight?.message
                                          }
                                       </p>
                                    )}
                                 </Td>
                                 <Td>
                                    <Select
                                       max-width={'fit-content'}
                                       {...register(
                                          `shippingProfile.${idx}.measure`,
                                       )}
                                    >
                                       <option value='gm'>{'gm'}</option>
                                       <option value='kg'>{'kg'}</option>
                                    </Select>
                                 </Td>
                                 <Td>
                                    {idx > 0 && (
                                       <CloseButton
                                          onClick={() => {
                                             remove(idx);
                                          }}
                                       />
                                    )}
                                 </Td>
                              </Tr>
                           ))}
                        </Tbody>
                     </Table>
                  </TableContainer>

                  <Button
                     border={'1px solid #c2cbd4'}
                     background={'none'}
                     onClick={() => {
                        append({
                           rate: '',
                           minWeight: '',
                           maxWeight: '',
                           measure: 'gm',
                        });
                     }}
                  >
                     Add Another Rate
                  </Button>
               </form>
            </Collapse>
         </Flex>
         <Flex
            borderTop={'1px solid #C2CBD4'}
            pt={4}
            mt={5}
            width={'100%'}
            justifyContent={'flex-end'}
            gap={4}
         >
            <Button
               onClick={() => handleStep('1')}
               border={'1px solid #C2CBD4'}
               background={'none'}
               borderRadius={'7px'}
               py={4}
               px={6}
            >
               Previous
            </Button>
            <Button
               onClick={() => handleStep()}
               color={'white'}
               _hover={{
                  backgroundColor: '#437EEBBB',
               }}
               backgroundColor={'#437EEB'}
               py={4}
               px={6}
               border={'1px solid #437EEB'}
               borderRadius={'7px'}
               disabled={saveLoad}
            >
               Save{' '}
               {saveLoad && (
                  <Spinner
                     ml={2}
                     thickness='4px'
                     speed='0.65s'
                     emptyColor='gray.200'
                     color='blue.500'
                     size='sm'
                  />
               )}
            </Button>
         </Flex>
      </>
   );
}

export default RateSetting;
