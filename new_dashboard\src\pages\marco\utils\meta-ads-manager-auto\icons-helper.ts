import { IoIosCheckbox } from 'react-icons/io';
import { MdCampaign } from 'react-icons/md';
import { FaAdversal } from 'react-icons/fa';
import { TbListDetails } from 'react-icons/tb';
import { FcCheckmark } from 'react-icons/fc';
import { VscTarget } from 'react-icons/vsc';
import { TbBulbFilled } from 'react-icons/tb';
import { MdBrush } from 'react-icons/md';
import { FcSettings } from 'react-icons/fc';
import { PiLaptopFill } from 'react-icons/pi';
import { GrSecure } from 'react-icons/gr';
import { PiRepeatFill } from 'react-icons/pi';
import { FaTag } from 'react-icons/fa6';
import { IoMdImages } from 'react-icons/io';
import React from 'react';

const iconMap: Record<string, React.ComponentType<{ className?: string }>> = {
   checkbox: IoIosCheckbox,
   campaign: MdCampaign,
   ad: FaAdversal,
   mark: Fc<PERSON>he<PERSON>mark,
   target: VscTarget,
   bulb: TbBulbFilled,
   creative: MdBrush,
   setting: FcSettings,
   laptop: PiLaptopFill,
   secure: GrSecure,
   tag: FaTag,
   repeat: PiRepeatFill,
   image: IoMdImages,
   moreDetails: TbListDetails,
};

export function replacePlaceholders(
   children: React.ReactNode,
): React.ReactNode {
   function mapChildren(child: React.ReactNode): React.ReactNode {
      return replacePlaceholders(child);
   }
   if (typeof children === 'string') {
      const parts = children.split(/(\[:[^\]]+:\])/g);
      return parts.map((part, index) => {
         const match = part.match(/\[:([^\]]+):\]/);
         if (match) {
            const iconName = match[1];
            const Icon = iconMap[iconName];
            if (Icon) {
               return React.createElement(Icon, {
                  key: index,
                  className: 'inline-block text-gray-500',
               });
            }
            return `[${iconName}]`;
         }
         return part;
      });
   }

   if (Array.isArray(children)) {
      return children.map(mapChildren);
   }

   return children;
}
