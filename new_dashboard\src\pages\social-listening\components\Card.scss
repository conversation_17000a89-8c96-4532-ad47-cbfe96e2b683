@use '../../../sass/variable.scss';
.IntCardWrapper {
   display: flex;
   flex-direction: column;

   .card {
      width: 190px;
      min-height: 210px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      border-radius: 8px;
      border: 1px solid rgba(160, 164, 168, 0.22);
      background: #fff;

      .avatar {
         width: 88px;
         height: 88px;
         flex-shrink: 0;
         object-fit: scale-down;
         border-radius: 50%;

         &.onboarding {
            width: 50px;
            height: 50px;
         }
      }

      .linkedin-avatar {
         width: 290px;
         height: 88px;
         flex-shrink: 0;
         object-fit: scale-down;
         border-radius: 50%;

         &.onboarding {
            width: 100px;
            height: 50px;
         }
      }
      .hubspot-avatar {
         width: 77px;
         height: 75px;
      }

      &.onboarding {
         width: 170px;
         min-height: 175px;
      }

      /* Add some padding inside the card container */
      &-container {
         padding: 7px 2px;
         margin: 5px 0;

         &.onboarding {
            font-size: 14px;
         }

         h4 {
            text-align: center;
         }

         .error {
            color: red;
            font-size: 13px;
         }
      }

      button {
         color: #337cdf;
         font-size: 12px;
         font-style: normal;
         font-weight: 400;
         line-height: normal;
         border-radius: 5px;
         border: 1px solid #337cdf;
         background: transparent;
         padding: 6px 10px;
         margin-top: 10px;

         display: flex;
         align-items: center;
         gap: 10px;

         &.soon {
            cursor: auto;
            color: orange;
            border: 1px solid orange;
         }

         &.disconnect {
            color: red;
            border: 1px solid red;
         }
      }
   }

   .banner {
      display: flex;
      justify-content: center;
      align-items: center;
      color: #36b37e;
      font-weight: 400;
      background-color: #b7f7dc;
      height: 40px;
      font-size: 17px;
      border-bottom-left-radius: 10px;
      border-bottom-right-radius: 10px;
   }
}
// Dark theme styles
// [data-theme='dark'] {
//    background: $background;
//    border: 1px solid rgba(255, 255, 255, 0.22);

//    .card {
//       background: $background_surface;
//    }

//    .banner {
//       background-color: $background_light;
//       color: #fff;
//    }
// }
.integrationCardHeading {
   [data-theme='dark'] & {
      color: #fff;
   }
}
