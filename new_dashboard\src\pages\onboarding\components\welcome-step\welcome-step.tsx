import { Box, Button, Radio, RadioGroup, Text, Stack } from '@chakra-ui/react';
import { useAppSelector, useAppDispatch } from '../../../../store/store';

import './welcome-step.scss';
import { welcomeStep } from '../../../../utils/strings/onboarding-strings';
import {
   setRegisterProgress,
   setOrganizationType,
} from '../../../../store/reducer/onboarding-reducer';

const WelcomeStep = () => {
   const dispatch = useAppDispatch();

   const { organizationType } = useAppSelector((state) => state.onboarding);

   const handleOrganizationTypeChange = (
      value: 'Individual Business' | 'Marketing Agency',
   ): void => {
      dispatch(setOrganizationType(value));
   };

   const handleClickNext = (): void => {
      dispatch(setRegisterProgress('Step 2'));
   };

   return (
      <Box
         width='100%'
         height='100%'
         display='flex'
         flexDirection='column'
         gap={5}
         justifyContent='center'
         alignItems='center'
         className='welcome-step'
         padding={{ base: '20px', md: '40px' }} // Adjust padding for different screens
      >
         <Text
            color='#424242'
            fontSize={{ base: '24px', md: '32px', lg: '40px' }}
            fontWeight={700}
         >
            {welcomeStep.title}
         </Text>

         <Box
            width={{ base: '90%', sm: '80%', md: '60%', lg: '65%', xl: '50%' }} // Responsive width
            height='auto'
            minHeight='400px'
            boxShadow='0px 0px 4px 0px #00000040'
            borderRadius='8px'
            display='flex'
            flexDirection='column'
            gap={5}
            padding='20px'
            alignItems='center'
            justifyContent='space-around'
         >
            <Text
               fontSize={{ base: '1rem', md: '1.5rem' }}
               fontWeight={500}
               textAlign='center'
               width='80%'
            >
               {welcomeStep.info}
            </Text>

            <Box display='flex' flexDirection='column' gap={5}>
               <Text
                  fontSize={{ base: '0.75rem', md: '1.25rem' }}
                  textAlign='center'
               >
                  {welcomeStep.question}
               </Text>

               <Box fontSize='1.5rem'>
                  <RadioGroup
                     defaultValue={organizationType}
                     value={organizationType}
                     onChange={handleOrganizationTypeChange}
                  >
                     <Stack
                        direction={{ base: 'column', md: 'row' }}
                        gap={5}
                        align='center'
                        justify='center'
                     >
                        <Radio value='Individual Business'>
                           <Text
                              fontSize={{ base: '0.5rem', md: '1rem' }}
                              textAlign='center'
                           >
                              Individual Business
                           </Text>
                        </Radio>
                        <Radio value='Marketing Agency'>
                           <Text
                              fontSize={{ base: '0.5rem', md: '1rem' }}
                              textAlign='center'
                           >
                              Marketing Agency
                           </Text>
                        </Radio>
                     </Stack>
                  </RadioGroup>
               </Box>
            </Box>
         </Box>

         <Button
            padding='4px 16px'
            backgroundColor='#437EEB'
            color='#fff'
            onClick={handleClickNext}
            width={{ base: '80%', md: 'auto' }} // Full-width on small screens, auto on larger
         >
            <Text fontSize={{ base: '0.75rem', md: '1rem' }} fontWeight={500}>
               Next
            </Text>
         </Button>
      </Box>
   );
};

export default WelcomeStep;
