.cost-setting {
   max-height: 100vh;
   overflow: auto;
   &::-webkit-scrollbar {
      width: 5px;
   }
   &::-webkit-scrollbar-thumb {
      background: #4d4d4daa;
      border-radius: 5px;
   }
   .children {
      max-height: 65vh;
      overflow: auto !important;
      &::-webkit-scrollbar {
         width: 5px;
      }
      &::-webkit-scrollbar-thumb {
         background: #4d4d4daa;
         border-radius: 5px;
      }
   }
   .add-text {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 4px 8px;
      gap: 5px;
      border-radius: 7px;
      font-size: 14px;
      svg {
         height: 20px;
         width: 20px;
      }
   }
   .defaultShippping {
      svg {
         height: 20px;
         width: 20px;
      }
   }
   .order-dynamic {
      svg {
         height: 15px;
         width: 15px;
      }
   }
   .cog-table,
   .ce-table {
      border: 1px solid #c2cbd4;
      border-radius: 7px;
      overflow: auto;
      // svg {
      //    height: 20px;
      //    width: 20px;
      // }
      th,
      td {
         font-size: 15px;
         font-weight: 400;
         color: black;
         border: none;
         button svg {
            height: 20px;
            width: 20px;
         }
         min-width: 200px;
      }

      th {
         text-transform: none;
         font-family: inherit;
         font-weight: 500;
         padding: 15px;
         border-bottom: 1px solid #c2cbd4;
      }
   }
   .ce-table {
      td {
         padding: 10px 15px;
      }
   }
   .custom-expenses {
      padding: 20px;
      border: 1px solid #c2cbd4;
      border-radius: 7px;
      margin: 10px;

      margin-top: 20px;
      .btns button {
         background: none;
         font-size: 15px;
         font-weight: 400;
         color: #5174c9;
      }
   }
}
.react-datepicker-wrapper,
.category {
   input {
      background: none;
      padding: 5px 15px;
      border: 1px solid #e2e8f0;
      border-radius: 6px;
      &:focus-visible {
         outline: none;
         border: 2px solid #3182ce;
      }
      &:disabled {
         color: grey;
         cursor: not-allowed;
         width: 130px;
      }
   }
}
.source {
   input {
      background: none;
      border: 1px solid #e2e8f0;
      border-radius: 6px;
      &:focus-visible {
         outline: none;
         border: 2px solid #3182ce;
      }
      &:disabled {
         color: grey;
         cursor: not-allowed;
         width: 130px;
      }
   }
}
