import subsEndPoints from '@/api/service/subscription';
import DefaultCard from '@/components/DefaultCard';
import { Badge } from '@/components/ui/badge';
import { useApiQuery } from '@/hooks/react-query-hooks';
import { UserDetails } from '@/pages/socialwatch/interface';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { Skeleton } from '@/components/ui/skeleton';
import { format } from 'date-fns';

export default function TopupTable() {
   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);

   const { data: topupRecords, isLoading: isTopupRecordsLoading } = useApiQuery(
      {
         queryKey: ['getTopupRecords', userDetails?.client_id],
         queryFn: () => subsEndPoints.getTopupRecords(userDetails?.client_id),
         enabled: !!userDetails?.client_id,
         refetchOnWindowFocus: false,
      },
   );

   return (
      <div className='w-full border border-gray-200 bg-white shadow-sm rounded-lg overflow-hidden'>
         <div className='overflow-x-auto min-h-[400px] max-h-[400px] overflow-y-auto scrollable'>
            <table className='w-full min-w-[1200px]'>
               <thead className='bg-gray-50 sticky top-0 z-20'>
                  <tr className='sticky top-0 z-20 bg-navy text-white'>
                     <th
                        colSpan={6}
                        className='py-3 px-6 text-lg font-bold text-left tracking-wide sticky top-0 z-20 bg-navy text-white'
                     >
                        Top-ups
                     </th>
                  </tr>
                  <tr className='border-b border-gray-200 bg-gray-50 sticky top-[48px] z-10'>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-left'>
                        Plan
                     </th>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-left'>
                        Currency
                     </th>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-left'>
                        Amount
                     </th>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-left'>
                        Status
                     </th>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-left'>
                        Created At
                     </th>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-left'>
                        Label
                     </th>
                  </tr>
               </thead>
               <tbody>
                  {isTopupRecordsLoading ? (
                     Array.from({ length: 4 }).map((_, idx) => (
                        <tr key={idx} className='border-b border-gray-100'>
                           <td className='py-4 px-6'>
                              <Skeleton className='h-4 w-24' />
                           </td>
                           <td className='py-4 px-6'>
                              <Skeleton className='h-4 w-16' />
                           </td>
                           <td className='py-4 px-6'>
                              <Skeleton className='h-4 w-20' />
                           </td>
                           <td className='py-4 px-6'>
                              <Skeleton className='h-6 w-16 rounded-full' />
                           </td>
                           <td className='py-4 px-6'>
                              <Skeleton className='h-4 w-28' />
                           </td>
                           <td className='py-4 px-6'>
                              <Skeleton className='h-4 w-20' />
                           </td>
                        </tr>
                     ))
                  ) : topupRecords?.topups?.length ? (
                     topupRecords.topups.map((topup, index) => (
                        <tr
                           key={topup.id}
                           className={`border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150 ${
                              index % 2 === 0 ? 'bg-white' : 'bg-gray-25'
                           }`}
                        >
                           <td className='py-4 px-6 font-medium text-gray-900'>
                              {topup.plan_name}
                           </td>
                           <td className='py-4 px-6 text-gray-700'>
                              {topup.currency}
                           </td>
                           <td className='py-4 px-6 font-semibold text-gray-900'>
                              ₹{topup.amount.toLocaleString()}
                           </td>
                           <td className='py-4 px-6'>
                              <Badge
                                 variant={
                                    topup.status === 'paid'
                                       ? 'success'
                                       : topup.status === 'failed'
                                         ? 'destructive'
                                         : 'outline'
                                 }
                                 className='capitalize font-medium px-3 py-1'
                              >
                                 {topup.status}
                              </Badge>
                           </td>
                           <td className='py-4 px-6 text-gray-700 text-sm'>
                              {topup.created_at
                                 ? format(
                                      new Date(topup.created_at),
                                      'dd MMM yyyy, hh:mm a',
                                   )
                                 : '-'}
                           </td>
                           <td className='py-4 px-6 text-gray-700'>
                              {topup.label || '-'}
                           </td>
                        </tr>
                     ))
                  ) : (
                     <tr>
                        <td colSpan={6} className='py-8 px-6'>
                           <DefaultCard
                              banner='inactivePlan'
                              imgClassName='w-28'
                              title='No Top-up History Found'
                              titleClassName='head6'
                           />
                        </td>
                     </tr>
                  )}
               </tbody>
            </table>
         </div>
      </div>
   );
}
