import React, { useEffect } from 'react';
import Swal from 'sweetalert2';
import Card from './Card';
import image from '../images/integrations/linkedin.png';
import darkImage from '../../../assets/icons/kpi/linkedin-dark-theme-removebg-preview__1_-removebg-preview (1).png';

import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { AuthUser } from '../../../types/auth';
import { dialogMessage } from '../../../utils/strings/content-manager';
import socialWatchEndpoints from '../../../api/service/social-watch/apis';
import { useAppDispatch, useAppSelector } from '../../../store/store';
import { openModal } from '../../../store/reducer/modal-reducer';
import { modalTypes } from '../../../components/modals/modal-types';

import {
   setIsDisconnecting,
   setLinkedinConnection,
} from '../../../store/reducer/integration-reducer';
import { connectToLinkedinSentiment } from '../utils';
import { loadingStateChannel } from '../../dashboard/utils/query-keys';
import { useColorMode } from '@chakra-ui/react';
import { useLocation } from 'react-router-dom';

const Linkedin: React.FC = () => {
   const client_id = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;
   const dispatch = useAppDispatch();
   const { colorMode } = useColorMode();
   const location = useLocation();

   const redirectPathName =
      location.pathname === '/onboarding' ? 'onboarding' : 'integrations';

   async function removeAccount() {
      const result = await Swal.fire({
         title: dialogMessage.delete.title,
         text: dialogMessage.delete.description,
         icon: 'warning',
         showCancelButton: true,
         confirmButtonColor: '#3085d6',
         cancelButtonColor: '#d33',
         confirmButtonText: dialogMessage.delete.buttonMessage,
      });

      if (result.isConfirmed) {
         try {
            dispatch(setIsDisconnecting(loadingStateChannel.LINKEDIN));
            await connectToLinkedinSentiment({
               client_id: client_id!,
               isConnect: false,
            });
            dispatch(setLinkedinConnection(null));
         } catch (err) {
            console.error('Failed to disconnect');
         } finally {
            dispatch(setIsDisconnecting(null));
         }
      }
   }

   const {
      isFetching,
      isConnecting,
      isDisconnecting,
      connections: { linkedin },
   } = useAppSelector((state) => state.integration);

   const onButtonClickUpdated = () => {
      if (linkedin) {
         void removeAccount();
      } else {
         const newTab = window.open('', '_blank');
         if (newTab)
            newTab.location.href = socialWatchEndpoints.authorize(
               `/linkedin-auth/authorize?client=${client_id!}&redirectPathName=${redirectPathName}`,
            );
      }
   };

   useEffect(() => {
      function saveLinkedinDetails() {
         try {
            const urlParams = new URLSearchParams(window.location.search);
            const accessToken = urlParams.get('access_token');
            const refreshToken = urlParams.get('refresh_token');

            if (!accessToken) return;
            // NEED to show the modal
            dispatch(
               openModal({
                  modalType: modalTypes.LINKDEIN_PAGE_SELECT,
                  modalProps: {
                     accessToken,
                     refreshToken,
                     clientId: client_id,
                  },
               }),
            );
         } catch (err) {
            console.log('ERR', err);
         }
      }
      saveLinkedinDetails();
   }, []);

   const isConnected = linkedin ? Boolean(linkedin.user.userId) : false;

   return (
      <Card
         heading={linkedin?.user.screenName || 'Linkedin'}
         isConnecting={isConnecting?.includes(loadingStateChannel.LINKEDIN)}
         isDisconnecting={isDisconnecting?.includes(
            loadingStateChannel.LINKEDIN,
         )}
         src={colorMode === 'dark' ? darkImage : image}
         onButtonClick={() => void onButtonClickUpdated()}
         isConnected={isConnected}
         isFetching={isFetching}
      />
   );
};

export default Linkedin;
