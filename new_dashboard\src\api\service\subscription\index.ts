import dashboardApiAgent from '@/api/agent';
import { SubscriptionEndpoints } from '@/pages/settings/subscription/types';

const subsEndPoints: SubscriptionEndpoints = {
   fetchPlans: () => dashboardApiAgent.get('/subs/plans-info'),
   fetchAgents: () => dashboardApiAgent.get('/subs/agents-info'),
   createRazorpayCustomer: (payload) =>
      dashboardApiAgent.post('/subs/razorpay/customers', payload),
   subscribeToPlan: (payload) =>
      dashboardApiAgent.post('/subs/razorpay/subscriptions', payload),
   cancelSubscription: (client_id: string) =>
      dashboardApiAgent.post(
         `/subs/razorpay/subscriptions/cancel?client_id=${client_id}`,
      ),
   getSubscriptionContractRecords: (client_id: string) =>
      dashboardApiAgent.get(`/subs/subscriptions?client_id=${client_id}`),
   getSubscriptionHistoryRecords: (client_id: string) =>
      dashboardApiAgent.get(
         `/subs/subscriptions-history?client_id=${client_id}`,
      ),
   purchaseTopup: (payload) =>
      dashboardApiAgent.post('/subs/razorpay/orders', payload),

   getPaymentRecords: (client_id: string) =>
      dashboardApiAgent.get(`/subs/payments?client_id=${client_id}`),
   getTopupRecords: (client_id: string) =>
      dashboardApiAgent.get(`/subs/topups?client_id=${client_id}`),
   getAgentUsages: (client_id: string) =>
      dashboardApiAgent.get(`/subs/agent-usages?client_id=${client_id}`),
   getClientAllInvoiceRecord: (client_id: string) =>
      dashboardApiAgent.get(`/subs/invoices?client_id=${client_id}`),
};

export default subsEndPoints;
