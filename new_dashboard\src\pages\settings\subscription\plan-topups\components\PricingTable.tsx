import { useState } from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { PiCheckBold } from 'react-icons/pi';
import { AiOutlineDash } from 'react-icons/ai';
import {
   Attributes,
   DashboardIntegrations,
   PlansTableProps,
   CollapsibleSections,
} from '../../types';
import { cap } from '@/pages/pulse/utils/helper';

const Tick = () => <PiCheckBold size={16} className='text-lime shrink-0' />;

const Dash = () => <AiOutlineDash size={16} className='text-charcoal' />;

const PlansTable = ({
   plansData,
   extraOptions,
   supportOptions,
}: PlansTableProps) => {
   const agentKeys = Object.keys(
      plansData[0].attribute?.agents,
   ) as (keyof Attributes['agents'])[];
   const integrationsKeys = Object.keys(
      plansData[0].attribute?.dashboard_connectors || {},
   ).filter(
      (key) => key !== 'social_media' && key !== 'shopify_store',
   ) as (keyof DashboardIntegrations)[];
   const socialMediaKeys = Object.keys(
      plansData[0]?.attribute?.dashboard_connectors?.social_media,
   ) as (keyof DashboardIntegrations['social_media'])[];

   const [collapsed, setCollapsed] = useState<CollapsibleSections>({
      agents: true,
      integrations: true,
      socialMedia: true,
      pulse: true,
      users: true,
   });

   const toggleSection = (section: keyof CollapsibleSections) => {
      setCollapsed((prev) => ({
         ...prev,
         [section]: !prev[section],
      }));
   };

   return (
      <div className='overflow-x-auto para-6 text-charcoal scrollable'>
         <table className='text-left w-full border-collapse'>
            <thead>
               <tr>
                  <th className='p-3 min-w-80 text-jet head6 font-medium'>
                     Compare Our Plans
                  </th>
                  {plansData.map((plan) => (
                     <th key={plan.name} className='p-3 text-center space-y-4'>
                        <div>{plan.name}</div>
                        <div className='flex justify-center gap-1.5 mt-1'>
                           {[1, 2, 3, 4, 5].map((i) => (
                              <div
                                 key={i}
                                 className={`w-2.5 h-2.5 rounded-full ${
                                    i <= plan.tier
                                       ? 'bg-navy'
                                       : 'border-1 !border-steel'
                                 }`}
                              />
                           ))}
                        </div>
                     </th>
                  ))}
               </tr>
            </thead>
            <tbody>
               {/* Separator */}
               <tr>
                  <td colSpan={plansData.length + 1}>
                     <div className='border-b border-fog w-full'></div>
                  </td>
               </tr>

               {/* Agents */}
               <tr>
                  <td colSpan={plansData.length + 1}>
                     <button
                        onClick={() => toggleSection('agents')}
                        className='flex items-center gap-2 py-2 font-semibold w-full text-left para5'
                     >
                        {collapsed.agents ? (
                           <ChevronDown size={16} />
                        ) : (
                           <ChevronRight size={16} />
                        )}
                        Agents
                     </button>
                  </td>
               </tr>
               {collapsed.agents && (
                  <>
                     {agentKeys.map((agent) => (
                        <tr key={String(agent)}>
                           <td className='p-3 font-semibold para6'>
                              {typeof agent === 'string'
                                 ? agent.replace(/_/g, ' ')
                                 : String(agent)}
                           </td>
                           {plansData.map((plan) => {
                              const agentKey = agent;
                              const agentData = plan.attribute.agents[agentKey];
                              return (
                                 <td key={plan.name} className='p-3'>
                                    <div className='flex justify-center items-center'>
                                       {agentData?.access ? <Tick /> : <Dash />}
                                    </div>
                                 </td>
                              );
                           })}
                        </tr>
                     ))}
                     <tr>
                        <td className='p-3 para5 font-medium'>
                           Pulse Insights (Deep Ad Analysis)
                        </td>
                        {plansData.map((plan) => (
                           <td key={plan.name} className='para6'>
                              <div className='flex justify-center items-center'>
                                 {plan.attribute[
                                    'Pulse Insights (Deep Ad Analysis)'
                                 ] ? (
                                    <Tick />
                                 ) : (
                                    <Dash />
                                 )}
                              </div>
                           </td>
                        ))}
                     </tr>
                     <tr>
                        <td className='p-3 para5 font-medium'>
                           Extra Agent Usages (PAYG)
                        </td>
                        {plansData.map((plan) => (
                           <td key={plan.name} className='p-3 para6'>
                              <div className='flex justify-center items-center'>
                                 {plan.attribute[
                                    'Extra Agent Usages (PAYG)'
                                 ] ? (
                                    <Tick />
                                 ) : (
                                    <Dash />
                                 )}
                              </div>
                           </td>
                        ))}
                     </tr>
                  </>
               )}
               {/* }  */}

               <tr>
                  <td colSpan={plansData.length + 1}>
                     <div className='border-b border-fog w-full'></div>
                  </td>
               </tr>

               {/* Dashboard Integrations */}
               <tr>
                  <td colSpan={plansData.length + 1}>
                     <button
                        onClick={() => toggleSection('integrations')}
                        className='flex items-center gap-2 py-2 font-semibold w-full text-left para5'
                     >
                        {collapsed.integrations ? (
                           <ChevronDown size={16} />
                        ) : (
                           <ChevronRight size={16} />
                        )}
                        Dashboard Integrations
                     </button>
                  </td>
               </tr>
               {/* Shopify Store Integration */}
               {collapsed.integrations && (
                  <>
                     <tr>
                        <td className='p-3 font-semibold para6'>
                           Shopify Store
                        </td>
                        {plansData.map((plan) => (
                           <td key={plan.name} className='p-3 capitalize'>
                              <div className='flex justify-center items-center'>
                                 <div className='flex justify-center items-center'>
                                    {plan.attribute?.dashboard_connectors
                                       ?.shopify_store ? (
                                       <Tick />
                                    ) : (
                                       <Dash />
                                    )}
                                 </div>
                              </div>
                           </td>
                        ))}
                     </tr>
                     {/* Extra Section Title */}
                     <tr>
                        <td>
                           <div className='p-3 font-bold para5 text-left'>
                              Extra
                           </div>
                        </td>
                        {extraOptions.map((option, idx) => (
                           <td
                              key={idx}
                              className='p-3 font-semibold para6 text-center'
                           >
                              {option}
                           </td>
                        ))}
                     </tr>
                     {integrationsKeys.map((integration) => (
                        <tr key={integration}>
                           <td className='p-3 font-semibold para6'>
                              {cap(integration)}
                           </td>
                           {plansData.map((plan) => (
                              <td key={plan.name} className='p-3 capitalize'>
                                 <div className='flex justify-center items-center'>
                                    {typeof plan.attribute
                                       ?.dashboard_connectors[integration] ===
                                    'boolean' ? (
                                       plan.attribute?.dashboard_connectors[
                                          integration
                                       ] ? (
                                          <Tick />
                                       ) : (
                                          <Dash />
                                       )
                                    ) : (
                                       <Dash />
                                    )}
                                 </div>
                              </td>
                           ))}
                        </tr>
                     ))}
                  </>
               )}
               <tr>
                  <td colSpan={plansData.length + 1}>
                     <div className='border-b border-fog w-full'></div>
                  </td>
               </tr>

               {/* Social Media Integrations */}
               <tr>
                  <td colSpan={plansData.length + 1}>
                     <button
                        onClick={() => toggleSection('socialMedia')}
                        className='flex items-center gap-2 py-2 font-semibold w-full text-left para5'
                     >
                        {collapsed.socialMedia ? (
                           <ChevronDown size={16} />
                        ) : (
                           <ChevronRight size={16} />
                        )}
                        Social Media Integrations
                     </button>
                  </td>
               </tr>
               {collapsed.socialMedia &&
                  socialMediaKeys.map((social) => (
                     <tr key={social}>
                        <td className='p-3 font-semibold para6'>{social}</td>
                        {plansData.map((plan) => (
                           <td key={plan.name} className='p-3'>
                              <div className='flex justify-center items-center'>
                                 {plan.attribute?.dashboard_connectors
                                    .social_media[social] ? (
                                    <Tick />
                                 ) : (
                                    <Dash />
                                 )}
                              </div>
                           </td>
                        ))}
                     </tr>
                  ))}
               <tr>
                  <td colSpan={plansData.length + 1}>
                     <div className='border-b border-fog w-full'></div>
                  </td>
               </tr>

               {/* User Limit */}
               <tr>
                  <td colSpan={plansData.length + 1}>
                     <button
                        onClick={() => toggleSection('users')}
                        className='flex items-center gap-2 py-2 font-semibold w-full text-left para5'
                     >
                        {collapsed.users ? (
                           <ChevronDown size={16} />
                        ) : (
                           <ChevronRight size={16} />
                        )}
                        Users
                     </button>
                  </td>
               </tr>
               {collapsed.users && (
                  <tr>
                     <td className='p-3 para6 font-medium'>User Limit</td>
                     {plansData.map((plan) => (
                        <td key={plan.name} className='p-3 para6 font-semibold'>
                           <div className='flex justify-center items-center'>
                              {String(
                                 plan.max_users === null
                                    ? 'Unlimited'
                                    : plan.max_users,
                              )}
                           </div>
                        </td>
                     ))}
                  </tr>
               )}
               <tr>
                  <td colSpan={plansData.length + 1}>
                     <div className='border-b border-fog w-full'></div>
                  </td>
               </tr>

               {/* Static Section */}
               <tr>
                  <td className='p-3 para5 font-medium'>360° Dashboard</td>
                  {plansData.map((plan) => (
                     <td key={plan.name} className='p-3 para6'>
                        <div className='flex justify-center items-center'>
                           {plan.attribute['360 degree Dashboard'] ? (
                              <Tick />
                           ) : (
                              <Dash />
                           )}
                        </div>
                     </td>
                  ))}
               </tr>
               <tr>
                  <td className='p-3 para5 font-medium'>
                     Weekly Business Report
                  </td>
                  {plansData.map((plan) => (
                     <td key={plan.name} className='p-3 para6'>
                        <div className='flex justify-center items-center'>
                           {plan.attribute['Weekly Business Report']?.access ? (
                              <Tick />
                           ) : (
                              <Dash />
                           )}
                        </div>
                     </td>
                  ))}
               </tr>
               <tr>
                  <td className='p-3 para5 font-medium'>Monthly Ad Spend</td>
                  {plansData.map((plan) => (
                     <td key={plan.name} className='p-3 para6 text-center'>
                        {plan.monthly_ad_spend}
                     </td>
                  ))}
               </tr>

               <tr>
                  <td className='p-3 para5 font-medium'>Agentic Fit</td>
                  {plansData.map((plan) => (
                     <td key={plan.name} className='p-3 para6 text-center'>
                        {plan.agentic_fit}
                     </td>
                  ))}
               </tr>

               {/* Support Section */}
               <tr>
                  <td>
                     <div className='p-3 font-bold para5 text-left'>
                        Support
                     </div>
                  </td>
                  {supportOptions.map((option, idx) => (
                     <td
                        key={idx}
                        className='p-3 font-semibold para6 text-center'
                     >
                        {option}
                     </td>
                  ))}
               </tr>
            </tbody>
         </table>
      </div>
   );
};

export default PlansTable;
