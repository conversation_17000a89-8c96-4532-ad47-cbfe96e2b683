import FivetranConnectorWrapper from './fivetran-connector-wrapper';
import image from '../images/integrations/amazon-ads-logo.svg';
import { connectToAmazonAdsSentiment } from '../utils';
import darkImage from '../../../assets/icons/kpi/aws-logo-scaled-removebg-preview.png';

import { useColorMode } from '@chakra-ui/react';

const AmazonAds = () => {
   const { colorMode } = useColorMode();

   return (
      <FivetranConnectorWrapper
         imageSrc={colorMode === 'dark' ? darkImage : image}
         heading='Amazon Ads'
         channelType='AMAZON_ADS'
         connectToSentimentFn={connectToAmazonAdsSentiment}
         modalData={{
            heading: 'Connect Amazon ads',
            content:
               'You are being redirected to Amazon ads to connect your account...',
         }}
      />
   );
};

export default AmazonAds;
