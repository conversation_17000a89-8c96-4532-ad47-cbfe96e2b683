import ShopifyImage from '../../../assets/icons/kpi/shopify.png';
import MetaImage from '../../../assets/icons/kpi/meta.png';
import WebImage from '../../../assets/icons/kpi/web.png';
import GAdImage from '../../../assets/icons/kpi/gads.png';
import ASPImage from '../../../assets/icons/kpi/amazon-seller.png';
import darkASPImage from '../../../assets/icons/kpi/aws-logo-scaled-removebg-preview.png';
import OverallImage from '../../../assets/icons/kpi/overall.png';
import OverallImageWhite from '../../../assets/icons/kpi/overallwhite.png'; // Add the white version of the image
import AAdsImage from '../../../assets/icons/kpi/amazon-ads.png';
import { CgWebsite } from 'react-icons/cg';
import { useColorModeValue } from '@chakra-ui/react';

const KPI_IMAGES: {
   [key: string]: { light: string; dark: string };
} = {
   facebookads: { light: MetaImage, dark: MetaImage },
   store: { light: ShopifyImage, dark: ShopifyImage },
   web: { light: WebImage, dark: WebImage },
   googleads: { light: GAdImage, dark: GAdImage },
   amazon_selling_partner: { light: ASPImage, dark: ASPImage },
   amazon_ads: { light: AAdsImage, dark: darkASPImage },
   overall_metrics: { light: OverallImage, dark: OverallImageWhite }, // Default to the light mode version
};

function KPIImage(props: {
   kpiCat: string;
   height?: string;
   width?: string;
   style?: React.CSSProperties;
}) {
   const { kpiCat, height, width, style } = props;
   const iconColor = useColorModeValue('gray.600', 'white');
   const isGAds = kpiCat === 'amazon_ads';

   // Define specific styles for AAdsImage in dark mode
   const customHeight = useColorModeValue(
      height || '25px',
      isGAds ? '35px' : height || '45px',
   ); // Increase height in dark mode
   const customWidth = width || '25px';

   const imageClass = isGAds
      ? useColorModeValue('', 'amazon-ads-dark-image') // Specific class for amazon_ads in dark mode
      : '';

   const imageSrc = useColorModeValue(
      KPI_IMAGES[kpiCat]?.light || '',
      KPI_IMAGES[kpiCat]?.dark || '',
   );

   if (imageSrc) {
      return (
         <img
            src={imageSrc}
            alt={kpiCat}
            className={imageClass}
            style={{
               width: customWidth,
               height: customHeight,
               color: iconColor,
               ...style, // Spread additional styles from props
            }}
         />
      );
   }
   return <CgWebsite color={iconColor} />;
}

export default KPIImage;
