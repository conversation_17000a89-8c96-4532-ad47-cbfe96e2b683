export interface Payload {
   user: string | null | undefined;
   client_id: string | null | undefined;
   mode: string;
   text: string;
   bearer_token: string | null | undefined;
   time_period: number | null;
   summary: boolean;
   metric: string | null;
   objective: string | null;
   dateFrequency: string | null;
   channel?: string | null;
   top_match?: string | null;
   sessionId?: string | null;
}
export interface Question {
   question: string;
   mode: string;
}
export interface QnABoxProps {
   history: HistoryItem[];
   onSearch: (
      sessionId: string,
      data: {
         id: string;
         userask: string;
         reply: {
            text: string;
            image: string | object;
            timeFrameChart?: string;
            selectedMode: string;
         };
      },
   ) => void;
   newChatTrigger: boolean;
   setShowHistoryBar: (show: boolean) => void | null;
   showHistoryBar: boolean;
   selectedSession: string | null;
   sessionId: string;
   deleteSession: string | null;
   onNewChat: () => void | null;
   marcoCustomSelect?: string;
   id?: string;
}
export interface Option {
   value: string;
   label: string;
   description: string;
   mode: string;
   icon: string;
   comingSoon: boolean;
   isActive?: boolean;
   inActiveMsg: string;
}
export interface HistoryItem {
   sessionId: string;
   summary: string;
   title: string;
   chat: ChatMessage[];
   createdAt: string;
   updatedAt: string;
}
export interface ChatMessage {
   id: string;
   userask: string;
   reply: {
      text: string;
      image: string | object;
      timeFrameChart?: string;
      selectedMode: string;
   };
   created_at?: string;
   updated_at?: string;
   feedback?: string;
}
export interface UserDetails {
   client_id: string;
   email: string;
}
export interface ChatAnswers {
   id: string;
   question: string;
   answer: { text: string | undefined; image: string | object };
   loading: boolean;
   thumbsUpClicked: boolean;
   thumbsDownClicked: boolean;
   showTimeFrameSelection: boolean;
   timeFram: number | null;
   createdAt: string;
   updatedAt: string;
   summary: boolean;
   timeFrameChart?: string | null;
   showMetricMenu: boolean;
   showObjectiveMenu: boolean;
   metric: string | null;
   objective: string | null;
   chartType?: string;
   showSummarize?: boolean;
   showChannel?: boolean;
   channel?: string | null;
   showTopMatch: boolean;
   topMatch?: string | null;
}
interface Field {
   name: string;
   type: string;
}

interface Schema {
   fields: Field[];
   primaryKey: string[];
   pandas_version: string;
}

export interface DataEntry {
   [key: string]: string | number;
}

export interface PerformanceChartData {
   schema: Schema;
   data: DataEntry[];
   Objective?: string;
   Timeframe: number;
   currency: string;
   text: string;
   dateFrequency?: string;
   compare?: boolean;
   table?: string | boolean;
   average_KPI_values?: DataEntry[];
}
export interface userFeedback {
   client_id: string;
   session_id: string;
   chat_id: string;
   user_feedback: string;
}
export interface ModeDescription {
   [mode: string]: {
      description: string;
   };
}
