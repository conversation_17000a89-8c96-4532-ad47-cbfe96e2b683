@use '../../sass/variable.scss';

.PillContentAccordian {
   border: 1px solid #d7d7d7;
   border-radius: 12px;
   min-height: 62px;
   display: flex;
   align-items: center;
   flex-direction: column;
   .top {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .generateBtn {
         color: #437eeb;
         border: 1px solid #437eeb;
         border-radius: 10px;
         height: 40px;
         width: 90px;
         cursor: pointer;
         display: flex;
         justify-content: center;
         align-items: center;
         margin: 10px;
      }

      .left {
         display: flex;
         align-items: center;
         .title {
            color: #242424;
            // [data-theme='dark'] & {
            //    color: $text_color;
            // }
         }
      }
   }
   .bottom-content {
      padding: 5px 7px;
      width: 100%;
      display: flex;
      justify-content: space-between;
      .post-link {
         color: #437eeb;
         padding-right: 15px;
      }

      a:hover {
         text-decoration: underline;
      }
   }
}
