import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
   KPIMeta,
   KPIRange,
   KPIStoreRange,
} from '../../pages/dashboard/utils/interface';
import { defineds } from '../../pages/dashboard/utils/default-ranges';
import { getPrevPeriod } from '../../pages/dashboard/utils/helpers';
import { format, startOfDay, endOfDay } from 'date-fns';

interface KPIState {
   dateRange: KPIStoreRange;
   prevRange: KPIStoreRange;
   groupBy: string;
   updatedPins: string[];
   kpiMeta: KPIMeta[];
}

const defaultStart = startOfDay(defineds.startOfLast7thDay);
const defaultEnd = endOfDay(defineds.endOfYesterday);

const defaultPrevRange: KPIRange = getPrevPeriod({
   start: defaultStart,
   end: defaultEnd,
});

export const initialKPIState: KPIState = {
   dateRange: {
      start: format(defaultStart, "yyyy-MM-dd'T'HH:mm:ss"),
      end: format(defaultEnd, "yyyy-MM-dd'T'HH:mm:ss"),
   },
   prevRange: {
      start: format(
         startOfDay(defaultPrevRange.start),
         "yyyy-MM-dd'T'HH:mm:ss",
      ),
      end: format(endOfDay(defaultPrevRange.end), "yyyy-MM-dd'T'HH:mm:ss"),
   },
   groupBy: 'day',
   updatedPins: [],
   kpiMeta: [],
};

const kpiSlice = createSlice({
   name: 'kpi',
   initialState: initialKPIState,
   reducers: {
      setdateRange: (state: KPIState, action: PayloadAction<KPIStoreRange>) => {
         state.dateRange = action.payload;
      },
      resetDateRange: (state: KPIState) => {
         state.dateRange = {
            start: format(
               startOfDay(defineds.startOfLast7thDay),
               "yyyy-MM-dd'T'HH:mm:ss",
            ),
            end: format(
               endOfDay(defineds.endOfYesterday),
               "yyyy-MM-dd'T'HH:mm:ss",
            ),
         };
         state.prevRange = {
            start: format(
               startOfDay(defaultPrevRange.start),
               "yyyy-MM-dd'T'HH:mm:ss",
            ),
            end: format(
               endOfDay(defaultPrevRange.end),
               "yyyy-MM-dd'T'HH:mm:ss",
            ),
         };
      },
      setprevRange: (state: KPIState, action: PayloadAction<KPIStoreRange>) => {
         state.prevRange = action.payload;
      },
      setGroupBy: (state: KPIState, action: PayloadAction<string>) => {
         state.groupBy = action.payload;
      },
      setUpdatedPins: (state: KPIState, action: PayloadAction<string>) => {
         state.updatedPins.push(action.payload);
      },
      setKpiMeta: (state: KPIState, action: PayloadAction<KPIMeta[]>) => {
         state.kpiMeta = action.payload;
      },
   },
});

export const {
   setdateRange,
   resetDateRange,
   setprevRange,
   setGroupBy,
   setUpdatedPins,
   setKpiMeta,
} = kpiSlice.actions;

export default kpiSlice.reducer;
