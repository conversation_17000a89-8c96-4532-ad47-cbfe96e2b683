import { ApexOptions } from 'apexcharts';
import Chart from 'react-apexcharts';
import {
   CampaignDaywiseKpiResponse,
   KpiwiseAggregate,
} from '../../../api/service/pulse';
import {
   Box,
   Flex,
   Skeleton,
   Spinner,
   Tooltip,
   useColorMode,
} from '@chakra-ui/react';
import { getUserDate } from '../../dashboard/utils/helpers';
import { useAppSelector } from '../../../store/store';
import { useState } from 'react';
import Dropdown from '../../../components/customDropdown/choose-dropdown';
import { cap, toUpperCase } from '../utils/helper';
import { format } from 'date-fns';
import { CampaignWithBudgetSpend } from '../../../api/service/pulse/performance-insights/meta-ads';

interface Props {
   chart_data:
      | CampaignDaywiseKpiResponse
      | KpiwiseAggregate
      | {
           [key: string]: CampaignWithBudgetSpend[];
        };
   selectedMetric: string;
   handleMetricChange: (newMetric: string) => void;
   loading?: boolean;
}

function MultiChart({
   chart_data,
   handleMetricChange,
   selectedMetric,
   loading,
}: Props) {
   if (!chart_data || !Object.keys(chart_data).length)
      return <Skeleton width='100%' height='500px' />;

   const { metricsOptions } = useAppSelector((state) => state.dropdown);

   const metrics = metricsOptions?.filter((x) =>
      Object.keys(chart_data)?.includes(x.value),
   );

   const [chartType, setchartype] = useState('column');

   const chartTypes = [
      { value: 'column', label: 'Bar' },
      { value: 'line', label: 'Line' },
   ];

   const state = {
      series: [
         {
            name: 'Budget',
            type: chartType,
            data: chart_data['spend']
               ?.slice(0, chart_data[selectedMetric]?.length)
               ?.map((x) => Number(x.budget).toFixed(2)),
         },
         {
            name: 'Spend',
            type: chartType,
            data: chart_data['spend']
               ?.slice(0, chart_data[selectedMetric]?.length)
               ?.map((x) => Number(x.kpi_value).toFixed(2)),
         },
         {
            name: metrics?.filter((x) => x.value == selectedMetric)?.[0]?.label,
            type: 'line',
            data: chart_data?.[selectedMetric]?.map((x) =>
               x.kpi_value ? Number(x.kpi_value).toFixed(2) : 0,
            ),
         },
      ],
      options: {
         chart: {
            type: 'line',
            background: useColorMode().colorMode === 'dark' ? '#1b202d' : '',
            toolbar: {
               show: true,
               offsetX: -30,
               offsetY: -10,
               tools: {
                  download: true,
                  selection: false,
                  zoom: false,
                  zoomin: false,
                  zoomout: false,
                  pan: false,
               },
            },
            zoom: {
               enabled: false,
            },
         },
         legend: {
            horizontalAlign: 'left',
            offsetX: 0,
            labels: {
               colors: useColorMode().colorMode === 'dark' ? '#fff' : '#000',
            },
         },
         labels: chart_data?.[selectedMetric]?.map((x) =>
            getUserDate(new Date(x.date_time.split('T')[0]), false),
         ),
         tooltip: {
            x: {
               formatter: function (
                  _val: number,
                  {
                     dataPointIndex,
                  }: { seriesIndex: number; dataPointIndex: number },
               ) {
                  return format(
                     new Date(
                        chart_data[selectedMetric][dataPointIndex].date_time,
                     ),
                     'E, dd MMM yyy',
                  );
               },
            },
         },

         xaxis: {
            labels: {
               style: {
                  colors: useColorMode().colorMode === 'dark' ? '#fff' : '#000',
               },
            },
         },
         yaxis: [
            {
               title: {
                  text: `Currency ${chart_data?.['spend']?.[0]?.currency || ''}`,
                  style: {
                     color:
                        useColorMode().colorMode === 'dark' ? '#fff' : '#000',
                  },
               },
               labels: {
                  style: {
                     colors:
                        useColorMode().colorMode === 'dark' ? '#fff' : '#000',
                  },
               },
               seriesName: 'Budget',
            },
            { show: false, title: 'Spend', seriesName: 'Budget' },
            {
               opposite: true,
               labels: {
                  style: {
                     colors:
                        useColorMode().colorMode === 'dark' ? '#fff' : '#000',
                  },
               },
               title: {
                  text: metrics?.filter((x) => x.value == selectedMetric)?.[0]
                     ?.label,
                  style: {
                     color:
                        useColorMode().colorMode === 'dark' ? '#fff' : '#000',
                  },
               },
               seriesName: metrics?.filter(
                  (x) => x.value == selectedMetric,
               )?.[0]?.label,
            },
         ],
      },
   };

   const handleMetric = (value: string) => {
      handleMetricChange(value);
   };

   const handleType = (value: string) => {
      setchartype(value);
   };

   return (
      <>
         <div className='spend-budget'>
            <Flex className='selects' justifyContent={'flex-end'} gap={2}>
               {metrics &&
                  (loading ? (
                     <Tooltip
                        label='Recommendations loading! Please wait.'
                        placement='top'
                        hasArrow
                     >
                        <Box
                           width='100px'
                           border='1px solid #e0e0e0'
                           borderRadius='5px'
                           display='flex'
                           justifyContent='center'
                           alignItems='center'
                        >
                           <Spinner size='sm' />
                        </Box>
                     </Tooltip>
                  ) : (
                     <Dropdown
                        options={metrics?.map((x) => {
                           const a = { value: x.value, label: cap(x.label) };
                           return a;
                        })}
                        onSelect={handleMetric}
                        initialValue={selectedMetric}
                        width={125}
                        loading={loading}
                     />
                  ))}
               {chartTypes && (
                  <Dropdown
                     options={chartTypes}
                     onSelect={handleType}
                     initialValue={chartType}
                     width={114}
                  />
               )}
            </Flex>
            {chart_data && chart_data[selectedMetric] && chart_data['spend'] ? (
               <Chart
                  options={state.options as ApexOptions}
                  series={state.series.map((series) => ({
                     ...series,
                     data: series.data.map((value) => Number(value)),
                  }))}
                  type='line'
                  width='100%'
                  height='100%'
               />
            ) : (
               <p
                  style={{
                     marginTop: '75px',
                     textAlign: 'center',
                  }}
                  className='vlaidation-text'
               >
                  {`There is no ${toUpperCase(chart_data[selectedMetric] ? 'spend' : selectedMetric)} data to show the Chart`}
               </p>
            )}
         </div>
      </>
   );
}

export default MultiChart;
