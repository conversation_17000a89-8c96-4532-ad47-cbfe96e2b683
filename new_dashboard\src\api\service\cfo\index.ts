import { AxiosResponse } from 'axios';
import dashboardApiAgent from '../../agent';
import {
   ShippingProfiles,
   WeightBased,
} from '../../../store/reducer/cfo-reducer';
import {
   FixedExpense,
   FixedExpenseData,
   VariableExpense,
   VariableExpenseData,
} from '../../../pages/settings/interface';

const endPoints: EndPoints = {
   getCogsData: async (clientId) => {
      const res = await dashboardApiAgent.get(`/cfo/cogs/${clientId}`);
      return res;
   },
   upsertCogs: async (payload) => {
      const res = await dashboardApiAgent.post('/cfo/cogs', payload);
      return res;
   },
   getPaymentMethod: async (clientId) => {
      const res = await dashboardApiAgent.get(
         `/cfo/payment-method/${clientId}`,
      );
      return res;
   },
   upsertPaymentMethod: async (payload) => {
      const res = await dashboardApiAgent.post('/cfo/payment-method', payload);
      return res;
   },
   getShippingCosts: async (clientId) => {
      const res = await dashboardApiAgent.get(`/cfo/shipping-cost/${clientId}`);
      return res;
   },
   getShippingCostByOrderId: async (clientId) => {
      const res = await dashboardApiAgent.get(
         `/cfo/costs-by-order-id/${clientId}`,
      );
      return res;
   },
   getShippingProfiles: async (clientId) => {
      const res = await dashboardApiAgent.get(
         `/cfo/shipping-profiles/${clientId}`,
      );
      return res;
   },
   getFixedExpenses: async (clientId) => {
      const res = await dashboardApiAgent.get(`/cfo/fixed-expense/${clientId}`);
      return res;
   },
   getVariableExpenses: async (clientId) => {
      const res = await dashboardApiAgent.get(
         `/cfo/variable-expense/${clientId}`,
      );
      return res;
   },
   upsertShippingCost: async (payload) => {
      const res = await dashboardApiAgent.post('/cfo/shipping-cost', payload);
      return res;
   },
   upsertFixedExpense: async (payload) => {
      const res = await dashboardApiAgent.post('/cfo/fixed-expense', payload);
      return res;
   },
   upsertVariableExpense: async (payload) => {
      const res = await dashboardApiAgent.post(
         '/cfo/variable-expense',
         payload,
      );
      return res;
   },
   upsertShippingCostByOrderId: async (payload) => {
      const res = await dashboardApiAgent.post(
         '/cfo/costs-by-order-id',
         payload,
      );
      return res;
   },
   upsertShippingProfile: async (payload) => {
      const res = await dashboardApiAgent.post(
         '/cfo/shipping-profile',
         payload,
      );
      return res;
   },
   deleteRecord: async (payload) => {
      const res = await dashboardApiAgent.put('/cfo/delete', payload);
      return res;
   },
};

interface EndPoints {
   getCogsData: (clientId: string) => Promise<AxiosResponse<COGSResult[]>>;
   upsertCogs: (payload: UpdateCOGSPayload) => Promise<AxiosResponse<string>>;
   getPaymentMethod: (
      clientId: string,
   ) => Promise<AxiosResponse<PaymentMethod[]>>;
   upsertPaymentMethod: (
      payload: PaymentMethod,
   ) => Promise<AxiosResponse<string>>;
   getShippingCosts: (
      clientId: string,
   ) => Promise<AxiosResponse<ShippingCost[]>>;
   getShippingCostByOrderId: (
      clientId: string,
   ) => Promise<AxiosResponse<ShippingCostByOrderIdData[]>>;
   getShippingProfiles: (
      clientId: string,
   ) => Promise<AxiosResponse<ShippingProfiles[]>>;
   getFixedExpenses: (
      clientId: string,
   ) => Promise<AxiosResponse<FixedExpenseData[]>>;
   getVariableExpenses: (
      clientId: string,
   ) => Promise<AxiosResponse<VariableExpenseData[]>>;
   upsertShippingCost: (
      payload: ShippingCost,
   ) => Promise<AxiosResponse<OutNewId[]>>;
   upsertShippingCostByOrderId: (
      payload: ShippingCostByOrderId[],
   ) => Promise<AxiosResponse<OutNewId[]>>;
   upsertShippingProfile: (
      payload: ShippingProfilePayload,
   ) => Promise<AxiosResponse<OutNewId[]>>;
   upsertFixedExpense: (
      payload: FixedExpense,
   ) => Promise<AxiosResponse<OutNewId[]>>;
   upsertVariableExpense: (
      payload: VariableExpense,
   ) => Promise<AxiosResponse<OutNewId[]>>;
   deleteRecord: (payload: DeleteRecord) => Promise<AxiosResponse<string>>;
}

interface ShippingProfilePayload {
   clientId: string;
   profileName: string;
   zones: BEZones[] | string;
   fixedRate: string;
   weightBased: WeightBased[];
}
export interface BEZones {
   name: string;
   continent: string;
}
export interface ShippingCostByOrderId {
   clientId?: string;
   Order_Id: number;
   Shipping_Cost: number;
}
export interface ShippingCostByOrderIdData {
   clientId: string;
   order_id: number;
   cost: number;
}
export interface DeleteRecord {
   clientId: string;
   id?: number;
   type: string;
   zone?: string;
}
export interface UpdateCOGSPayload {
   clientId: string;
   fixedPercent: string;
   id: number | null;
}
export interface ShippingCost {
   clientId: string;
   is_default: boolean | null;
   id: number | null;
}
export interface OutNewId {
   new_id: number;
}
export interface PaymentMethod {
   clientId: string;
   method: string;
   cost: string;
   fee: string;
   id: number | null;
}
export interface COGSResult {
   fixed_percent: string;
   id: number | null;
}

export default endPoints;
