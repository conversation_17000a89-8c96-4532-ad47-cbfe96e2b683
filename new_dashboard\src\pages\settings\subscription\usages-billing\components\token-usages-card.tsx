import {
   <PERSON>,
   <PERSON><PERSON><PERSON><PERSON>,
   <PERSON><PERSON><PERSON><PERSON>,
   <PERSON><PERSON><PERSON>nt,
   CardFooter,
   CardDescription,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import ApexCharts from 'react-apexcharts';
import { UserDetails } from '@/pages/socialwatch/interface';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { useApiQuery } from '@/hooks/react-query-hooks';
import subsEndPoints from '@/api/service/subscription';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Skeleton } from '@/components/ui/skeleton';
import { cap } from '@/pages/pulse/utils/helper';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/store';
import DefaultCard from '@/components/DefaultCard';
import { AgentUsageRecord } from '@/pages/settings/subscription/types';

// Helper function to get chart color based on usage percentage
const getChartColor = (percentage: number) => {
   if (percentage === 0) {
      return 'transparent'; // Transparent color
   } else if (percentage > 0 && percentage < 30) {
      return '#fbbf24'; // Light vibrant amber
   } else if (percentage >= 30 && percentage <= 75) {
      return '#f87171'; // Light vibrant red
   } else {
      return '#f87171'; // Light vibrant red
   }
};

// Helper function to get chart color with opacity for cards
const getChartColorWithOpacity = (percentage: number) => {
   if (percentage === 0) {
      return 'bg-transparent border-transparent'; // Transparent color
   } else if (percentage > 0 && percentage < 30) {
      return 'bg-emerald/20 border-emerald'; // Light vibrant green with opacity
   } else if (percentage >= 30 && percentage <= 75) {
      return 'bg-golden/20 border-golden'; // Light vibrant amber with opacity
   } else {
      return 'bg-ruby/20 border-ruby'; // Light vibrant red with opacity
   }
};

const TokenUsageCard = () => {
   const navigate = useNavigate();
   const { isActive } = useSelector((state: RootState) => state.subscription);
   const subscribed = isActive === true;

   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);

   const [series, setSeries] = useState<number[]>([]);
   const [labels, setLabels] = useState<string[]>([]);
   const [tokenUsages, setTokenUsages] = useState<AgentUsageRecord[] | null>(
      null,
   );

   const { data: agentUsages, isLoading } = useApiQuery({
      queryKey: ['getAgentUsages', userDetails?.client_id],
      queryFn: () => subsEndPoints.getAgentUsages(userDetails?.client_id),
      enabled: !!userDetails?.client_id,
      refetchOnWindowFocus: false,
      staleTime: Infinity,
   });

   useEffect(() => {
      if (!agentUsages?.agentUsages) return;

      const chartSeries: number[] = [];
      const chartLabels: string[] = [];

      agentUsages.agentUsages.forEach((agentUsage: AgentUsageRecord) => {
         const agentLabel = agentUsage.agent_name
            .replace(/_/g, ' ')
            .replace(/\b\w/g, (l) => l.toUpperCase());

         if (agentUsage.total_tokens === null) {
            // Infinite agent - exclude from chart
            return;
         }

         const total = Number(agentUsage.total_tokens);
         const used = Number(agentUsage.total_tokens_used);
         const percentage = total > 0 ? Math.round((used / total) * 100) : 0;

         chartSeries.push(percentage);
         chartLabels.push(agentLabel);
      });

      setSeries(chartSeries);
      setLabels(chartLabels);
      setTokenUsages(agentUsages.agentUsages);
   }, [agentUsages]);

   const chartOptions: ApexCharts.ApexOptions = {
      chart: {
         type: 'radialBar',
         height: 400,
         animations: {
            enabled: true,
            speed: 800,
         },
      },
      plotOptions: {
         radialBar: {
            offsetY: 0,
            startAngle: 0,
            endAngle: 360,
            hollow: {
               size: '60%',
               background: 'transparent',
            },
            track: {
               background: '#F3F4F6',
               strokeWidth: '130%',
            },
            dataLabels: {
               name: {
                  show: true,
                  fontSize: '14px',
                  color: '#000',
               },
               value: {
                  show: true,
                  fontSize: '16px',
               },
               total: {
                  show: true,
                  label: 'Total Tokens Used',
                  fontSize: '14px',
                  formatter: function () {
                     const totalUsed =
                        tokenUsages?.reduce((sum, agent) => {
                           return sum + Number(agent.total_tokens_used || 0);
                        }, 0) || 0;
                     return totalUsed.toString();
                  },
               },
            },
         },
      },
      labels: labels,
      colors: series.map((value) => {
         const percentage = value;
         return getChartColor(percentage);
      }),
      stroke: {
         lineCap: 'round',
         width: 3,
      },
   };

   if (isLoading) {
      return <Skeleton className='h-full w-full' />;
   }

   return (
      <Card className='w-full h-full rounded-xl shadow-md border !border-porcelain p-6 bg-white space-y-6'>
         <CardHeader>
            <CardTitle>Token Usage</CardTitle>
            <CardDescription className='text-muted-foreground'>
               Track token consumption per agent
            </CardDescription>
         </CardHeader>
         {!subscribed ? (
            <DefaultCard
               className='w-full'
               banner='inactivePlan'
               title='No Active Subscription'
               desc='Choose a plan that fits your needs and unlock full access to our platform.'
               actionLabel='Subscribe to a plan'
               navigate='/settings?mode=plansTopups&tab=plans'
            />
         ) : (
            <>
               <CardContent className='flex flex-col items-center space-y-4'>
                  {labels.length > 0 && series.length > 0 && (
                     <div className='rounded-lg overflow-hidden'>
                        <ApexCharts
                           options={chartOptions}
                           series={series}
                           type='radialBar'
                           height={500}
                           className='rounded-lg'
                        />
                     </div>
                  )}

                  <div className='space-y-2 w-full px-2'>
                     {tokenUsages &&
                        [...tokenUsages]
                           .sort((a, b) =>
                              a.agent_name.localeCompare(b.agent_name),
                           )
                           .map((item, index) => {
                              const percentage =
                                 item.total_tokens === null
                                    ? 0
                                    : Math.round(
                                         (Number(item.total_tokens_used) /
                                            Number(item.total_tokens)) *
                                            100,
                                      );
                              return (
                                 <div
                                    key={index}
                                    className={`flex justify-between text-sm text-gray-700 p-2.5 rounded-sm border ${getChartColorWithOpacity(percentage)}`}
                                 >
                                    <div className='flex items-center gap-2'>
                                       <span className='font-medium'>
                                          {cap(item.agent_name)}
                                       </span>
                                    </div>
                                    <span className='font-medium'>
                                       {item.total_tokens === null
                                          ? `${item.total_tokens_used} tokens used (Unlimited)`
                                          : `${percentage}% ( ${item.total_tokens_used} of ${item.total_tokens} )`}
                                    </span>
                                 </div>
                              );
                           })}
                  </div>
               </CardContent>

               <CardFooter className='flex flex-col gap-2 items-center'>
                  <Button
                     variant='outline'
                     className='text-blue-600 border-blue-600 hover:bg-blue-50'
                     onClick={() =>
                        navigate('/settings?mode=plansTopups&tab=topups')
                     }
                  >
                     Top Up Plan
                  </Button>
               </CardFooter>
            </>
         )}
      </Card>
   );
};

export default TokenUsageCard;
