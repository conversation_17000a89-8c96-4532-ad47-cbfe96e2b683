import { createSlice, PayloadAction } from '@reduxjs/toolkit';

import {
   MediaState,
   MultimediaData,
   UploadToSocialMedia,
} from '../../types/social-watch';
import { UserSocialDetails } from '../../api/service/onboarding';

const initialState: MediaState = {
   mediaData: [],
   status: 'idle',
   multiMedia: {},
   rawFiles: {},
   selectedTab: 'twitter',
   uploadToSocialMedia: {
      file: null,
      encodedUri: '',
      decodedUri: '',
   },
   marcoSidebarOpen: true,
   connectionDetails: [],
};

const mediaSlice = createSlice({
   name: 'media',
   initialState,
   reducers: {
      setSelectedTab: (state, action: PayloadAction<string>) => {
         state.selectedTab = action.payload;
      },
      setMultiMedia: (state, action: PayloadAction<MultimediaData>) => {
         const { socialMedia } = action.payload;

         if (!socialMedia) return;

         if (Object.keys(action.payload).length === 0) return;

         if (!state.multiMedia[socialMedia]) state.multiMedia[socialMedia] = [];
         state.multiMedia[socialMedia].push(action.payload);
      },
      removeMultiMedia: (
         state,
         action: PayloadAction<{ file: string; socialMedia: string }>,
      ) => {
         const { file, socialMedia } = action.payload;
         if (!state.multiMedia[socialMedia]) return;
         const deleteIdx = state.multiMedia[socialMedia].findIndex(
            (item) => item.rawFile === file,
         );

         if (deleteIdx !== -1)
            state.multiMedia[socialMedia].splice(deleteIdx, 1);
      },
      setRawFiles: (
         state,
         action: PayloadAction<{ result: string; socialMedia: string }>,
      ) => {
         const { socialMedia } = action.payload;
         if (!state.rawFiles[socialMedia]) state.rawFiles[socialMedia] = [];
         state.rawFiles[socialMedia].push(action.payload.result);
      },
      removeRawFile: (
         state,
         action: PayloadAction<{ file: string; socialMedia: string }>,
      ) => {
         const { file, socialMedia } = action.payload;
         if (!state.rawFiles[socialMedia]) return;
         const deleteIdx = state.rawFiles[socialMedia].findIndex(
            (item) => item === file,
         );

         if (deleteIdx != -1) {
            state.rawFiles[socialMedia].splice(deleteIdx, 1);
         }
      },

      resetRawFiles: (state) => {
         state.rawFiles = {};
      },
      resetMultiMedia: (state) => {
         state.multiMedia = {};
      },
      setUploadToSocialMedia: (
         state,
         action: PayloadAction<UploadToSocialMedia>,
      ) => {
         state.uploadToSocialMedia = action.payload;
      },
      toggleMarcoSidebar: (state, action: PayloadAction<boolean>) => {
         state.marcoSidebarOpen = action.payload;
      },
      setUserConnectionDetails: (
         state,
         action: PayloadAction<UserSocialDetails[]>,
      ) => {
         state.connectionDetails = action.payload;
      },
   },
});

export const {
   setMultiMedia,
   setRawFiles,
   removeRawFile,
   resetRawFiles,
   removeMultiMedia,
   resetMultiMedia,
   setSelectedTab,
   setUploadToSocialMedia,
   toggleMarcoSidebar,
   setUserConnectionDetails,
} = mediaSlice.actions;

export default mediaSlice.reducer;
