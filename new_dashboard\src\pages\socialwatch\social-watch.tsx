import ContentCreator from './content-creator';

import { useAppSelector } from '../../store/store';
import { Loading } from '../../components';
import './socialwatch.scss';
import IntegrateInfo from '../../components/integrate-info/integrate-info';
import { integrationInfoStrings } from '../../utils/strings/integrate-info-strings';

function ShowMessage() {
   return (
      <IntegrateInfo
         feature={integrationInfoStrings.socialCreative.title}
         text={integrationInfoStrings.socialCreative.description}
      />
   );
}

function SocialWatch() {
   const { connections, isFetching } = useAppSelector(
      (state) => state.integration,
   );

   const hasIntegrated = connections.twitter || connections.linkedin;

   if (isFetching) return <Loading />;
   return hasIntegrated ? <ContentCreator /> : <ShowMessage />;
}

export default SocialWatch;
