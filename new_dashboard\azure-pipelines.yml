trigger:
   branches:
      include:
         - develop

resources:
   repositories:
      - repository: flable_configurations
        ref: flable_configurations
        type: git
        name: flable_configurations

parameters:
   - name: env
     displayName: Deployment Environment
     type: string
     default: develop
     values:
        - develop
        - master
        - master_de

variables:
   - name: dockerfilePath
     value: '$(Build.ArtifactStagingDirectory)/Dockerfile'
   - template: ./vars.yml
     parameters:
        env: ${{ parameters.env }}
   - name: vmImageName
     value: 'ubuntu-latest'
   # - name: portmapping
   #   value: '-p 3000:3000'
   # - name: volumemount
   #   value: ''

stages:
   - stage: Build
     displayName: Build and push stage
     jobs:
        - job: Build
          displayName: Build
          pool:
             vmImage: $(vmImageName)
          steps:
             - checkout: self
             - checkout: flable_configurations

             - task: CopyFiles@2
               displayName: 'Copying Application files'
               inputs:
                  SourceFolder: '$(Agent.BuildDirectory)/s/$(Build.Repository.Name)'
                  Contents: '**'
                  TargetFolder: '$(Build.ArtifactStagingDirectory)/'

             - task: CopyFiles@2
               displayName: 'Copying Configurations and environment'
               inputs:
                  SourceFolder: '$(Agent.BuildDirectory)/s/flable_configurations/$(Build.Repository.Name)/${{ parameters.env }}/'
                  Contents: '**'
                  TargetFolder: '$(Build.ArtifactStagingDirectory)/config/env/'
                  OverWrite: true

             - task: CopyFiles@2
               displayName: 'Copying Certificates'
               inputs:
                  SourceFolder: '$(Agent.BuildDirectory)/s/flable_configurations/certs/'
                  Contents: '*'
                  TargetFolder: '$(Build.ArtifactStagingDirectory)/certs/'
                  OverWrite: true

             - script: |
                  echo "Listing files in $(Build.ArtifactStagingDirectory):"
                  ls -ltar $(Build.ArtifactStagingDirectory)/
                  echo "Listing files in $(Build.ArtifactStagingDirectory)/certs/:"
                  ls -ltar $(Build.ArtifactStagingDirectory)/certs/
               displayName: 'List files in Artifact Staging Directory'

             - task: Docker@2
               displayName: Build and push an image to container registry
               continueOnError: true
               env:
                  DOCKER_BUILDKIT: 1
               inputs:
                  command: buildAndPush
                  repository: ${{ variables.imagerepository }}
                  dockerfile: $(dockerfilePath)
                  containerRegistry: ${{ variables.dockerRegistryServiceConnection }}
                  tags: |
                     latest
                     $(Build.BuildId)

   - stage: Deploy
     displayName: 'Deploy'
     jobs:
        - deployment: 'applyinfra'
          environment: $(environmentDeploymentGate)
          displayName: 'Deploy to App'
          strategy:
             runOnce:
                deploy:
                   steps:
                      - task: AzureContainerApps@1
                        displayName: 'Azure Container Apps Deploy'
                        inputs:
                           azureSubscription: ${{ variables.serviceConnection }}
                           acrName: ${{ variables.containerRegistryName }}
                           imageToDeploy: '${{ variables.containerRegistry }}/$(Build.Repository.Name):$(Build.BuildId)'
                           containerAppName: ${{ variables.containerAppName }}
                           resourceGroup: ${{ variables.resourceGroup }}
                           containerAppEnvironment: ${{ variables.containerAppEnvironment }}
                           disableTelemetry: false

   # - stage: Deploy
   #   displayName: 'Deploy'
   #   jobs:
   #      - deployment: 'applyinfra'
   #        environment: $(environmentDeploymentGate)
   #        displayName: 'Deploy to App'
   #        strategy:
   #           runOnce:
   #              deploy:
   #                 steps:
   #                    - task: SSH@0
   #                      displayName: 'SSH to Private Server'
   #                      inputs:
   #                         sshEndpoint: ${{ variables.sshServiceConnection }}
   #                         runOptions: 'inline'
   #                         inline: |
   #                            containerName=$(echo $(Build.Repository.Name) | tr '[:upper:]' '[:lower:]')-$(echo ${{ parameters.env }})
   #                            echo "Container Name: $containerName"

   #                            containerRegistry="${{ variables.containerRegistry }}"
   #                            echo "Container Registry: $containerRegistry"

   #                            repositoryName=$(echo $(Build.Repository.Name))
   #                            repositoryNameLower=$(echo "$repositoryName" | tr '[:upper:]' '[:lower:]')
   #                            echo "Repository Name: $repositoryNameLower"

   #                            # Check if the container exists and remove it if it does
   #                            if [ $(docker ps -a | grep $containerName | wc -l) -gt 0 ]; then
   #                                docker rm -f $containerName
   #                                echo "The Previous Container $containerName deleted"
   #                            else
   #                                echo "No previous Container exists"
   #                            fi

   #                            # Check if images exist and remove them if they do
   #                            imageList=$(docker images | grep -w "${containerRegistry}/${repositoryNameLower}" | awk '{print $1":"$2}')
   #                            if [ -n "$imageList" ]; then
   #                                for i in $imageList; do
   #                                    docker rmi -f $i
   #                                    echo "The Previous Image with Build Number ${i} deleted"
   #                                done
   #                            else
   #                                echo "No previous Images exist"
   #                            fi

   #                            # Pull the new image
   #                            imageName="${containerRegistry}/${repositoryNameLower}:$(Build.BuildId)"
   #                            echo "Pulling image: $imageName"
   #                            docker pull $imageName

   #                            if [ $? -ne 0 ]; then
   #                                echo "Error: Failed to pull Docker image $imageName"
   #                                exit 1
   #                            fi

   #                            # Run the new container
   #                            docker run -d -e LOGLEVEL=DEBUG $(portmapping) $(volumemount) --name $containerName $imageName
