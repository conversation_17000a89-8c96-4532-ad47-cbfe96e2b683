import { ChunkData } from '@/api/service/agentic-workflow/analytics-agent';
import { cap } from '@/pages/pulse/utils/helper';
import {
   Accordion,
   AccordionItem,
   AccordionContent,
   AccordionTrigger,
} from '@/components/ui/accordion';
import { convertToHTMLTable, formatSqlForHtml } from './helpers';
import {
   PiCheckSquareFill,
   PiLaptopFill,
   PiRepeatBold,
   PiTagFill,
} from 'react-icons/pi';
import { useRef } from 'react';

interface ChunkRendererProps {
   chunk: ChunkData;
}

type ParsedQueryResult = Record<string, unknown> | string;
type ParsedPlanJSONStr = Record<string, unknown> | string;

interface HasQueryResult {
   query_result: string;
}

interface HasPlanJSONStr {
   plan_json_str: string;
}

const hasQueryResult = (obj: unknown): obj is HasQueryResult => {
   return (
      obj !== null &&
      typeof obj === 'object' &&
      'query_result' in obj &&
      typeof (obj as Record<string, unknown>).query_result === 'string'
   );
};

const hasPlanJSONStr = (obj: unknown): obj is HasPlanJSONStr => {
   return (
      obj !== null &&
      typeof obj === 'object' &&
      'plan_json_str' in obj &&
      typeof (obj as Record<string, unknown>).plan_json_str === 'string'
   );
};

interface RenderObjectProps {
   obj: Record<string, unknown>;
}
const RenderObject = ({ obj }: RenderObjectProps) => {
   return (
      <div className='pl-2'>
         {Object.entries(obj).map(([key, value]) => (
            <div key={key} className='my-1'>
               <span className='text-purple-700 font-medium'>{key}:</span>{' '}
               {typeof value === 'object' && value !== null ? (
                  <div className='pl-4 border-l-2 border-gray-300 ml-2 mt-1'>
                     <RenderObject obj={value as Record<string, unknown>} />
                  </div>
               ) : (
                  <span className='text-gray-800'>{String(value)}</span>
               )}
            </div>
         ))}
      </div>
   );
};

const ChunkRenderer = (props: ChunkRendererProps) => {
   const { content, metadata, agent_name } = props.chunk;

   switch (props.chunk.type) {
      case 'tool_call':
         return (
            <>
               <div className='flex flex-col w-full gap-2 bg-white p-[10px] mb-[20px]'>
                  <h3 className='text-[14px] md:text-[16px] font-bold ml-[-5px] mt-[-3px]'>
                     {`${agent_name}`}:
                  </h3>
                  <div className='section flex items-center gap-2'>
                     <PiTagFill />
                     <span>
                        {cap(typeof content === 'string' ? content : '')}
                     </span>
                  </div>
               </div>
            </>
         );

      case 'tool_input': {
         let parsedQueryResult: ParsedQueryResult = '';
         let parsedPlanJSONStr: ParsedPlanJSONStr = '';

         if (hasQueryResult(content)) {
            try {
               const parsed = JSON.parse(
                  content.query_result,
               ) as ParsedQueryResult;
               parsedQueryResult = parsed;
            } catch {
               parsedQueryResult = content.query_result;
            }
         }

         if (hasPlanJSONStr(content)) {
            try {
               const parsed = JSON.parse(
                  content.plan_json_str,
               ) as ParsedPlanJSONStr;
               parsedPlanJSONStr = parsed;
            } catch {
               parsedPlanJSONStr = content.plan_json_str;
            }
         }

         const viewSQLRef = useRef<HTMLDivElement>(null);

         const handleAccordionToggle = () => {
            setTimeout(() => {
               viewSQLRef.current?.scrollIntoView({
                  behavior: 'smooth',
                  block: 'start',
               });
            }, 300);
         };

         return (
            <>
               <div className='flex flex-col w-full gap-2 bg-white p-[10px] mb-[20px]'>
                  <h3 className='text-[14px] md:text-[16px] font-bold ml-[-5px] mt-[-3px]'>
                     {`${agent_name}`}:
                  </h3>

                  {Object.keys(content).includes('sql') ? (
                     <div className='section'>⚙️ Setting up data filters</div>
                  ) : (
                     <></>
                  )}

                  {typeof content === 'string' && <div>{content}</div>}

                  {typeof content === 'object' &&
                     Object.keys(content).length > 0 &&
                     Object.keys(content).map((key, index) => (
                        <div key={`${key}-${index}`} className='section w-full'>
                           {content?.[key] && (
                              <>
                                 {key.toLowerCase() === 'sql' ? (
                                    <strong className='flex items-center gap-2'>
                                       <PiLaptopFill /> Running a secure
                                       database query
                                    </strong>
                                 ) : key.toLowerCase() === 'query_result' &&
                                   typeof parsedQueryResult === 'string' ? (
                                    <strong>Query Result:</strong>
                                 ) : key.toLowerCase() === 'context' ? (
                                    <strong>Context: </strong>
                                 ) : (
                                    <></>
                                 )}
                                 <span>
                                    {key.toLowerCase() === 'query_result' &&
                                    typeof parsedQueryResult === 'string' ? (
                                       <div
                                          className='w-full overflow-x-auto'
                                          dangerouslySetInnerHTML={{
                                             __html:
                                                convertToHTMLTable(
                                                   parsedQueryResult,
                                                ),
                                          }}
                                       />
                                    ) : key.toLowerCase() === 'query_result' &&
                                      typeof parsedQueryResult === 'object' ? (
                                       <>
                                          {typeof parsedQueryResult ===
                                             'object' &&
                                             parsedQueryResult !== null &&
                                             !Array.isArray(
                                                parsedQueryResult,
                                             ) &&
                                             Object.entries(
                                                parsedQueryResult,
                                             ).map(([subKey, value]) => (
                                                <>
                                                   <div
                                                      key={subKey}
                                                      className='w-full flex-column justify-between items-center gap-2'
                                                   >
                                                      <strong>
                                                         {subKey
                                                            .slice(0, 1)
                                                            .toUpperCase() +
                                                            subKey.slice(1)}
                                                         :{' '}
                                                      </strong>
                                                      <span className='max-w-[60%]'>
                                                         <code className='block  bg-gray-200 p-2 rounded-md text-gray-800'>
                                                            {typeof value ===
                                                               'object' &&
                                                            value !== null ? (
                                                               <RenderObject
                                                                  obj={
                                                                     value as Record<
                                                                        string,
                                                                        unknown
                                                                     >
                                                                  }
                                                               />
                                                            ) : (
                                                               String(value)
                                                            )}
                                                         </code>
                                                      </span>
                                                   </div>
                                                   <br />
                                                </>
                                             ))}
                                       </>
                                    ) : key.toLowerCase() === 'sql' ? (
                                       <>
                                          <Accordion type='single' collapsible>
                                             <AccordionItem value='item-1'>
                                                <AccordionTrigger className='text-gray-500 text-[16px] justify-start gap-1 hover:cursor-pointer [&[data-state=open]>svg]:rotate-90 hover:no-underline'>
                                                   <div className='text-[12px] md:text-[16px]'>
                                                      Here's the SQL we used.{' '}
                                                      <span
                                                         className='text-blue-500 hover:underline cursor-pointer'
                                                         onClick={
                                                            handleAccordionToggle
                                                         }
                                                         ref={viewSQLRef}
                                                      >
                                                         View SQL
                                                      </span>
                                                   </div>
                                                </AccordionTrigger>
                                                <AccordionContent className='pl-[10px]'>
                                                   <div
                                                      className='w-full overflow-x-auto p-2 bg-gray-200 rounded-md m-2'
                                                      dangerouslySetInnerHTML={{
                                                         __html:
                                                            formatSqlForHtml(
                                                               content?.[key],
                                                            ),
                                                      }}
                                                   />
                                                </AccordionContent>
                                             </AccordionItem>
                                          </Accordion>
                                       </>
                                    ) : key.toLowerCase() === 'kwargs' ? (
                                       <></>
                                    ) : key.toLowerCase() === 'plan_json_str' &&
                                      typeof parsedPlanJSONStr === 'object' ? (
                                       <>
                                          {typeof parsedPlanJSONStr ===
                                             'object' &&
                                             parsedPlanJSONStr !== null &&
                                             !Array.isArray(
                                                parsedPlanJSONStr,
                                             ) &&
                                             Object.entries(
                                                parsedPlanJSONStr,
                                             ).map(([subKey, value]) => (
                                                <>
                                                   <div
                                                      key={subKey}
                                                      className='w-full flex-column justify-between items-center gap-2'
                                                   >
                                                      <strong>
                                                         {subKey
                                                            .slice(0, 1)
                                                            .toUpperCase() +
                                                            subKey.slice(1)}
                                                         :{' '}
                                                      </strong>
                                                      <span className='max-w-[60%]'>
                                                         <code className='block  bg-gray-200 p-2 rounded-md text-gray-800'>
                                                            {typeof value ===
                                                               'object' &&
                                                            value !== null ? (
                                                               <RenderObject
                                                                  obj={
                                                                     value as Record<
                                                                        string,
                                                                        unknown
                                                                     >
                                                                  }
                                                               />
                                                            ) : (
                                                               String(value)
                                                            )}
                                                         </code>
                                                      </span>
                                                   </div>
                                                   <br />
                                                </>
                                             ))}
                                       </>
                                    ) : (
                                       typeof content?.[key] === 'string' && (
                                          <span>{content?.[key]}</span>
                                       )
                                    )}
                                 </span>
                              </>
                           )}
                        </div>
                     ))}
               </div>
            </>
         );
      }

      case 'transition':
         return (
            <>
               <div className='flex flex-col w-full gap-2 bg-white p-[10px] mb-[20px]'>
                  <h3 className='text-[14px] md:text-[16px] font-bold ml-[-5px] mt-[-3px]'>
                     {`${typeof metadata?.previous_agent === 'string' ? metadata?.previous_agent : ''}`}
                     :
                  </h3>
                  <div className='section flex items-center gap-2'>
                     <PiCheckSquareFill />
                     <span>Final Input Sent</span>
                  </div>
                  <div className='flex items-center gap-2'>
                     <PiRepeatBold />
                     Handing it over to {`${agent_name}`}
                  </div>
               </div>
            </>
         );

      case 'thought':
         return;

      case 'final_result':
         return;
   }
};

export default ChunkRenderer;
