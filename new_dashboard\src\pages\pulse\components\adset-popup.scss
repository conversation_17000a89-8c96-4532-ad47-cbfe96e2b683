@use '../../../sass/variable.scss';
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500&display=swap');

:root {
   // Light theme variables
   --popup-bg: #ffffff;
   --text-primary: #333333;
   --border-color: #e0e0e0;
   --table-header-bg: #f5f5f5;
   --table-hover: #f8f8f8;
}

[data-theme='dark'] {
   // Dark theme variables
   --popup-bg: #1a202c;
   --text-primary: #ffffff;
   --border-color: #404040;
   --table-header-bg: #2d2d2d;
   --table-hover: #2a2a2a;
}

.adset-popup-overlay {
   position: fixed;
   top: 0;
   left: 0;
   width: 100%;
   height: 100%;
   background: rgba(0, 0, 0, 0.5);
   display: flex;
   justify-content: center;
   align-items: center;
   z-index: 1000;

   // [data-theme='dark'] & {
   //    background-color: $background;
   // }
   .adset-popup-content {
      background-color: var(--popup-bg);
      color: var(--text-primary);
      padding: 20px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
      max-height: 90%;
      width: 80%;
      overflow-y: auto;
      box-sizing: border-box;
      padding-top: 0px;
      scrollbar-width: thin;
      scrollbar-color: #ccc transparent;

      &::-webkit-scrollbar {
         width: 6px;
      }

      &::-webkit-scrollbar-thumb {
         background-color: #ccc;
         border-radius: 10px;
         border: 2px solid #e0e0e0;
      }

      &::-webkit-scrollbar-thumb:hover {
         background-color: #888;
      }

      &::-webkit-scrollbar-track {
         background: transparent;
      }

      &::-webkit-scrollbar-thumb:window-inactive {
         visibility: hidden;
      }

      .adset-heading {
         position: fixed;
         display: flex;
         justify-content: space-between;
         align-items: center;
         margin-bottom: 20px;
         z-index: 1000;
         background-color: #ffffff;
         width: 77%;
         padding-top: 10px;

         h3 {
            font-weight: 600;
            font-size: 20px;
            font-family: 'Poppins';
            display: flex;
            align-items: center;

            button {
               font-weight: 600;
               font-size: 20px;
               font-family: 'Poppins';
               margin-right: 10px;
               background: none;
               border: none;
               cursor: pointer;
            }
         }

         .close-button {
            cursor: pointer;
            font-weight: 600;
            font-size: 25px;
            background: none;
            border: none;
         }
      }

      .adset-divider {
         height: 1px;
         background-color: #ccc;
         margin-top: 60px;
      }

      .adset-top {
         display: flex;
         gap: 5px;
         margin-bottom: 20px;
         margin-top: 20px;

         button {
            padding: 6px 10px;
            border: none;
            border-radius: 5px;
            background-color: #e3e2ff;
            color: blue;
            cursor: pointer;
            font-size: 12px;
         }
      }

      .adset-details {
         margin-bottom: 20px;

         h3 {
            font-weight: 600;
            font-size: 18px;
            font-family: 'Poppins';
            margin-bottom: 10px;
         }

         .campaign-insights-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
         }

         .insight-icon {
            width: 20px;
            height: 20px;
            margin-right: 8px;
            margin-bottom: 10px;
         }

         .headers {
            background-color: #ffffff;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;

            h4 {
               font-weight: 500;
               font-size: 16px;
               font-family: 'Poppins';
               margin-bottom: 5px;
            }

            p {
               font-size: 14px;
               margin-top: 5px;
            }

            .line {
               height: 1px;
               background-color: #ccc;
               margin: 10px 0;
            }
         }
      }

      .adset-overview,
      .performance-chart {
         margin-bottom: 40px;
      }

      .adset-overview,
      .ad-set {
         margin-bottom: 20px;

         h3 {
            font-weight: 600;
            font-size: 18px;
            font-family: 'Poppins';
            margin-bottom: 10px;
         }

         .insights,
         .adset-overview {
            .insights-table,
            .adset-table {
               width: 100%;
               border-collapse: collapse;

               th,
               td {
                  padding: 8px;
                  border-bottom: 1px solid #ddd;
                  text-align: left;
                  font-family: 'Poppins';
               }

               th {
                  font-weight: 500;
                  font-size: 16px;
               }

               td {
                  font-weight: 300;
                  font-size: 14px;

                  span {
                     font-size: 12px;
                     margin-left: 5px;
                     font-weight: bold;
                  }
               }

               .ads {
                  font-weight: 500;
                  font-size: 16px;
               }

               .image_link {
                  width: 100px;
                  height: 100px;
                  object-fit: cover;
                  padding-left: 10px;

                  .icon {
                     width: 30px;
                     height: 30px;
                     object-fit: cover;
                  }

                  .image {
                     width: 60px;
                     height: 60px;
                     object-fit: cover;
                  }
               }

               .links {
                  text-decoration: underline;
                  color: blue;
                  font-size: 16px;
                  font-family: 'Poppins';
               }
            }
         }
      }
   }
}

.adset-summary-table {
   width: 100%;
   border-collapse: collapse;
   margin-bottom: 20px;
   font-size: 14px;
   text-align: center;
   table-layout: fixed;

   th,
   td {
      border: 1px solid var(--border-color);
      padding: 8px;
   }

   th {
      background-color: var(--table-header-bg);
      font-weight: bold;
   }

   tr:hover {
      background-color: var(--table-hover);
   }
}

.recommendation {
   font-weight: bold;
}
