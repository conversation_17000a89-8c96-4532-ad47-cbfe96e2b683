import { Keys, LocalStorageService } from '@/utils/local-storage';
import { Separator } from '../ui/separator';
import { SidebarTrigger, useSidebar } from '../ui/sidebar';
import FlableIcon from '@/assets/image/flableicon.png';
import { AuthUser } from '@/types/auth';
import ProfilePopup from '../profile/profile';
import NotificationsPopover from '../notifications-popover/notifications-popover';

const AppHeader = () => {
   const { open } = useSidebar();

   const userDetails = LocalStorageService.getItem(
      Keys.FlableUserDetails,
   ) as AuthUser;

   return (
      <header className='flex w-full shrink-0 items-center gap-2 h-[52px] shadow-md'>
         <div
            className={`flex w-full ${open ? 'justify-end' : 'justify-between'} items-center`}
         >
            {!open && (
               <div className='flex items-center gap-2'>
                  <SidebarTrigger className='ml-2 hover:cursor-pointer' />
                  <Separator
                     orientation='vertical'
                     className='mr-2 data-[orientation=vertical]:h-4'
                  />
                  <img
                     src={FlableIcon}
                     className='w-[100px] md:w-[130px]'
                     alt='flable icon'
                  />
               </div>
            )}
            <div className='flex items-center gap-2 mr-2'>
               <NotificationsPopover />
               <p className='text-[12px] md:text-[16px] text-black font-semibold'>
                  {`${userDetails?.fullName} ${userDetails?.company_name ? `(${userDetails?.company_name})` : ''}`}
               </p>
               <ProfilePopup />
            </div>
         </div>
      </header>
   );
};

export default AppHeader;
