import { useState } from 'react';
import {
   Box,
   Image,
   useToast,
   HStack,
   PinInput,
   PinInputField,
   Text,
   Button,
   Highlight,
} from '@chakra-ui/react';
import { Navigate, useNavigate } from 'react-router-dom';
import { useApiMutation } from '../../hooks/react-query-hooks';
import { appStrings } from '../../utils/strings/app-strings';
import { LocalStorageService, Keys } from '../../utils/local-storage';

import ICON from '../../assets/icons/icon.png';
import keys from '../../utils/strings/query-keys';
import authEndpoints from '../../api/service/auth';
import { useAppDispatch } from '../../store/store';
import { setRegisterProgress } from '../../store/reducer/onboarding-reducer';

function EmailVerification() {
   const [otp, setOtp] = useState('');

   const email_address = LocalStorageService.getItem(Keys.UserName) as string;

   const toast = useToast();
   const navigate = useNavigate();
   const dispatch = useAppDispatch();

   const { mutate, isPending } = useApiMutation({
      queryKey: [keys.verifyEmail],
      mutationFn: authEndpoints.verifyEmail,
      onSuccessHandler: (response) => {
         LocalStorageService.setItem(Keys.UserName, response.email_address);
         dispatch(setRegisterProgress(response.register_progress.trim()));
         navigate('/onboarding');

         toast({
            title: 'Registration successful',
            description: 'Email verified successfully.',
            status: 'success',
            duration: 5000,
            isClosable: true,
         });
      },
      onError: (message) => {
         if (message === 'OTP expired. Please login again.') {
            navigate('/auth/login');
         }

         toast({
            title: 'Failed',
            description: message,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
   });

   const handleChange = (value: string) => {
      setOtp(value);
   };

   const handleSubmit = () => {
      mutate({ email_address, email_otp: otp, action: 'email-verification' });
   };

   if (!email_address) {
      return <Navigate replace to='/auth/login' />;
   }

   return (
      <>
         <Box
            display='flex'
            justifyContent='center'
            alignItems='center'
            mt={10}
            mb={10}
         >
            <Image src={ICON} alt='Flable Icon' w={'15%'} />
            <Box ml={2} fontSize='xl' fontWeight='bold'>
               {appStrings.companyName}
            </Box>
         </Box>
         <Box>
            <Text as='h2' textAlign='center' fontWeight='bold' fontSize='28px'>
               OTP Verification
            </Text>
            <Text as='p' textAlign='center' mt={7} fontSize='15px'>
               One Time Password (OTP) has been sent via Email to{' '}
               <Highlight query={email_address} styles={{ fontWeight: 'bold' }}>
                  {email_address}
               </Highlight>
               .
            </Text>
            <Text as='p' textAlign='center' mt={7}>
               Enter the OTP below to verify it.
            </Text>
            <Box mt={3}>
               <HStack>
                  <PinInput
                     value={otp}
                     defaultValue={otp}
                     size='lg'
                     onChange={handleChange}
                  >
                     <PinInputField />
                     <PinInputField />
                     <PinInputField />
                     <PinInputField />
                     <PinInputField />
                     <PinInputField />
                  </PinInput>
               </HStack>
            </Box>
            <Button
               width='full'
               mt={3}
               colorScheme='blackAlpha'
               onClick={handleSubmit}
               size='sm'
               disabled={otp.length !== 6}
               isLoading={isPending}
            >
               Verify OTP
            </Button>
         </Box>
      </>
   );
}

export default EmailVerification;
