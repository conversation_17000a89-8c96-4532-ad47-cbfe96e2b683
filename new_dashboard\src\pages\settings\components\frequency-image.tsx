import Daily from '../../../assets/icons/email-report/daily.svg';
import Weekly from '../../../assets/icons/email-report/weekly.svg';
import Monthly from '../../../assets/icons/email-report/monthly.svg';
import Yearly from '../../../assets/icons/email-report/yearly.svg';
import CalenderBlank from '../../../assets/icons/email-report/CalendarBlank.svg';
import HourglassLow from '../../../assets/icons/email-report/HourglassLow.svg';
import Clock from '../../../assets/icons/email-report/Clock.svg';
import Flag from '../../../assets/icons/email-report/Flag.svg';
import Infinity from '../../../assets/icons/email-report/Infinity.svg';

const frequencyImageMap: {
   [key: string]: string;
} = {
   day: Daily,
   week: Weekly,
   month: Monthly,
   year: Yearly,
   calendar: CalenderBlank,
   hourglass: HourglassLow,
   clock: Clock,
   flag: Flag,
   infinity: Infinity,
};

function FrequencyImage(props: { frequency: string }) {
   const { frequency } = props;
   return frequencyImageMap[frequency] ? (
      <img
         src={frequencyImageMap[frequency]}
         alt='Logo'
         width={22}
         height={22}
      />
   ) : null;
}

export default FrequencyImage;
