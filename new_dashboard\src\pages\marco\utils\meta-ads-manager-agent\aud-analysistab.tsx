import React from 'react';
import {
   Box,
   Text,
   Flex,
   Thead,
   Tr,
   Th,
   Tbody,
   Td,
   Table,
   Stack,
} from '@chakra-ui/react';
import { ChevronDownIcon } from '@chakra-ui/icons';
import { Collapse } from '@chakra-ui/react';
import { ResponseTargetingAnalysis } from '../../../../api/service/agentic-workflow/meta-ads-manager';
type TableRow = Record<string, string>;
interface DynamicCollapsibleTableListProps {
   data: ResponseTargetingAnalysis;
}

const formatLabel = (label: string) => {
   return label.replace(/_/g, ' ').replace(/\b\w/g, (c) => c.toUpperCase());
};
export const DynamicCollapsibleTableList: React.FC<
   DynamicCollapsibleTableListProps
> = ({ data }) => {
   const [openStates, setOpenStates] = React.useState<Record<string, boolean>>(
      {},
   );

   const toggleSection = (key: string) => {
      setOpenStates((prev) => ({
         ...prev,
         [key]: !prev[key],
      }));
   };

   return (
      <Stack spacing={4} width='100%'>
         {Object.entries(data).map(([key, value]) => {
            if (!Array.isArray(value) || value.length === 0) return null;

            const typedValue = value as Record<string, string>[];
            const headers = Object.keys(typedValue[0]);
            const displayTitle = key
               .replace(/_/g, ' ')
               .replace(/\b\w/g, (c) => c.toUpperCase());

            return (
               <Box key={key}>
                  <Flex
                     onClick={() => toggleSection(key)}
                     align='center'
                     justify='space-between'
                     p={2}
                     cursor='pointer'
                     borderBottom='1px solid'
                     bg='gray.100'
                     borderColor='gray.200'
                     _hover={{ bg: 'gray.50' }}
                  >
                     <Text fontSize='sm' fontWeight='500'>
                        {displayTitle}
                     </Text>
                     <ChevronDownIcon
                        transform={openStates[key] ? 'rotate(180deg)' : 'none'}
                        transition='transform 0.2s'
                        w={4}
                        h={4}
                     />
                  </Flex>

                  <Collapse in={openStates[key]}>
                     <Box pt={4} bg='gray.50' borderRadius='md' p={4}>
                        <Table size='sm' variant='simple'>
                           <Thead>
                              <Tr>
                                 {headers.map((header) => (
                                    <Th key={header}>
                                       {header.replace(/_/g, ' ')}
                                    </Th>
                                 ))}
                              </Tr>
                           </Thead>
                           <Tbody>
                              {value.map((row: TableRow, rowIndex: number) => (
                                 <Tr key={rowIndex}>
                                    {headers.map((header) => (
                                       <Td key={header}>
                                          {row[header] !== null &&
                                          row[header] !== undefined
                                             ? typeof row[header] === 'string'
                                                ? formatLabel(row[header])
                                                : row[header]
                                             : '-'}
                                       </Td>
                                    ))}
                                 </Tr>
                              ))}
                           </Tbody>
                        </Table>
                     </Box>
                  </Collapse>
               </Box>
            );
         })}
      </Stack>
   );
};
