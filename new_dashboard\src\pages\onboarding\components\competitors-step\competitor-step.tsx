import { Box, Heading, Text, Button, Flex, useToast } from '@chakra-ui/react';
import './competitor-step.scss';

import { competitorStep } from '../../../../utils/strings/onboarding-strings';
import { useAppSelector } from '../../../../store/store';
import { useApiMutation } from '../../../../hooks/react-query-hooks';
import onboardingEndpoints from '../../../../api/service/onboarding';
import { LocalStorageService, Keys } from '../../../../utils/local-storage';
import {
   setOrganizationType,
   setRegisterProgress,
} from '../../../../store/reducer/onboarding-reducer';
import { useDispatch } from 'react-redux';
import CompetitorsStepBody from './competitors-step-body';
import { useNavigate } from 'react-router-dom';
import keys from '../../../../utils/strings/query-keys';

const CompetitorStep = () => {
   const toast = useToast();
   const navigate = useNavigate();
   const dispatch = useDispatch();

   const { organizationType } = useAppSelector((state) => state.onboarding);

   const registerProgMutation = useApiMutation({
      mutationFn: onboardingEndpoints.updateRegisterProgress,
      onSuccessHandler(response) {
         switch (response.action) {
            case 'register-complete': {
               if (response.details) {
                  const { refreshToken } = response.details;

                  LocalStorageService.setItem(Keys.Token, refreshToken);

                  LocalStorageService.removeItem(Keys.FlableUserDetails);
                  LocalStorageService.removeItem(Keys.ClientId);

                  navigate('/auth/choose-profile');
               } else {
                  toast({
                     title: 'Error',
                     description: 'Token missing',
                     status: 'error',
                     duration: 5000,
                     isClosable: true,
                  });
               }
               break;
            }
            case 'company-added': {
               dispatch(setRegisterProgress('Step 2'));
               dispatch(setOrganizationType('Marketing Agency'));

               LocalStorageService.removeItem(Keys.FlableUserDetails);
               LocalStorageService.removeItem(Keys.ClientId);
               break;
            }
         }
      },
      onError(msg) {
         toast({
            title: 'Error',
            description: msg,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
      invalidateCacheQuery: [keys.fetchUserDetails],
   });

   const addNewCompanyMutation = useApiMutation({
      mutationFn: onboardingEndpoints.addNewCompany,
      onSuccessHandler() {
         toast({
            title: 'Success',
            description: 'Company added successfully',
            status: 'success',
            duration: 5000,
            isClosable: true,
         });
      },
      onError(msg) {
         toast({
            title: 'Error',
            description: msg,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
   });

   const handleBack = () => {
      registerProgMutation.mutate({
         email_address: LocalStorageService.getItem(Keys.UserName) as string,
         client_id: LocalStorageService.getItem(Keys.ClientId) as string,
         register_progress: 'Step 4',
      });
   };

   const handleNavigateDashboard = () => {
      registerProgMutation.mutate({
         email_address: LocalStorageService.getItem(Keys.UserName) as string,
         client_id: LocalStorageService.getItem(Keys.ClientId) as string,
         register_progress: 'Completed',
         action: 'register-complete',
      });
   };

   const handleAddAnotherCompany = () => {
      addNewCompanyMutation.mutate({
         email_address: LocalStorageService.getItem(Keys.UserName) as string,
      });

      registerProgMutation.mutate({
         email_address: LocalStorageService.getItem(Keys.UserName) as string,
         client_id: LocalStorageService.getItem(Keys.ClientId) as string,
         register_progress: 'Completed',
         action: 'company-added',
      });
   };

   return (
      <Box
         width='100%'
         height='100%'
         className='competitor-step'
         display='flex'
         alignItems='center'
         justifyContent='center'
         flexDirection='column'
      >
         <Box
            width='60%'
            display='flex'
            alignItems='center'
            justifyContent='center'
            flexDirection='column'
         >
            <Box width='60%' marginTop='20px'>
               <Flex
                  direction='column'
                  alignItems='center'
                  justifyContent='center'
                  gap='10px'
               >
                  <Heading fontSize='32px' fontWeight='500' color='#424242'>
                     {competitorStep.title}
                  </Heading>
                  <Text
                     fontSize='14px'
                     fontWeight='400'
                     color='gray'
                     textAlign='center'
                     width='80%'
                  >
                     {competitorStep.info}
                  </Text>
               </Flex>
            </Box>
            <CompetitorsStepBody />
            <Box display='flex' gap='10px' marginTop='20px'>
               <Button colorScheme='gray' onClick={handleBack}>
                  <Text fontSize='16px' fontWeight={500}>
                     Back
                  </Text>
               </Button>
               {organizationType === 'Marketing Agency' && (
                  <Button
                     color='#424242'
                     background='none'
                     border='1px solid #437eeb'
                     onClick={handleAddAnotherCompany}
                  >
                     <Text fontSize='16px' fontWeight={500} color='#437eeb'>
                        Add another Company
                     </Text>
                  </Button>
               )}
               <Button colorScheme='blue' onClick={handleNavigateDashboard}>
                  <Text fontSize='16px' fontWeight={500}>
                     Go to Dashboard
                  </Text>
               </Button>
            </Box>
         </Box>
      </Box>
   );
};

export default CompetitorStep;
