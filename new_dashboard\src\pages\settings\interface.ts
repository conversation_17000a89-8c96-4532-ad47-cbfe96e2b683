export interface SettingsMode {
   title: string;
   key: Modes;
   description: string;
}
export interface CostSettingOptions {
   title: string;
   key: CSModes;
   description: string;
}

export type Modes =
   | 'languages'
   | 'competitors'
   | 'reports'
   | 'costsettings'
   | 'apitoken'
   | 'attributions'
   | 'plansTopups'
   | 'usagesBilling';
export type CSModes =
   | 'cogs'
   | 'shipping'
   | 'paymentgate'
   | 'customexpenses'
   | '';

export interface FixedExpense {
   clientId?: string;
   id?: number | null;
   title: string;
   cost: number | string;
   source: string;
   categories: string[];
   selCategory: string;
   startDate: Date | string | null;
   endDate: Date | string | null;
   adSpend: boolean;
   recurringDays: number | string;
}
export interface VariableExpense {
   clientId?: string;
   id?: number | null;
   name: string;
   campaign: string;
   metric: string;
   source: string;
   percent: number | string;
   categories: string[];
   selCategory: string;
   startDate: Date | string | null;
   endDate: Date | string | null;
   adSpend: boolean;
}
export interface FixedExpenseData {
   clientId?: string;
   id?: number | null;
   title: string;
   cost: number | string;
   source: string;
   categories: string;
   sel_category: string;
   start_date: Date | string | null;
   end_date: Date | string | null;
   ad_spend: boolean;
   recurring_days: number | string;
}
export interface VariableExpenseData {
   clientId?: string;
   id?: number | null;
   name: string;
   campaign: string;
   metric: string;
   source: string;
   percent: number | string;
   categories: string;
   sel_category: string;
   start_date: Date | string | null;
   end_date: Date | string | null;
   ad_spend: boolean;
}
