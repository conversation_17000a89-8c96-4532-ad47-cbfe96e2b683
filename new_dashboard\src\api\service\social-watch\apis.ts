import Config from '../../../config';
import { MultimediaData } from '../../../types/social-watch';
import dashboardApiAgent from '../../agent';
import { PromiseAxios } from '../common';

interface TweetPayload {
   oauthToken: string;
   oauthTokenSecret: string;
   tweets: string;
}

interface ContentCalendarDataPayload {
   time: string;
   date: string;
   social_media_type: string;
   is_schedule: boolean;
   post_data: string;
   client_id: string;
   uuid: string;
   linkedinPageId: string | null;
}

interface AiSuggestionPayload {
   client_id: string;
   social_channel: string;
   reference_text: string;
   tone: string;
   no_hashtags: string;
   word_size: string;
   top_posts: string;
   top_competitor_posts: string;
   top_hashtags: string;
}

interface ContentCalendarPayload {
   client_id: string;
   social_media_types: string[];
}
export interface LinkedinProfile {
   id: string;
   localizedFirstName: string;
}
interface ContentCalendarData {
   _id: string;
   time: string;
   date: string;
   social_media_type: string;
   post_data: string;
   client_id: string;
   is_schedule: boolean;
   uuid: string;
   media: MultimediaData[];
}
export interface LinkedinPageDetails {
   localizedName: string;
   id: number;
}

export interface LinkedinSelectPages {
   id: string;
   name: string;
   page: boolean;
}

interface LinkedinPostPayload {
   content: string;
   linkedinUserId: string;
   token: string;
   pageId: string | null;
}

interface TwitterConnectionDetail {
   oauthToken: string;
   oauthTokenSecret: string;
   screenName: string;
   socialMedia: string;
   userId: string;
   clientID: string;
}

interface TwitterTokenData {
   oauthToken: string;
   oauthTokenSecret: string;
   authorizeURL: string;
}

interface TwitterAccessTokenPayload {
   clientId: string;
   pin: string;
   oauthToken: string;
}

interface Endpoints {
   requestTwitterToken: () => PromiseAxios<{ tokenData: TwitterTokenData }>;
   fetchTwitterAccessToken: (
      data: TwitterAccessTokenPayload,
   ) => PromiseAxios<{ connectionDetails: TwitterConnectionDetail }>;

   uploadImageToTwitter: (data: FormData) => PromiseAxios<{ mediaId: string }>;
   uploadImageToLinkedin: (data: FormData) => PromiseAxios<{ assetId: string }>;
   postTweets: (data: TweetPayload) => PromiseAxios<{ message: string }>;
   postContentCalendar: (
      data: ContentCalendarDataPayload,
   ) => PromiseAxios<{ message: string }>;
   getAiSuggestion: (
      payload: AiSuggestionPayload,
   ) => PromiseAxios<{ captions: string[]; media: string }>;

   getContentCalendarData: (
      payload: ContentCalendarPayload,
   ) => PromiseAxios<{ contentCalendarData: ContentCalendarData[] }>;

   updateContentCalendarData: (
      data: Partial<ContentCalendarData> & { uuid: string },
   ) => PromiseAxios<{ updatedData: ContentCalendarData }>;
   uploadMediaToAzure: (data: FormData) => PromiseAxios<{ uri: string }>;
   decodeAzureMediaUrl: (data: {
      image_url: string;
   }) => PromiseAxios<{ uri: string }>;

   authorize: (url: string) => string;
   getLinkedinPages: (data: {
      token: string;
   }) => PromiseAxios<LinkedinPageDetails[]>;

   getLinkedinUserDetails: (data: {
      token: string;
   }) => PromiseAxios<{ linkedinUser: LinkedinProfile }>;

   postContentOnLinkedin: (
      data: LinkedinPostPayload,
   ) => PromiseAxios<{ postId: string }>;
}

const socialWatchEndpoints: Endpoints = {
   // twitter auth
   requestTwitterToken: () =>
      dashboardApiAgent.get('/twitter-auth/request-token'),
   fetchTwitterAccessToken: (payload) =>
      dashboardApiAgent.post('/twitter-auth/access-token', payload),
   uploadImageToTwitter: (data) =>
      dashboardApiAgent.post('/twitter/upload', data, {
         headers: {
            'Content-Type': 'multipart/form-data',
         },
      }),
   postTweets: (data) => dashboardApiAgent.post('/twitter/tweets', data),
   postContentCalendar: (data) =>
      dashboardApiAgent.post('/twitter/contentcalendar', data),
   getAiSuggestion: (data) =>
      dashboardApiAgent.post('/twitter/get_caption', data),
   getContentCalendarData: (data) =>
      dashboardApiAgent.post('/twitter/content-calendar', data),

   updateContentCalendarData: (data) =>
      dashboardApiAgent.put('/twitter/update', data),
   uploadMediaToAzure: (data) =>
      dashboardApiAgent.post('/azureblob/encode', data, {
         headers: {
            'Content-Type': 'multipart/form-data',
            Accept: 'application/json',
         },
         transformRequest: (data) => {
            if (data instanceof FormData) {
               return data;
            }
            return JSON.stringify(data);
         },
      }),
   decodeAzureMediaUrl: (data) =>
      dashboardApiAgent.post('/azureblob/decode', data),
   authorize: (url) => encodeURI(`${Config.VITE_BE_API}${url}`),
   getLinkedinPages: (data) => dashboardApiAgent.post('/linkedin/pages', data),

   getLinkedinUserDetails: (data) =>
      dashboardApiAgent.post('/linkedin/user-details', data),

   postContentOnLinkedin: (data) =>
      dashboardApiAgent.post('/linkedin/post-content', data),
   uploadImageToLinkedin: (data) =>
      dashboardApiAgent.post('/linkedin/upload', data, {
         headers: {
            'Content-Type': 'multipart/form-data',
         },
      }),
};

export type { ContentCalendarData };

export default socialWatchEndpoints;
