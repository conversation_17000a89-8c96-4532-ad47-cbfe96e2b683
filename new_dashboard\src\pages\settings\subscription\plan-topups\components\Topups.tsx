import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { cn } from '@/utils';
import { CheckItem } from './PricingCard';
import subsEndPoints from '@/api/service/subscription';
import { useApiMutation, useApiQuery } from '@/hooks/react-query-hooks';
import { Skeleton } from '@/components/ui/skeleton';
import { agentImg } from '../../constants';
import { UserDetails } from '@/pages/socialwatch/interface';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { useDispatch } from 'react-redux';
import { closeModal, openModal } from '@/store/reducer/modal-reducer';
import { modalTypes } from '@/components/modals/modal-types';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/store';
import {
   AgentTopupPayload,
   RazorpayInstance,
   RazorpayOptions,
   RazorpayResponse,
} from '@/pages/settings/subscription/types';
import DefaultCard from '@/components/DefaultCard';
import { toast } from 'sonner';

export default function Topups() {
   const [selectedAgents, setSelectedAgents] = useState<{
      [key: string]: { selected: boolean; tokens: number };
   }>({});
   const { isActive } = useSelector((state: RootState) => state.subscription);
   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);
   const [loading, setLoading] = useState(false);
   const dispatch = useDispatch();
   const { data: agents, isLoading: isAgentLoading } = useApiQuery({
      queryKey: ['agents'],
      queryFn: () => subsEndPoints.fetchAgents(),
      enabled: true,
      refetchOnWindowFocus: false,
      staleTime: Infinity,
   });

   const toggleAgent = (id: string) => {
      setSelectedAgents((prev) => ({
         ...prev,
         [id]: {
            selected: !prev[id]?.selected,
            tokens: prev[id]?.tokens || 1,
         },
      }));
   };

   const setTokens = (id: string, value: number) => {
      setSelectedAgents((prev) => ({
         ...prev,
         [id]: {
            selected: true,
            tokens: value,
         },
      }));
   };

   const handleSuccess = (data: {
      success: boolean;
      message: string;
      order_id?: string;
   }) => {
      setLoading(false);
      dispatch(closeModal());

      if (!window.Razorpay) {
         alert('Razorpay SDK not loaded');
         return;
      }

      const options = {
         key: import.meta.env.VITE_RAZRPAY_KEY_ID as string,
         name: 'Flable Ai',
         description: 'Top-up Payment',
         order_id: data.order_id,
         handler: async function (response: RazorpayResponse) {
            try {
               const verify = await fetch(
                  `${import.meta.env.VITE_BE_API}/subs/razorpay/verify-signature`,
                  {
                     method: 'POST',
                     headers: { 'Content-Type': 'application/json' },
                     body: JSON.stringify({
                        ...response,
                        type: 'order',
                     }),
                  },
               );
               const result = (await verify.json()) as { success: boolean };
               if (result.success) {
                  console.log('Top-up Successful!');
                  dispatch(closeModal());
               } else {
                  console.warn('Verification Failed!');
               }
            } catch (error) {
               console.error('Verification error: ', error);
            }
         },
         prefill: {
            email: userDetails.email,
            contact: null,
         },
      };
      const rzp: RazorpayInstance = new window.Razorpay(
         options as unknown as RazorpayOptions,
      );
      rzp.open();
   };

   const handleError = () => {
      setLoading(false);
      dispatch(closeModal());
      toast.error('Failed to initiate top-up. Please try again.');
   };

   const { mutate: buyTopup } = useApiMutation({
      queryKey: ['buytopup'],
      mutationFn: subsEndPoints.purchaseTopup,
      onSuccessHandler: handleSuccess,
      onError: handleError,
   });

   const handleBuyNow = () => {
      if (isActive === true) {
         const agentTopupPayload: AgentTopupPayload[] = Object.entries(
            selectedAgents,
         )
            .filter(([, data]) => data.selected)
            .map(([id, data]) => {
               const agent = agents!.find((a) => a.agent_id === id)!;
               return {
                  id,
                  name: agent.agent_name,
                  tokens: data.tokens,
                  pricePerToken: agent.pricepertoken,
                  totalPrice: data.tokens * agent.pricepertoken,
               };
            });

         const totalAmount = agentTopupPayload.reduce(
            (sum, item) => sum + item.totalPrice,
            0,
         );
         if (totalAmount <= 0.99) {
            toast.error('Please select at least one agent');
            return;
         }
         if (loading) return;

         setLoading(true);
         dispatch(
            openModal({
               modalType: modalTypes.LOADER_MODAL,
            }),
         );

         buyTopup({
            client_id: userDetails.client_id,
            amount: totalAmount,
            currency: agents?.[0].currency || 'INR',
            topup: true,
            agents: agentTopupPayload,
         });
      } else {
         toast.error("You don't have active subscription");
      }
   };

   if (!(isActive === true)) {
      return (
         <DefaultCard
            banner='inactivePlan'
            title='No Active Subscription'
            desc='Choose a plan that fits your needs and unlock full access to our platform.'
            actionLabel='Subscribe to a plan'
            navigate='/settings?mode=plansTopups&tab=plans'
         />
      );
   }

   return (
      <div className='space-y-6 max-w-6xl mx-auto flex flex-col justify-end items-end gap-6'>
         <div className='grid grid-cols-1 md:grid-cols-3 gap-4 w-full'>
            {isAgentLoading ? (
               Array.from({ length: 3 }).map((_, idx) => (
                  <Skeleton key={idx} className='h-100 w-full' />
               ))
            ) : (
               <>
                  {agents &&
                     agents
                        .filter(
                           (agent) => agent.agent_name !== 'Alerting Agent',
                        )
                        .map((agent) => {
                           const agentState = selectedAgents[
                              agent.agent_id
                           ] || {
                              selected: false,
                              tokens: 1,
                           };
                           const isSelected = agentState.selected;
                           const thumbPosition =
                              ((agentState.tokens - 1) / (500 - 1)) * 100;

                           return (
                              <div
                                 key={agent.agent_id}
                                 className={`${isSelected && 'bg-porcelain rounded-b-3xl'}`}
                              >
                                 <div
                                    onClick={() => toggleAgent(agent.agent_id)}
                                    className={cn(
                                       'border rounded-3xl p-4 space-y-3 relative bg-white cursor-pointer',
                                       isSelected
                                          ? '!border-cerulean shadow'
                                          : '!border-fog',
                                    )}
                                 >
                                    <div
                                       className={cn(
                                          'relative w-5 h-5 rounded-full border',
                                          { '!border-cerulean': isSelected },
                                          '!border-steel',
                                       )}
                                    >
                                       {isSelected && (
                                          <div className='absolute inset-1 rounded-full bg-cerulean' />
                                       )}
                                    </div>

                                    <div className='flex flex-col gap-4 items-center justify-around text-center relative'>
                                       <img
                                          className='w-18 h-18 rounded-full'
                                          src={
                                             agentImg[
                                                agent.agent_name as keyof typeof agentImg
                                             ]
                                          }
                                          alt={`${agent.agent_name}-img`}
                                       />

                                       <h3 className='font-semibold head6 text-jet'>
                                          {agent.agent_name}
                                       </h3>
                                       <p className='para6 text-charcoal px-4'>
                                          {agent.agent_desc}
                                       </p>
                                       <div className='head3 text-jet font-semibold'>
                                          ${agent.pricepertoken}{' '}
                                          <span className='text-steel para5 font-medium'>
                                             / token
                                          </span>
                                       </div>

                                       <div
                                          className='relative w-full'
                                          onClick={(e) => e.stopPropagation()}
                                       >
                                          {isSelected && (
                                             <div
                                                className='absolute -top-16 z-50'
                                                style={{
                                                   left: `${thumbPosition}%`,
                                                   transform:
                                                      'translateX(-50%)',
                                                }}
                                             >
                                                <div className='relative z-10 shadow-md para4 text-cerulean px-3 whitespace-nowrap rounded-md py-2 bg-white border border-fog'>
                                                   <input
                                                      type='number'
                                                      value={agentState.tokens}
                                                      onChange={(e) => {
                                                         const val = Math.max(
                                                            1,
                                                            Math.min(
                                                               500,
                                                               Number(
                                                                  e.target
                                                                     .value,
                                                               ),
                                                            ),
                                                         );
                                                         setTokens(
                                                            agent.agent_id,
                                                            val,
                                                         );
                                                      }}
                                                      className='w-16 text-center border-none outline-none bg-transparent para4 text-cerulean'
                                                      min={1}
                                                      max={500}
                                                   />{' '}
                                                   Tokens
                                                   <div
                                                      className='absolute left-1/2 top-full -translate-x-1/2 w-0 h-0'
                                                      style={{
                                                         borderLeft:
                                                            '8px solid transparent',
                                                         borderRight:
                                                            '8px solid transparent',
                                                         borderTop:
                                                            '8px solid white',
                                                      }}
                                                   />
                                                </div>
                                             </div>
                                          )}

                                          <Slider
                                             value={[agentState.tokens]}
                                             min={1}
                                             max={500}
                                             onValueChange={(value) =>
                                                setTokens(
                                                   agent.agent_id,
                                                   value[0],
                                                )
                                             }
                                             className='w-full'
                                          />
                                       </div>
                                    </div>
                                 </div>

                                 <AnimatePresence>
                                    {isSelected && (
                                       <motion.div
                                          initial={{ opacity: 0, height: 0 }}
                                          animate={{
                                             opacity: 1,
                                             height: 'auto',
                                          }}
                                          exit={{ opacity: 0, height: 0 }}
                                          className='overflow-hidden mt-4 para6 p-4 space-y-2'
                                          onClick={(e) => e.stopPropagation()}
                                       >
                                          <p className='text-jet font-medium mb-3'>
                                             What's Included:
                                          </p>
                                          {agent.agent_features.map(
                                             (feature: string) => (
                                                <CheckItem
                                                   key={feature}
                                                   text={feature}
                                                />
                                             ),
                                          )}
                                       </motion.div>
                                    )}
                                 </AnimatePresence>
                              </div>
                           );
                        })}
               </>
            )}
         </div>

         {!isAgentLoading && (
            <div className='w-full flex justify-end'>
               <Button onClick={handleBuyNow}>
                  {loading ? 'Processing..' : 'Buy Now'}
               </Button>
            </div>
         )}
      </div>
   );
}
