import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>a<PERSON>ye, FaEyeSlash } from 'react-icons/fa';
import {
   Box,
   Button,
   FormControl,
   Input,
   Image,
   useToast,
   FormErrorMessage,
   InputGroup,
   InputRightElement,
   Text,
   FormLabel,
   HStack,
   PinInputField,
   PinInput,
} from '@chakra-ui/react';
import { useApiMutation } from '../../hooks/react-query-hooks';
import { forgotPasswordStrings } from '../../utils/strings/login-strings';
import { appStrings } from '../../utils/strings/app-strings';

import ICON from '../../assets/icons/icon.png';
import keys from '../../utils/strings/query-keys';
import authEndpoints from '../../api/service/auth';
import { regex } from '../../utils/strings/auth-strings';

function ForgotPassword() {
   const navigate = useNavigate();
   const toast = useToast();

   const [status, setStatus] = useState<
      'Send OTP' | 'OTP Verification' | 'Password Reset'
   >('Send OTP');

   const [otp, setOtp] = useState('');
   const [showPassword, setShowPassword] = useState({
      password: false,
      confirm_password: false,
   });
   const [form, setForm] = useState({
      email_address: '',
      password: '',
      confirm_password: '',
   });
   const [error, setError] = useState({
      email_address: '',
      password: '',
      confirm_password: '',
   });

   const sendOtpMutation = useApiMutation({
      queryKey: [keys.sendOtp],
      mutationFn: authEndpoints.sendOtp,
      onSuccessHandler: () => {
         setStatus('OTP Verification');

         toast({
            title: 'Success',
            description: 'OTP sent to your email.',
            status: 'success',
            duration: 5000,
            isClosable: true,
         });
      },
      onError: (msg) => {
         toast({
            title: 'Error',
            description: msg,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
   });

   const verifyOtpMutation = useApiMutation({
      queryKey: [keys.verifyOtp],
      mutationFn: authEndpoints.verifyEmail,
      onSuccessHandler: () => {
         setStatus('Password Reset');

         toast({
            title: 'Success',
            description: 'Email verified successfully.',
            status: 'success',
            duration: 5000,
            isClosable: true,
         });
      },
      onError: (msg) => {
         if (msg === 'OTP expired. Please login again.') {
            navigate('/auth/login');
         }

         toast({
            title: 'Failed',
            description: msg,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
   });

   const resetPasswordMutation = useApiMutation({
      queryKey: [keys.resetPassword],
      mutationFn: authEndpoints.resetPassword,
      onSuccessHandler: () => {
         toast({
            title: 'Success',
            description: 'Password reset successfully.',
            status: 'success',
            duration: 5000,
            isClosable: true,
         });

         navigate('/auth/login');
      },
      onError: (msg) => {
         toast({
            title: 'Error',
            description: msg,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
   });

   const validateFields = (name: string, value: string) => {
      switch (name) {
         case 'email_address':
            if (!regex.email_address.test(value)) {
               setError((prevErrors) => ({
                  ...prevErrors,
                  email_address: 'Please enter a valid email address',
               }));
            } else {
               setError((prevErrors) => ({
                  ...prevErrors,
                  email_address: '',
               }));
            }
            break;

         case 'password':
            if (!regex.password.test(value)) {
               setError((prevErrors) => ({
                  ...prevErrors,
                  password:
                     'Password must be at least 8 characters long, must have atleast one number and one special character',
               }));
            } else {
               setError((prevErrors) => ({
                  ...prevErrors,
                  password: '',
               }));
            }

            break;

         case 'confirm_password':
            if (!regex.password.test(value)) {
               setError((prevErrors) => ({
                  ...prevErrors,
                  confirm_password:
                     'Password must be at least 8 characters long, must have atleast one number and one special character',
               }));
            } else {
               setError((prevErrors) => ({
                  ...prevErrors,
                  confirm_password: '',
               }));
            }
      }
   };

   const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setForm((prevForm) => ({
         ...prevForm,
         [name]: value,
      }));
      validateFields(name, value);
   };

   const handleOtpChange = (value: string) => {
      setOtp(value);
   };

   const handleSendOtp = () => {
      let emailError = '';

      if (!form.email_address) {
         emailError = 'Email is required';
         setError((prev) => ({ ...prev, email_address: emailError }));
      } else if (!/\S+@\S+\.\S+/.test(form.email_address)) {
         emailError = 'Please enter a valid email address';
         setError((prev) => ({ ...prev, email_address: emailError }));
      }

      !emailError &&
         sendOtpMutation.mutate({
            email_address: form.email_address,
            action: 'password-reset',
         });
   };

   const handleVerifyOtp = () => {
      verifyOtpMutation.mutate({
         email_address: form.email_address,
         email_otp: otp,
         action: 'password-reset',
      });
   };

   const handleResetPassword = () => {
      resetPasswordMutation.mutate({
         email_address: form.email_address,
         password: form.password,
      });
   };

   return (
      <>
         <Box
            display='flex'
            justifyContent='center'
            alignItems='center'
            height='17%'
            maxHeight='70px'
         >
            <Image src={ICON} alt='Flable Icon' h='45%' />
            <Box ml={2} fontSize='xl' fontWeight='bold'>
               {appStrings.companyName}
            </Box>
         </Box>
         <Box height='70%'>
            <Text as='h2' textAlign='center' fontWeight='bold' fontSize='22px'>
               Reset your Password
            </Text>
            <Text as='p' textAlign='center' fontSize='14px'>
               Enter your email address and we'll send you an One Time Password
               (OTP) to verify your email.
            </Text>
            {status === 'Send OTP' && (
               <FormControl
                  mt={3}
                  mb={2}
                  id='email_address'
                  height='17%'
                  isInvalid={!!error.email_address}
               >
                  <FormLabel ml={1} mb={0} fontSize='sm'>
                     Email Address *
                  </FormLabel>
                  <Input
                     type='email'
                     name='email_address'
                     value={form.email_address}
                     onChange={handleChange}
                     placeholder='Enter your email address'
                     variant='outline'
                     height='60%'
                     disabled={status !== 'Send OTP'}
                  />
                  {error.email_address && (
                     <FormErrorMessage fontSize='xs'>
                        {error.email_address}
                     </FormErrorMessage>
                  )}
               </FormControl>
            )}

            {status === 'Send OTP' && (
               <Button
                  mt={3}
                  width='full'
                  disabled={sendOtpMutation.isPending || !form.email_address}
                  colorScheme='blue'
                  onClick={handleSendOtp}
                  size='sm'
                  isLoading={sendOtpMutation.isPending}
               >
                  {forgotPasswordStrings.sendOtp}
               </Button>
            )}

            {status === 'OTP Verification' && (
               <>
                  <Text as='p' textAlign='center' mt={3} fontSize={14}>
                     Enter the OTP below to verify it.
                  </Text>
                  <Box mt={3}>
                     <HStack>
                        <PinInput
                           value={otp}
                           defaultValue={otp}
                           size='lg'
                           onChange={handleOtpChange}
                        >
                           <PinInputField />
                           <PinInputField />
                           <PinInputField />
                           <PinInputField />
                           <PinInputField />
                           <PinInputField />
                        </PinInput>
                     </HStack>
                  </Box>
                  <Button
                     width='full'
                     mt={3}
                     colorScheme='gray'
                     onClick={handleVerifyOtp}
                     size='sm'
                     disabled={otp.length !== 6}
                     isLoading={verifyOtpMutation.isPending}
                  >
                     Verify OTP
                  </Button>
               </>
            )}
            {status === 'Password Reset' && (
               <>
                  <FormControl
                     mt={5}
                     id='password'
                     height='28%'
                     minHeight='87px'
                     isInvalid={!!error.password}
                  >
                     <FormLabel ml={1} mb={0} fontSize='sm'>
                        Password *
                     </FormLabel>
                     <InputGroup height='35%'>
                        <Input
                           type={showPassword.password ? 'text' : 'password'}
                           name='password'
                           value={form.password}
                           onChange={handleChange}
                           placeholder='Enter your password'
                           variant='outline'
                           height='100%'
                        />
                        <InputRightElement>
                           <Button
                              h='1.75rem'
                              size='sm'
                              onClick={() =>
                                 setShowPassword((prev) => ({
                                    ...prev,
                                    password: !prev.password,
                                 }))
                              }
                              variant='ghost'
                           >
                              {showPassword.password ? (
                                 <FaEyeSlash />
                              ) : (
                                 <FaEye />
                              )}
                           </Button>
                        </InputRightElement>
                     </InputGroup>
                     {error.password && (
                        <FormErrorMessage ml={1} fontSize='12px' height='20%'>
                           {error.password}
                        </FormErrorMessage>
                     )}
                  </FormControl>
                  <FormControl
                     height='28%'
                     minHeight='87px'
                     id='confirm_password'
                     isInvalid={!!error.confirm_password}
                  >
                     <FormLabel ml={1} fontSize='sm'>
                        Confirm Password *
                     </FormLabel>
                     <InputGroup height='35%'>
                        <Input
                           type={
                              showPassword.confirm_password
                                 ? 'text'
                                 : 'password'
                           }
                           name='confirm_password'
                           value={form.confirm_password}
                           onChange={handleChange}
                           placeholder='Confirm your password'
                           variant='outline'
                           height='100%'
                        />
                        <InputRightElement>
                           <Button
                              h='1.75rem'
                              size='sm'
                              onClick={() =>
                                 setShowPassword((prev) => ({
                                    ...prev,
                                    confirm_password: !prev.confirm_password,
                                 }))
                              }
                              variant='ghost'
                           >
                              {showPassword.confirm_password ? (
                                 <FaEyeSlash />
                              ) : (
                                 <FaEye />
                              )}
                           </Button>
                        </InputRightElement>
                     </InputGroup>
                     {error.confirm_password && (
                        <FormErrorMessage ml={1} fontSize='12px' height='20%'>
                           {error.confirm_password}
                        </FormErrorMessage>
                     )}
                  </FormControl>
                  <Button
                     mt={3}
                     width='full'
                     disabled={resetPasswordMutation.isPending}
                     colorScheme='blue'
                     onClick={handleResetPassword}
                     size='sm'
                     isLoading={resetPasswordMutation.isPending}
                  >
                     {forgotPasswordStrings.changePassword}
                  </Button>
               </>
            )}
         </Box>
         <Text
            as='span'
            mt={5}
            float='right'
            fontSize='xs'
            sx={{
               cursor: 'pointer',
               textDecoration: 'underline dashed',
               '&:hover': {
                  textDecoration: 'underline',
                  fontWeight: 'semibold',
               },
            }}
            onClick={() => navigate('/auth/login')}
         >
            Go to Login Page
         </Text>
      </>
   );
}

export default ForgotPassword;
