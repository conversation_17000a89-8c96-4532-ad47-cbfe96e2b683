import React from 'react';
import {
   Button,
   VStack,
   Text,
   useColorModeValue,
   Flex,
   Icon,
   Box,
} from '@chakra-ui/react';
import { WarningIcon, InfoIcon, CheckIcon } from '@chakra-ui/icons';
import ModalWrapper from './modal-wrapper';
import { useAppSelector } from '../../store/store';
import { useDispatch } from 'react-redux';
import { closeModal } from '../../store/reducer/modal-reducer';

interface ConfirmationModalProps {
   title: string;
   message: string;
   confirmButtonText?: string;
   cancelButtonText?: string;
   confirmButtonColor?: string;
   cancelButtonColor?: string;
   icon?: 'warning' | 'info' | 'success';
   showCancelButton?: boolean;
   onConfirm: () => void;
   onCancel?: () => void;
}

const ConfirmationModal: React.FC = () => {
   const dispatch = useDispatch();
   const { payload } = useAppSelector((state) => state.modal);

   const {
      title,
      message,
      confirmButtonText = 'OK',
      cancelButtonText = 'Cancel',
      confirmButtonColor = 'blue',
      cancelButtonColor = 'gray',
      icon = 'info',
      showCancelButton = false,
      onConfirm,
      onCancel,
   } = (payload?.modalProps || {}) as ConfirmationModalProps;

   const iconColor = useColorModeValue(
      icon === 'warning'
         ? 'orange.500'
         : icon === 'success'
           ? 'green.500'
           : 'blue.500',
      icon === 'warning'
         ? 'orange.300'
         : icon === 'success'
           ? 'green.300'
           : 'blue.300',
   );

   const getIcon = () => {
      switch (icon) {
         case 'warning':
            return WarningIcon;
         case 'success':
            return CheckIcon;
         default:
            return InfoIcon;
      }
   };

   const handleConfirm = () => {
      onConfirm();
      dispatch(closeModal());
   };

   const handleCancel = () => {
      if (onCancel) onCancel();
      dispatch(closeModal());
   };

   const footer = (
      <Flex gap={3} justifyContent='flex-end' width='100%'>
         {showCancelButton && (
            <Button
               variant='outline'
               onClick={handleCancel}
               colorScheme={cancelButtonColor}
            >
               {cancelButtonText}
            </Button>
         )}
         <Button colorScheme={confirmButtonColor} onClick={handleConfirm}>
            {confirmButtonText}
         </Button>
      </Flex>
   );

   return (
      <ModalWrapper
         heading={title}
         footer={footer}
         size='md'
         closeOnEsc={true}
         closeOnOverlayClick={false}
         closeFunction={showCancelButton ? handleCancel : undefined}
      >
         <VStack spacing={4} align='center' py={4}>
            <Box>
               <Icon as={getIcon()} boxSize={12} color={iconColor} />
            </Box>

            <Text
               textAlign='center'
               fontSize='md'
               color={useColorModeValue('gray.700', 'gray.300')}
            >
               {message}
            </Text>
         </VStack>
      </ModalWrapper>
   );
};

export default ConfirmationModal;
