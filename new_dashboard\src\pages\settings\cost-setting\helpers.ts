import { ICountry } from 'countries-list';
interface ContinentAgg {
   [key: string]: ICountry[];
}
export const getContinentAgg = (countries: ICountry[]): ContinentAgg => {
   const continentAgg: ContinentAgg = {};
   countries.forEach((c) => {
      if (continentAgg[c.continent]) continentAgg[c.continent].push(c);
      else continentAgg[c.continent] = [c];
   });
   return continentAgg;
};

export const getCostPerDay = (d1: Date, d2: Date, cost: number) => {
   if (!cost) return 0;
   const diffDays = (d2.getTime() - d1.getTime()) / (1000 * 3600 * 24);
   return (diffDays / cost).toFixed(2);
};
