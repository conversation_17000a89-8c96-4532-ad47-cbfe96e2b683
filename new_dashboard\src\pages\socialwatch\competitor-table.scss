@use '../../sass/variable.scss';

.competitorTableContainer {
   border: 1px solid #d7d7d7;
   border-radius: 12px;

   .btn-generate {
      color: #437eeb;
      border: 1px solid #437eeb;
      border-radius: 10px;
      height: 40px;
      width: 90px;
   }

   h2 {
      font-size: 24px;
   }

   .tabs {
      display: flex;
      margin-bottom: 20px;
      justify-content: center;
   }

   .tab {
      padding: 10px 20px;
      border: 1px solid #ccc;
      background-color: #f9f9f9;
      cursor: pointer;
   }

   .tab.active {
      background-color: #007bff;
      color: #fff;
   }

   .table-container {
      width: 100%;
      overflow-x: auto;
   }

   table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
   }

   th,
   td {
      color: #000000;
      font-weight: 400;
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #ddd;
      // [data-theme='dark'] & {
      //    color: $text_color;
      // }
   }

   a {
      display: flex;
      align-items: center;
      color: #007bff;
      gap: 2px;
      text-decoration: none;
      img {
         height: 20px;
         width: 20px;
      }
   }

   a:hover {
      text-decoration: underline;
   }
}
