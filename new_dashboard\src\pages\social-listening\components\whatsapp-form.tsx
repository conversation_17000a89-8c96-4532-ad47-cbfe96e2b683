import { useState, useEffect } from 'react';
import {
   Box,
   Button,
   Input,
   FormControl,
   FormLabel,
   Text,
   Flex,
   useToast,
   Center,
   Select,
} from '@chakra-ui/react';
import endPoints from '../apis/agent';
import {
   getCountries,
   getCountryCallingCode,
   CountryCode,
} from 'libphonenumber-js';
import { Keys, LocalStorageService } from '../../../utils/local-storage';

const allCountries = getCountries();

const WhatsappForm = () => {
   const [country, setCountry] = useState<CountryCode>('US');
   const [phone, setPhone] = useState('');
   const [otp, setOtp] = useState('');
   const [codeSent, setCodeSent] = useState(false);
   const [sendCodeLoading, setSendCodeLoading] = useState(false);
   const [verifyLoading, setVerifyLoading] = useState(false);
   const [successMsg, setSuccessMsg] = useState('');
   const [errorMsg, setErrorMsg] = useState('');
   const toast = useToast();

   const userDetails = LocalStorageService.getItem<{
      client_id: string;
   } | null>(Keys.FlableUserDetails);
   const user_client_id = userDetails?.client_id || '';

   const countryCode = getCountryCallingCode(country);
   const fullPhone = countryCode + phone.replace(/\D/g, '');

   useEffect(() => {
      return () => {
         setPhone('');
         setOtp('');
         setCodeSent(false);
         setSendCodeLoading(false);
         setVerifyLoading(false);
         setSuccessMsg('');
         setErrorMsg('');
      };
   }, []);

   const handleSendCode = async () => {
      setSendCodeLoading(true);
      setErrorMsg('');
      try {
         const data = await endPoints.connectWhatsapp({
            client_id: user_client_id,
            phone_number: fullPhone,
         });
         if (data?.success) {
            setCodeSent(true);
            toast({ title: data?.message, status: 'success' });
         } else {
            setErrorMsg(data?.message || 'Failed to send OTP');
            toast({
               title: data?.message || 'Failed to send OTP',
               status: 'error',
            });
         }
      } catch (e) {
         setErrorMsg('Failed to send OTP');
         toast({ title: 'Failed to send OTP', status: 'error' });
      }
      setSendCodeLoading(false);
   };

   const handleVerify = async (e: React.FormEvent) => {
      e.preventDefault();
      setVerifyLoading(true);
      setErrorMsg('');
      try {
         const data = await endPoints.verifyWhatsapp({
            client_id: user_client_id,
            phone_number: fullPhone,
            otp,
         });
         if (data?.success) {
            setSuccessMsg(
               data?.message || 'WhatsApp Connected! Redirecting...',
            );
            toast({ title: data?.message, status: 'success' });
            setTimeout(() => {
               window.location.href = '/integrations';
            }, 2000);
         } else {
            setErrorMsg(data?.message || 'OTP verification failed');
            toast({
               title: data?.message || 'OTP verification failed',
               status: 'error',
            });
         }
      } catch (e) {
         setErrorMsg('OTP verification failed');
         toast({ title: 'OTP verification failed', status: 'error' });
      }
      setVerifyLoading(false);
   };

   return (
      <Center minH='100vh' bg='gray.50' px={2}>
         <Box
            as='form'
            onSubmit={(e: React.FormEvent) => void handleVerify(e)}
            p={6}
            borderRadius='md'
            boxShadow='md'
            bg='white'
            w='100%'
            maxW='400px'
            maxH='90vh'
            overflowY='auto'
            display='flex'
            flexDirection='column'
            justifyContent='center'
            alignItems='center'
         >
            <Text fontSize='xl' fontWeight='bold' mb={2}>
               Connect WhatsApp to Flable
            </Text>
            <Text fontSize='sm' color='gray.600' mb={6}>
               We'll only use your number to send important alerts and
               automation-related messages.
            </Text>
            {successMsg && (
               <Text color='green.600' mb={4} fontWeight={500}>
                  {successMsg}
               </Text>
            )}
            {errorMsg && (
               <Text color='red.500' mb={4} fontWeight={500}>
                  {errorMsg}
               </Text>
            )}
            <FormControl mb={4} isRequired>
               <FormLabel>Phone number</FormLabel>
               <Flex>
                  <Select
                     maxW='110px'
                     value={country}
                     onChange={(e) => setCountry(e.target.value as CountryCode)}
                     mr={2}
                     isDisabled={codeSent}
                  >
                     {allCountries.map((c) => (
                        <option key={c} value={c}>
                           {c} +{getCountryCallingCode(c)}
                        </option>
                     ))}
                  </Select>
                  <Input
                     placeholder='(*************'
                     value={phone}
                     onChange={(e) => setPhone(e.target.value)}
                     isDisabled={codeSent}
                  />
               </Flex>
               <Text fontSize='xs' color='gray.500' mt={1}>
                  Make sure this number is registered on WhatsApp.
               </Text>
            </FormControl>
            <FormControl mb={2} isRequired>
               <FormLabel>Verification code</FormLabel>
               <Flex>
                  <Input
                     placeholder='Enter 6-digit code'
                     value={otp}
                     onChange={(e) => setOtp(e.target.value)}
                     maxLength={6}
                     isDisabled={!codeSent}
                  />
                  <Button
                     ml={2}
                     onClick={() => void handleSendCode()}
                     isLoading={sendCodeLoading}
                     isDisabled={!phone || codeSent}
                     type='button'
                     minW='110px'
                     variant='outline'
                     borderColor='blue.500'
                     color='blue.500'
                     bg='white'
                     _hover={{ bg: 'blue.50' }}
                  >
                     Send Code
                  </Button>
               </Flex>
            </FormControl>
            {codeSent && (
               <Text fontSize='xs' color='gray.500' mb={4}>
                  <Button
                     variant='link'
                     colorScheme='blue'
                     size='xs'
                     onClick={() => void handleSendCode()}
                     p={0}
                     h='auto'
                     minW='unset'
                  >
                     Resend Code
                  </Button>
               </Text>
            )}
            <Flex justify='flex-end' gap={2} mt={4} w='100%'>
               <Button
                  variant='outline'
                  color='red.500'
                  borderColor='red.500'
                  bg='white'
                  _hover={{ bg: 'red.50' }}
                  onClick={() => {
                     window.location.href = '/integrations';
                  }}
               >
                  Cancel
               </Button>
               <Button
                  type='submit'
                  isLoading={verifyLoading}
                  isDisabled={!phone || !otp || otp.length !== 6}
                  borderColor='blue.500'
                  color='white'
                  bg='blue.500'
               >
                  Verify & Connect
               </Button>
            </Flex>
         </Box>
      </Center>
   );
};

export default WhatsappForm;
