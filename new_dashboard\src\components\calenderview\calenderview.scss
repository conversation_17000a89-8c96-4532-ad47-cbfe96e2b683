@use '../../sass/variable.scss';
.rbc-month-row {
   display: flex;
   flex-wrap: wrap;
   // margin-right: -10px;
   // margin-left: -10px;
   /* overflow: none !important;
     */
   /* override the overflow into none */
   overflow: visible !important;
}
.rbc-event .rbc-day-slot .rbc-background-event {
   border: none;
   box-sizing: border-box;
   box-shadow: none;
   margin: 0;
   padding: 2px 5px;
   background-color: #6da0ff;
   border-radius: 5px;
   color: #fff;
   cursor: pointer;
   width: 100%;
   text-align: left;
}
.card {
}
.card__top {
   background-color: #6da0ff;
   /* display: flex; */
   padding-left: 0.5rem;
   padding-right: 2rem;
   padding-bottom: 0.5rem;
   padding-top: 0.5rem;
   width: 100%;
}
.card__top__time {
   display: flex;
   flex-direction: row;
   justify-content: space-between;
   color: #fff;
}
.card__top__time p {
   margin: 0;
   font-size: 12px;
   /* font-weight: 500; */
   color: #fff;
   width: 50px;
}
.card__top__icon {
}
.card__bottom p {
   margin: 0;
   font-size: 12px;
   font-weight: 500;
   color: #000;
   padding: 1rem;
   /* break line on every 5 char */
   /* word-wrap: break-word; */
   /* word-break: break-all; */
   /* break line on every 5 char */
   white-space: pre-line;
}
.rbc-events-container .rbc-event {
   background-color: #fff;
   border: none;
   box-sizing: border-box;
   box-shadow: none;
   margin: 0;
   padding: 2px 5px;
   border-radius: 5px;
   color: #fff;
   cursor: pointer;
   width: 100%;
   text-align: left;
   height: 100% !important;
}

.rbc-btn-group button {
   [data-theme='dark'] & {
      color: white;
   }
}
// .rbc-toolbar button:active:hover,
// .rbc-toolbar button:active:focus,
// .rbc-toolbar button.rbc-active:hover,
// .rbc-toolbar button.rbc-active:focus {
//    [data-theme='dark'] & {
//       background-color: $controls_hover;
//    }
// }
.rbc-toolbar button:hover {
   // color: $text_color;
   // background-color: $background_light;
   border-color: #adadad;
}
button.rbc-active {
   [data-theme='dark'] & {
      color: black;
   }
}
.rbc-today {
   // [data-theme='dark'] & {
   //    background-color: $background;
   // }
}
.rbc-off-range-bg {
   [data-theme='dark'] & {
      background-color: black;
   }
}
