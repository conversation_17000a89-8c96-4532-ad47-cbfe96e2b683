import { Spinner } from '@chakra-ui/react';
import ModalWrapper from './modal-wrapper';

const LoaderModal = () => {
   return (
      <ModalWrapper
         overlayBgcolor='#00000099'
         heading=''
         noCloseBtn={true}
         bgcolor='transparent'
         isClosable={false}
         closeOnEsc={false}
         boxShadow={false}
      >
         <div
            style={{
               display: 'flex',
               justifyContent: 'center',
               alignItems: 'center',
               height: '50vh',
            }}
         >
            <Spinner size='xl' color='#fff' />
         </div>
      </ModalWrapper>
   );
};

export default LoaderModal;
