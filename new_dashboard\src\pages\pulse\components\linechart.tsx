import React, { useEffect, useState } from 'react';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import { useColorMode } from '@chakra-ui/react';
import { StatusTypes } from '../utils/helper';
import { format } from 'date-fns';

interface KPIData {
   date: string;
   kpi_value: number;
}

interface ChartProp {
   kpiDetails: {
      displayName: string;
      allData: KPIData[];
      stat: string;
   };
}

const LineChart: React.FC<ChartProp> = ({ kpiDetails }) => {
   const isDarkTheme = useColorMode();
   const chartColor = [StatusTypes.ACTIVE, StatusTypes.ENABLED].includes(
      kpiDetails.stat,
   )
      ? isDarkTheme.colorMode === 'dark'
         ? '#00FF00'
         : '#15994A'
      : null;

   const [chartData, setChartData] = useState({
      options: {
         chart: {
            id: kpiDetails.displayName,
            toolbar: {
               show: true,
            },
         },
         xaxis: {
            // type: 'datetime',
            categories: kpiDetails.allData.map((x: KPIData) =>
               format(new Date(x.date), 'MMM dd yyy, E'),
            ),
            labels: {
               show: false,
               style: {
                  colors: isDarkTheme ? ['#fff'] : ['#000000'],
               },
            },
         },
         yaxis: {
            labels: {
               show: false,
               style: {
                  colors: isDarkTheme ? ['#fff'] : ['#000000'],
               },
            },
         },
         stroke: {
            curve: 'smooth',
         },
         dataLabels: {
            enabled: false,
         },
         grid: {
            show: false,
         },
         colors: chartColor ? [chartColor] : undefined,
      },
      series: [
         {
            name: kpiDetails.displayName,
            data: kpiDetails.allData.map((x: KPIData) => x.kpi_value),
         },
      ],
   });

   useEffect(() => {
      setChartData({
         options: {
            chart: {
               id: kpiDetails.displayName,
               toolbar: {
                  show: false,
               },
            },
            xaxis: {
               // type: 'datetime',
               categories: kpiDetails.allData.map((x: KPIData) =>
                  format(new Date(x.date), 'E, dd MMM yyy'),
               ),
               labels: {
                  show: false,
                  style: {
                     colors: isDarkTheme ? ['#FFFFFF'] : ['#000000'],
                  },
               },
            },
            yaxis: {
               labels: {
                  show: false,
                  style: {
                     colors: isDarkTheme ? ['#FFFFFF'] : ['#000000'],
                  },
               },
            },
            stroke: {
               curve: 'smooth',
            },
            dataLabels: {
               enabled: false,
            },
            grid: {
               show: false,
            },
            colors: chartColor ? [chartColor] : undefined,
         },
         series: [
            {
               name: kpiDetails.displayName,
               data: kpiDetails.allData.map((x: KPIData) => x.kpi_value),
            },
         ],
      });
   }, [kpiDetails, isDarkTheme]);

   return (
      <Chart
         options={chartData.options as ApexOptions}
         series={chartData.series}
         type='area'
         width='200'
         height='150'
      />
   );
};

export default LineChart;
