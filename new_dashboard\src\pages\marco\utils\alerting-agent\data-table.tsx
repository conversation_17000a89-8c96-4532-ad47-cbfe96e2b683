import React from 'react';
import {
   Box,
   Table as ChakraTable,
   Thead,
   Tbody,
   Tr,
   Th,
   Td,
   Heading,
} from '@chakra-ui/react';

type DataItem = { [key: string]: string | number };

type TableProps = {
   title: string;
   data: DataItem[];
};

const Table: React.FC<TableProps> = ({ title, data }) => {
   if (data.length === 0) return null;

   const headers = Object.keys(data[0]);

   return (
      <Box
         my={6}
         p={4}
         borderWidth={1}
         borderRadius='lg'
         shadow='sm'
         width='100%'
      >
         <Heading size='md' mb={3}>
            {title}
         </Heading>

         <Box overflowX='auto'>
            <ChakraTable variant='simple' width='100%' minWidth='600px'>
               <Thead bg='gray.200'>
                  <Tr>
                     {headers.map((header) => (
                        <Th
                           key={header}
                           textTransform='uppercase'
                           whiteSpace='nowrap'
                        >
                           {header.replace(/_/g, ' ')}
                        </Th>
                     ))}
                  </Tr>
               </Thead>
               <Tbody>
                  {data.map((row, rowIndex) => (
                     <Tr key={rowIndex}>
                        {headers.map((header) => (
                           <Td key={header} whiteSpace='nowrap'>
                              {row[header]}
                           </Td>
                        ))}
                     </Tr>
                  ))}
               </Tbody>
            </ChakraTable>
         </Box>
      </Box>
   );
};

export default Table;
