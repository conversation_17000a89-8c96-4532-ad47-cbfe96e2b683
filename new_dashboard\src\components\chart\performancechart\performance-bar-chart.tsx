import React from 'react';
import Chart from 'react-apexcharts';
import { PerformanceChartData } from '../../chatbox/interface';
import {
   getFormattedVal,
   getFullMonth,
   getMetricDescription,
   getUserDate,
   isValidDateString,
} from '../../../pages/dashboard/utils/helpers';
import { useColorMode } from '@chakra-ui/react';
const truncate = (str: string) => {
   return str.length > 20 ? str.substring(0, 20) + '...' : str;
};

interface BarChartProps {
   performanceData: PerformanceChartData;
   height?: string | number;
   prevData: PerformanceChartData | undefined;
   theme: 'light' | 'dark';
}

const PerformanceBarChart: React.FC<BarChartProps> = ({
   performanceData,
   height,
   prevData,
   theme,
}) => {
   const { fields } = performanceData.schema;
   const validFields = fields.filter((f) => !f.name.includes('cumulative'));
   const xAxisField = validFields[1].name;
   const yAxisField = validFields[2].name;
   const displayYaxis = getMetricDescription(yAxisField);
   const dataLabelField = validFields[3]
      ? validFields[3].name
      : validFields[2].name;
   const isSegmented = performanceData.compare;
   let series: { name: string; data: { x: string; y: number }[] }[] = [];
   if (isSegmented) {
      validFields.slice(2).forEach((field) => {
         series.push({
            name: field.name,
            data: performanceData.data.map((entry) => ({
               x: String(entry[xAxisField]),
               y: entry[field.name] as number,
            })),
         });
      });
   } else {
      series = [
         {
            name: yAxisField,
            data: performanceData.data.map((entry) => ({
               x: String(entry[xAxisField]),
               y: entry[yAxisField] as number,
               label: entry[dataLabelField] as number,
            })),
         },
      ];
   }
   const categories = [...performanceData.data];
   const group = {
      groups: [] as {
         title: string;
         cols: number;
      }[],
      style: {
         fontSize: '14px',
         fontWeight: 700,
      },
   };
   let title;
   if (prevData) {
      series[0].data.unshift(
         ...prevData.data.map((entry) => ({
            x: String(entry[xAxisField]),
            y: entry[yAxisField] as number,
            label: entry[dataLabelField] as number,
         })),
      );
      categories.unshift(...prevData.data);

      let group1 = getFullMonth(
         prevData.data[0][xAxisField].toString().split('-')[1],
      );

      let group2 = getFullMonth(
         performanceData.data[0][xAxisField].toString().split('-')[1],
      );
      group.groups.push(
         { title: group1, cols: prevData.data.length },
         { title: group2, cols: performanceData.data.length },
      );
      const prevOverall = prevData.average_KPI_values
         ? Object.values(prevData.average_KPI_values[0])[0]
         : '';
      const currOverall = performanceData.average_KPI_values
         ? Object.values(performanceData.average_KPI_values[0])[0]
         : '';
      if (prevOverall)
         group1 += prevOverall
            ? ` (${getFormattedVal(Math.round(Number(prevOverall) * 100) / 100)})`
            : '';
      if (currOverall)
         group2 += currOverall
            ? ` (${getFormattedVal(Math.round(Number(currOverall) * 100) / 100)})`
            : '';
      title = `${performanceData.text.split(')')[0]}) ${group1} v/s ${group2}`;
   }

   const options = {
      chart: {
         id: 'PerformanceChart',
         background: useColorMode().colorMode === 'dark' ? '#1b202d' : '',
         stacked: false,
         toolbar: {
            export: {
               csv: {
                  categoryFormatter: (v: number) => {
                     const value = String(v);
                     const fullLabel = categories.find((data) =>
                        (data[xAxisField] as string).includes(
                           value.slice(0, -3),
                        ),
                     );
                     return fullLabel
                        ? (fullLabel[xAxisField] as string)
                        : value;
                  },
               },
            },
         },
      },
      title: {
         text: title || performanceData.text, // + `${prevData ? ' V/S ' + prevData.text : ''}`
         align: 'center' as const,
         wrap: true,
         overflow: 'auto',
         style: {
            color: useColorMode().colorMode === 'dark' ? '#fff' : '#000',
         },
      },
      subtitle: {},
      xaxis: {
         labels: {
            rotate: -45,
            rotateAlways: true,
            style: {
               colors: useColorMode().colorMode === 'dark' ? '#fff' : '#000',
            },
            formatter: function (value: string) {
               const str = truncate(String(value));
               if (!isValidDateString(str)) return str;
               return getUserDate(new Date(str), false);
            },
         },
         tickAmount: 30,
         group: group,
      },
      yaxis: {
         title: {
            text: isSegmented ? 'count' : displayYaxis,
            style: {
               color: useColorMode().colorMode === 'dark' ? '#fff' : '#000',
            },
         },
         labels: {
            style: {
               colors: useColorMode().colorMode === 'dark' ? '#fff' : '#000',
            },
            formatter: (value: number) => value.toFixed(2),
         },
      },
      tooltip: {
         enabled: true,
         custom: ({
            series,
            seriesIndex,
            dataPointIndex,
         }: {
            series: number[][];
            seriesIndex: number;
            dataPointIndex: number;
         }) => {
            // const fullLabel = performanceData.data[dataPointIndex][
            //    xAxisField
            // ] as string;
            // const dataLabel = performanceData.data[dataPointIndex][
            //    dataLabelField
            // ] as number;
            // const yValue = series[seriesIndex][dataPointIndex];
            // return `<div style="padding: 10px; background-color: #f9f9f9; border: 1px solid #ccc; border-radius: 5px;">
            //         <div style="background-color: #e0e0e0; padding: 5px; border-radius: 3px;">
            //         <strong style="font-size: 14px; color: #333;">${fullLabel}</strong>

            //     </div>
            //       ${dataLabelField}: ${performanceData.currency} ${dataLabel.toFixed(2)}<br/>
            //       ${yAxisField}: ${yValue.toFixed(2)}
            //     </div>`;

            const fullLabel = categories[dataPointIndex][xAxisField] as string;
            const yValue = series[seriesIndex][dataPointIndex];

            if (isSegmented) {
               return `<div style="padding: 5px; background-color: #f9f9f9; border: 1px solid #ccc; border-radius: 5px;">
                        
                        <strong style="background-color: #e0e0e0; padding: 5px; border-radius: 3px;font-size: 14px; color: #333;">${fullLabel}</strong>
                   
                   <span>${validFields[seriesIndex + 2].name}: ${yValue.toFixed(2)}</span>
                    </div>`;
            } else {
               if (yAxisField === dataLabelField) {
                  return `<div style="padding: 5px; background-color: #f9f9f9; border: 1px solid #ccc; border-radius: 5px;">
                        
                        <strong style="background-color: #e0e0e0; padding: 5px; border-radius: 3px;font-size: 14px; color: #333;">${fullLabel}</strong>
                   
                    <span>${yAxisField}: ${yValue.toFixed(2)}</span>
                    </div>`;
               } else {
                  const dataLabel = categories[dataPointIndex][
                     dataLabelField
                  ] as number;
                  return `<div style="padding: 5px; background-color: #f9f9f9; border: 1px solid #ccc; border-radius: 5px;">
                        
                        <strong style="background-color: #e0e0e0; padding: 5px; border-radius: 3px;font-size: 14px; color: #333;">${fullLabel}</strong>
                   
                   <span>${dataLabelField}: ${performanceData.currency} ${dataLabel.toFixed(2)}</span>
                  <span>${yAxisField}: ${yValue.toFixed(2)}</span>
                    </div>`;
               }
            }
         },
      },

      dataLabels: {
         enabled: false,
      },
      legend: {
         labels: {
            colors: useColorMode().colorMode === 'dark' ? '#fff' : '#000',
         },
      },
   };

   const avgOverall = performanceData.average_KPI_values;
   if (avgOverall && avgOverall.length <= 2) {
      if (avgOverall.length == 1 && options.xaxis.group.groups.length < 2) {
         const totalVal = Number(Object.values(avgOverall[0])[0] || 0);
         options.subtitle = {
            text: `Aggregated ${displayYaxis}: ${getFormattedVal(Math.round(totalVal * 100) / 100)}`, // + `${prevData ? ' V/S ' + prevData.text : ''}`
            align: 'center' as const,
            wrap: true,
            overflow: 'auto',
            style: {
               fontSize: '12px',
               fontWeight: 'bolder',
               background: 'red',
               color: useColorMode().colorMode === 'dark' ? '#fff' : '#000',
            },
         };
      }
   }
   return (
      <Chart
         options={options}
         series={series}
         type='bar'
         height={height}
         theme={theme}
      />
   );
};

export default PerformanceBarChart;
