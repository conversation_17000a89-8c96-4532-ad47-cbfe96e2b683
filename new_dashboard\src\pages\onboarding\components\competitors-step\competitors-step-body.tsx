import React, { useState, useRef } from 'react';
import {
   Box,
   Text,
   Button,
   useDisclosure,
   Flex,
   Tooltip,
   useToast,
} from '@chakra-ui/react';
import { EditIcon, DeleteIcon, CheckIcon } from '@chakra-ui/icons';
import { <PERSON><PERSON>, <PERSON>b<PERSON><PERSON>, Tab, TabPanel } from '@chakra-ui/react';
import { TabPanels, Input, Stack, Skeleton } from '@chakra-ui/react';
import {
   AlertDialog,
   AlertDialogBody,
   AlertDialogFooter,
   AlertDialogHeader,
   AlertDialogContent,
   AlertDialogOverlay,
} from '@chakra-ui/react';
import './competitor-step.scss';

import {
   useApiQuery,
   useApiMutation,
} from '../../../../hooks/react-query-hooks';
import { SettingsQueryKeys } from '../../../dashboard/utils/query-keys';
import settingsService, { Competitors } from '../../../../api/service/settings';
import { LocalStorageService, Keys } from '../../../../utils/local-storage';

const CompetitorsStepBody = () => {
   const toast = useToast();
   const { isOpen, onOpen, onClose } = useDisclosure();
   const cancelRef = useRef<HTMLButtonElement>(null);

   const [handle, setHandle] = useState<string>('');
   const [editedHandle, setEditedHandle] = useState<string>('');
   const [isEditing, setIsEditing] = useState<{
      status: boolean;
      key: string;
   }>({
      status: false,
      key: '',
   });

   const { data: instagramData, isFetching: isInstagramFetching } = useApiQuery(
      {
         queryKey: [SettingsQueryKeys.competitors, 'instagramdata'],
         queryFn: () =>
            settingsService.fetchCompetitors({
               client_id: LocalStorageService.getItem(Keys.ClientId) as string,
               channel: 'instagram',
            }),
         enabled: true,
         refetchOnWindowFocus: false,
      },
   );

   const { mutate, isPending } = useApiMutation({
      queryKey: ['updatecompetitors'],
      mutationFn: settingsService.updateCompetitors,
      onError(msg) {
         toast({
            title: 'Error',
            description: msg,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
   });

   const handleAddOrDelete = (
      action: 'add' | 'delete',
      handleValue?: string,
   ) => {
      const data = instagramData as Competitors[];

      switch (action) {
         case 'add': {
            onClose();
            setHandle('');

            mutate({
               client_id: LocalStorageService.getItem(Keys.ClientId) as string,
               channel: 'instagram',
               competitor_handles: [...data, { handle: handle }],
            });
            break;
         }

         case 'delete': {
            const updatedData = data.filter(
               (item) => item.handle !== handleValue,
            );

            mutate({
               client_id: LocalStorageService.getItem(Keys.ClientId) as string,
               channel: 'instagram',
               competitor_handles: [...updatedData],
            });
            break;
         }
      }
   };

   const handleEditChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setEditedHandle(e.target.value);
   };

   const handleEdit = (value: string = '') => {
      setIsEditing({ status: true, key: value });
      setEditedHandle(value);
   };

   const handleSave = () => {
      setIsEditing({ status: false, key: '' });
      setEditedHandle('');

      const data = instagramData as Competitors[];

      const updatedData = data.map((item) => {
         if (item.handle === isEditing.key) {
            return { handle: editedHandle };
         }
         return item;
      });

      mutate({
         client_id: LocalStorageService.getItem(Keys.ClientId) as string,
         channel: 'instagram',
         competitor_handles: [...updatedData],
      });
   };

   const returnListItem = (item: Competitors) => {
      return (
         <Flex
            justifyContent='space-between'
            alignItems='center'
            key={item.handle}
            mt={2}
            gap={4}
         >
            {isEditing.status && isEditing.key === item.handle ? (
               <Input
                  placeholder='Edit handle'
                  fontSize='16px'
                  width='100%'
                  value={editedHandle}
                  onChange={handleEditChange}
               />
            ) : (
               <Text
                  key={item.handle}
                  fontSize='16px'
                  fontWeight='bold'
                  className='panel'
               >
                  {item.handle}
               </Text>
            )}
            <Flex justifyContent='space-between' alignItems='center' gap={3}>
               {isEditing.status && isEditing.key === item.handle ? (
                  <CheckIcon
                     sx={{ cursor: 'pointer' }}
                     onClick={() => handleSave()}
                  />
               ) : (
                  <EditIcon
                     sx={{ cursor: 'pointer' }}
                     onClick={() => handleEdit(item.handle)}
                  />
               )}
               <DeleteIcon
                  sx={{ cursor: 'pointer' }}
                  onClick={() => handleAddOrDelete('delete', item.handle)}
               />
            </Flex>
         </Flex>
      );
   };

   const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const { value } = e.target;
      setHandle(value);
   };

   const handleOpen = () => onOpen();
   return (
      <>
         <Box
            width='60%'
            height='500px'
            marginTop='20px'
            className='social-tabs-container'
         >
            <Tabs defaultIndex={0}>
               <TabList>
                  <Tab name='instagram'>
                     <h4>Instagram</h4>
                  </Tab>
               </TabList>
               <TabPanels>
                  <TabPanel>
                     {isInstagramFetching || isPending ? (
                        <Stack>
                           <Skeleton height='20px' />
                           <Skeleton height='20px' />
                           <Skeleton height='20px' />
                           <Skeleton height='20px' />
                           <Skeleton height='20px' />
                        </Stack>
                     ) : (
                        instagramData &&
                        instagramData?.length > 0 &&
                        instagramData.map((item: Competitors) =>
                           returnListItem(item),
                        )
                     )}
                     <Input
                        placeholder='Write handle'
                        fontSize='16px'
                        width='100%'
                        name='instagram'
                        value={handle}
                        onChange={handleChange}
                        mt={4}
                     />
                     <Tooltip
                        label={
                           instagramData && instagramData?.length >= 5
                              ? 'Only 5 competitors allowed per social media platform.'
                              : ''
                        }
                        placement='right'
                        hasArrow
                        shouldWrapChildren
                     >
                        <Button
                           className='SendSnippepBtn'
                           size='sm'
                           colorScheme='blue'
                           marginTop='5px'
                           onClick={handleOpen}
                           disabled={
                              !handle ||
                              (instagramData && instagramData?.length >= 5)
                           }
                        >
                           Add competitor
                        </Button>
                     </Tooltip>
                  </TabPanel>
               </TabPanels>
            </Tabs>
         </Box>

         <AlertDialog
            isOpen={isOpen}
            leastDestructiveRef={cancelRef}
            onClose={onClose}
         >
            <AlertDialogOverlay>
               <AlertDialogContent>
                  <AlertDialogHeader fontSize='lg' fontWeight='bold'>
                     Do you want to add this account name?
                  </AlertDialogHeader>
                  <AlertDialogBody>
                     Please verify the account name or ID manually before
                     proceeding ahead.Changes will be reflected after 24 hours.
                  </AlertDialogBody>
                  <AlertDialogFooter>
                     <Button ref={cancelRef} onClick={onClose}>
                        No
                     </Button>
                     <Button
                        colorScheme='blue'
                        onClick={() => handleAddOrDelete('add')}
                        ml={3}
                     >
                        Yes
                     </Button>
                  </AlertDialogFooter>
               </AlertDialogContent>
            </AlertDialogOverlay>
         </AlertDialog>
      </>
   );
};

export default CompetitorsStepBody;
