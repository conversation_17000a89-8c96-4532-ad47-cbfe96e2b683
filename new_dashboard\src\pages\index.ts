/* eslint-disable @typescript-eslint/no-var-requires */
/* eslint-disable @typescript-eslint/no-unsafe-return */
// import { lazy } from 'react';

// export const Login = lazy(() => import('./login/login'));

// export const MarcoChat = lazy(() => import('./marco/marco-chat'));

// export const SocialListening = lazy(
//    () => import('./social-listening/social-listening'),
// );
// export const SocialWatch = lazy(() => import('./socialwatch/social-watch'));
// export const ContentCalender = lazy(() => import('./contentcalender'));
// export const Pulse = lazy(() => import('./pulse'));
// export const Performance = lazy(() => import('./pulse/performance-insights'));
// export const Overview = lazy(
//    () => import('./pulse/performance-insights/overview'),
// );
// export const Settings = lazy(() => import('./settings/index'));

// TODO: Removing lazy loading for the time being, as we are getting path issue in deployment: Will fix this later
import Login from './auth/login';
import Register from './auth/register';
import MarcoLayout from './marco/marco-layout';
import SocialListening from './social-listening/social-listening';
import SocialWatch from './socialwatch/social-watch';
import ContentCalender from './contentcalender';
import Pulse from './pulse';
import Performance from './pulse/performance-insights';
import Overview from './pulse/performance-insights/overview';
import Settings from './settings/index';
import Onboarding from './onboarding/onboarding';

export {
   Login,
   MarcoLayout,
   SocialListening,
   SocialWatch,
   ContentCalender,
   Pulse,
   Performance,
   Overview,
   Settings,
   Register,
   Onboarding,
};
