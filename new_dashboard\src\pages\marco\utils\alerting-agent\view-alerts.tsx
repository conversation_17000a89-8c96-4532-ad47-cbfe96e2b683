import {
   Box,
   Checkbox,
   Flex,
   Icon,
   IconButton,
   Image,
   Input,
   Skeleton,
   SkeletonText,
   Table,
   TableContainer,
   Tag,
   TagLabel,
   Tbody,
   Td,
   Text,
   Th,
   Thead,
   Tooltip,
   Tr,
   useDisclosure,
} from '@chakra-ui/react';
import { Keys, LocalStorageService } from '../../../../utils/local-storage';
import { AuthUser } from '../../../../types/auth';
import {
   AlertingAgentAlert,
   AlertingAgentChatMetaData,
} from '../../../../api/service/agentic-workflow/alerting-agent';
import { useNavigate } from 'react-router-dom';
import ShopifyImage from '../../../../assets/icons/kpi/shopify.png';
import MetaImage from '../../../../assets/icons/kpi/meta.png';
import WebImage from '../../../../assets/icons/kpi/web.png';
import GAdImage from '../../../../assets/icons/kpi/gads.png';
import ASPImage from '../../../../assets/icons/kpi/amazon-seller.png';
import AAdsImage from '../../../../assets/icons/kpi/amazon-ads.png';
import {
   ArrowDownIcon,
   ArrowUpIcon,
   ChevronLeftIcon,
   ChevronRightIcon,
} from '@chakra-ui/icons';
import { MdOutlineModeEditOutline } from 'react-icons/md';
import { RiDeleteBinLine } from 'react-icons/ri';
import { splitAndUppercaseString } from './agents-helpers';
import { useAppDispatch } from '../../../../store/store';
import { openModal } from '../../../../store/reducer/modal-reducer';
import { modalTypes } from '../../../../components/modals/modal-types';
import DeleteConfirmation from './delete-confirmation';
import { useEffect, useState } from 'react';
import {
   useDeleteAlertMutation,
   useDeleteMultipleAlertsMutation,
   useFetchAllAlertsQuery,
   useFetchChatByChatIdQuery,
} from '../../apis/alerting-agent-apis';
import { Button } from '@/components/ui/button';
import {
   setCurrentAlertID,
   setCurrentChatID,
   setCurrentSessionID,
} from '../../../../store/reducer/alerting-agent-reducer';
import { LuArrowDownUp, LuArrowUp, LuArrowDown } from 'react-icons/lu';

const CHANNELS = {
   googleads: {
      option: 'Google Ads',
      icon: GAdImage,
   },
   facebookads: {
      option: 'Meta Ads',
      icon: MetaImage,
   },
   web: {
      option: 'Web',
      icon: WebImage,
   },
   store: {
      option: 'Shopify',
      icon: ShopifyImage,
   },
   amazon_selling_partner: {
      option: 'Amazon Selling Partner',
      icon: ASPImage,
   },
   amazon_ads: {
      option: 'Amazon Ads',
      icon: AAdsImage,
   },
};

const TRENDS = {
   Decreasing: {
      option: 'Decreasing',
      icon: ArrowDownIcon,
      color: '#B42318',
      background: '#FEF3F2',
   },
   Increasing: {
      option: 'Increasing',
      icon: ArrowUpIcon,
      color: '#027A48',
      background: '#ECFDF3',
   },
   More_than: {
      option: 'More than',
      icon: ChevronRightIcon,
      color: '#027A48',
      background: '#ECFDF3',
   },
   Less_than: {
      option: 'Less than',
      icon: ChevronLeftIcon,
      color: '#B42318',
      background: '#FEF3F2',
   },
};

const ALERT_STATUS = {
   ACTIVE: {
      option: 'Active',
      color: '#027A48',
      background: '#ECFDF3',
   },
   PAUSED: {
      option: 'Paused',
      color: '#B42318',
      background: '#FEF3F2',
   },
};

interface SortableThProps {
   columnKey: string;
   secondaryKey?: string;
   label: string;
   align?: 'start' | 'center';
}

const ViewAlerts = () => {
   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) ?? {};

   const navigate = useNavigate();
   const dispatch = useAppDispatch();

   const { isOpen, onOpen, onClose } = useDisclosure();

   const [alerts, setAlerts] = useState<AlertingAgentAlert[]>([]);
   const [currentAlert, setCurrentAlert] = useState<AlertingAgentAlert | null>(
      null,
   );
   const [searchAlert, setSearchAlert] = useState<string>('');
   const [selectedAlerts, setSelectedAlerts] = useState<number[]>([]);
   const [sortDetails, setSortDetails] = useState<{
      column: string | null;
      order: string | null;
   }>({
      column: null,
      order: null,
   });

   const {
      data: allAlerts,
      isFetching: isAllAlertsFetching,
      refetch: refetchAllAlerts,
   } = useFetchAllAlertsQuery();

   const { refetch: refetchCurrentChat } = useFetchChatByChatIdQuery();

   const { mutateAsync: deleteAlert } = useDeleteAlertMutation();

   const { mutateAsync: deleteMultipleAlerts } =
      useDeleteMultipleAlertsMutation();

   const handleEdit = async (alert: AlertingAgentAlert) => {
      await Promise.all([
         dispatch(setCurrentAlertID(String(alert.alert_id))),
         dispatch(setCurrentSessionID(alert.session_id)),
         dispatch(setCurrentChatID(alert.chat_id)),
      ]);

      const { data: currentChat } = await refetchCurrentChat();

      if (currentChat) {
         const chat_meta_data =
            (JSON.parse(
               currentChat?.final_response,
            ) as AlertingAgentChatMetaData) || {};

         dispatch(
            openModal({
               modalType: modalTypes.EDIT_ALERT_MODAL,
               modalProps: {
                  alert_setup_status: 'success',
                  alert_id: String(alert.alert_id),
                  currentChat: currentChat,
                  meta_data: chat_meta_data.meta_data,
               },
            }),
         );
      }
   };

   const handleDeleteCancel = () => {
      onClose();
      setCurrentAlert(null);
   };

   const handleDelete = (alert: AlertingAgentAlert) => {
      onOpen();
      setCurrentAlert(alert);
   };

   const handleDeleteConfirmation = async () => {
      onClose();

      const payload = {
         client_id: client_id || '',
         user_id: user_id || '',
         alert_id: String(currentAlert?.alert_id) || '',
      };

      await deleteAlert(payload);
      await refetchAllAlerts();
      dispatch(setCurrentAlertID(''));
      dispatch(setCurrentChatID(''));
      setCurrentAlert(null);
      setSelectedAlerts([]);
   };

   const handleMultipleDelete = () => {
      onOpen();
   };

   const handleMultipleDeleteConfirmation = async () => {
      onClose();

      const payload = {
         client_id: client_id || '',
         user_id: user_id || '',
         alert_ids: [...selectedAlerts],
      };

      await deleteMultipleAlerts(payload);
      await refetchAllAlerts();
      setSelectedAlerts([]);
   };

   const handleAddAlert = () => {
      dispatch(setCurrentAlertID(''));
      dispatch(setCurrentChatID(''));
      dispatch(setCurrentSessionID(''));
      navigate('/marco/alerting-agent');
   };

   const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchAlert(e.target.value);
   };

   const handleCheckboxChange = (alert_id: number) => {
      if (selectedAlerts.includes(alert_id)) {
         setSelectedAlerts(selectedAlerts.filter((id) => id !== alert_id));
      } else {
         setSelectedAlerts([...selectedAlerts, alert_id]);
      }
   };

   const handleOverallCheckboxChange = (
      e: React.ChangeEvent<HTMLInputElement>,
   ) => {
      if (e.target.checked && alerts) {
         setSelectedAlerts(alerts?.map((alert) => Number(alert.alert_id)));
      } else {
         setSelectedAlerts([]);
      }
   };

   const filterAlert = (alerts: AlertingAgentAlert[]) => {
      return alerts?.filter(
         (alert: AlertingAgentAlert) =>
            alert?.alert_name
               .toLowerCase()
               .includes(searchAlert.toLowerCase()) ||
            alert?.channel.toLowerCase().includes(searchAlert.toLowerCase()) ||
            alert?.kpi.toLowerCase().includes(searchAlert.toLowerCase()) ||
            alert?.trend.toLowerCase().includes(searchAlert.toLowerCase()) ||
            alert?.value.toLowerCase().includes(searchAlert.toLowerCase()),
      );
   };

   const sortData = (
      alerts: AlertingAgentAlert[],
      column: keyof AlertingAgentAlert,
      order: 'asc' | 'desc',
   ) => {
      return [...alerts].sort((a, b) => {
         const aVal = (a[column] ?? '').toString().toLowerCase();
         const bVal = (b[column] ?? '').toString().toLowerCase();

         if (aVal < bVal) return order === 'asc' ? -1 : 1;
         if (aVal > bVal) return order === 'asc' ? 1 : -1;
         return 0;
      });
   };

   const handleSort = (column: keyof AlertingAgentAlert) => {
      if (sortDetails.column === column) {
         const newSortOrder = sortDetails.order === 'asc' ? 'desc' : 'asc';

         setSortDetails({
            column,
            order: newSortOrder,
         });

         column = column.includes('value') ? 'value' : column;
         const sortedData = sortData(alerts, column, newSortOrder);
         setAlerts(sortedData);
      } else {
         setSortDetails({ column, order: 'asc' });

         column = column.includes('value') ? 'value' : column;
         const sortedData = sortData(alerts, column, 'asc');
         setAlerts(sortedData);
      }
   };

   const SortableTh = ({
      columnKey,
      label,
      align = 'start',
   }: SortableThProps): JSX.Element => {
      const isActive = sortDetails.column === columnKey;
      const arrowIcon = isActive ? (
         sortDetails.order === 'asc' ? (
            <LuArrowUp />
         ) : (
            <LuArrowDown />
         )
      ) : (
         <Box
            opacity={0}
            transition='opacity 0.2s ease'
            _groupHover={{ opacity: 1 }}
            ml='4px'
            className='filter-arrow'
         >
            <LuArrowDownUp />
         </Box>
      );

      return (
         <Th
            fontFamily='Nunito Sans'
            height='40px'
            position='sticky'
            cursor='pointer'
            onClick={() => handleSort(columnKey as keyof AlertingAgentAlert)}
            _hover={{ '.filter-arrow': { opacity: 1 } }}
         >
            <Box display='flex' alignItems='center' justifyContent={align}>
               {label} {arrowIcon}
            </Box>
         </Th>
      );
   };

   useEffect(() => {
      if (allAlerts) {
         setAlerts(
            allAlerts.sort((a, b) => Number(b.alert_id) - Number(a.alert_id)),
         );
      }
   }, [allAlerts]);

   return (
      <>
         <div className='w-full h-full flex flex-col items-start py-[10px] px-[50px] transition-all duration-300 ease-in-out gap-5 overflow-hidden'>
            <div className='w-full h-full flex flex-col'>
               <div className='flex items-center justify-between p-[15px_30px] gap-2'>
                  {alerts ? (
                     <p className='w-full font-semibold text-[18px] text-left'>
                        {`You have set up ${alerts?.length} alert${
                           alerts?.length > 1 ? 's' : ''
                        }`}
                     </p>
                  ) : (
                     <Skeleton height='10px' width='120px' />
                  )}
                  {selectedAlerts.length > 0 && (
                     <Button
                        className='w-[210px] bg-red-600'
                        onClick={() => handleMultipleDelete()}
                     >
                        Delete Selected
                     </Button>
                  )}
                  <Input
                     name='filter_alert'
                     width='270px'
                     placeholder='Search Alert'
                     fontSize='14px'
                     value={searchAlert}
                     onChange={handleInputChange}
                  />
                  <Button
                     className='bg-blue-800 hover:cursor-pointer w-[150px] hover:bg-blue-900'
                     onClick={() => handleAddAlert()}
                  >
                     Add Alert
                  </Button>
               </div>
               <TableContainer
                  width='100%'
                  height='86%'
                  boxShadow='0 0 4px rgba(0, 0, 0, 0.1)'
                  borderRadius='lg'
                  className='table-container'
                  overflowY='auto'
                  position='relative'
               >
                  <Table variant='simple' size='sm'>
                     <Thead
                        background='#f2f2f2'
                        position='sticky'
                        top={0}
                        zIndex={10}
                     >
                        <Tr>
                           <Th>
                              <Checkbox
                                 margin={2}
                                 borderColor='blackAlpha.600'
                                 disabled={alerts?.length === 0}
                                 isChecked={
                                    selectedAlerts.length === alerts?.length
                                 }
                                 onChange={handleOverallCheckboxChange}
                              ></Checkbox>
                           </Th>
                           <Th
                              fontFamily='Nunito Sans'
                              position='sticky'
                              textAlign='center'
                           >
                              Actions
                           </Th>
                           <SortableTh
                              columnKey='alert_name'
                              label='Alert Name'
                              align='start'
                           />
                           <SortableTh
                              columnKey='channel'
                              label='Data Source'
                              align='start'
                           />
                           <SortableTh
                              columnKey='campaigns'
                              label='Campaigns'
                              align='start'
                           />
                           <SortableTh
                              columnKey='kpi'
                              label='KPI'
                              align='center'
                           />
                           <SortableTh
                              columnKey='trend'
                              label='Trend'
                              align='center'
                           />
                           <SortableTh
                              columnKey='value-per'
                              label='By Percentage'
                              align='center'
                           />
                           <SortableTh
                              columnKey='value-num'
                              label='By Number'
                              align='center'
                           />
                           <SortableTh
                              columnKey='comparison'
                              label='Comparison'
                              align='center'
                           />
                           <SortableTh
                              columnKey='alert_status'
                              label='Alert Status'
                              align='center'
                           />
                        </Tr>
                     </Thead>
                     <Tbody>
                        {isAllAlertsFetching ? (
                           <>
                              {Array.from({ length: 5 }).map((_, rowIndex) => (
                                 <Tr key={rowIndex}>
                                    {Array.from({ length: 10 }).map(
                                       (_, cellIndex) => (
                                          <Td key={cellIndex}>
                                             <SkeletonText noOfLines={1} />
                                          </Td>
                                       ),
                                    )}
                                 </Tr>
                              ))}
                           </>
                        ) : (
                           filterAlert(alerts).map((alert) => (
                              <Tr
                                 key={alert?.alert_id}
                                 background={
                                    selectedAlerts.includes(
                                       Number(alert?.alert_id),
                                    )
                                       ? '#D0E8FF'
                                       : ''
                                 }
                              >
                                 <Td textAlign='center'>
                                    <Checkbox
                                       margin={2}
                                       isChecked={selectedAlerts.includes(
                                          Number(alert?.alert_id),
                                       )}
                                       borderColor='blackAlpha.600'
                                       onChange={() =>
                                          handleCheckboxChange(
                                             Number(alert?.alert_id),
                                          )
                                       }
                                    ></Checkbox>
                                 </Td>
                                 <Td textAlign='center'>
                                    <Flex
                                       alignItems='center'
                                       justifyContent='center'
                                    >
                                       <Tooltip label='Edit' hasArrow>
                                          <IconButton
                                             size='sm'
                                             _hover={{ cursor: 'pointer' }}
                                             aria-label='Edit'
                                             icon={<MdOutlineModeEditOutline />}
                                             background='none'
                                             onClick={() =>
                                                void handleEdit(alert)
                                             }
                                          />
                                       </Tooltip>
                                       <Tooltip label='Delete' hasArrow>
                                          <IconButton
                                             size='sm'
                                             _hover={{ cursor: 'pointer' }}
                                             aria-label='Delete'
                                             icon={<RiDeleteBinLine />}
                                             background='none'
                                             onClick={() => handleDelete(alert)}
                                          />
                                       </Tooltip>
                                    </Flex>
                                 </Td>
                                 <Td textAlign='left'>
                                    <Tooltip hasArrow label={alert?.alert_name}>
                                       {alert?.alert_name.length > 15
                                          ? alert?.alert_name.slice(0, 15) +
                                            '...'
                                          : alert?.alert_name || '-'}
                                    </Tooltip>
                                 </Td>
                                 <Td className='t-data' textAlign='left'>
                                    <Flex
                                       width='100%'
                                       alignItems='center'
                                       gap={2}
                                    >
                                       <Image
                                          width={6}
                                          src={
                                             CHANNELS[
                                                alert?.channel as keyof typeof CHANNELS
                                             ]?.icon
                                          }
                                       />
                                       <Text>
                                          {
                                             CHANNELS[
                                                alert?.channel as keyof typeof CHANNELS
                                             ]?.option
                                          }
                                       </Text>
                                    </Flex>
                                 </Td>
                                 <Td textAlign='center'>
                                    <Tooltip
                                       label={
                                          (alert.campaigns &&
                                             alert?.campaigns
                                                .map((c) => c.name)
                                                .join(', ')) ||
                                          ''
                                       }
                                       hasArrow
                                    >
                                       {alert.campaigns &&
                                          ((alert?.campaigns.length > 0 &&
                                             alert?.campaigns
                                                .map((c) => c.name)
                                                .join(', ')
                                                .slice(0, 12) + '...') ||
                                             '')}
                                    </Tooltip>
                                 </Td>
                                 <Td textAlign='center'>
                                    <Tooltip
                                       label={splitAndUppercaseString(
                                          alert?.kpi,
                                       )}
                                       hasArrow
                                    >
                                       {alert?.kpi.length > 12
                                          ? splitAndUppercaseString(
                                               alert?.kpi,
                                            ).slice(0, 12) + '...'
                                          : splitAndUppercaseString(
                                               alert?.kpi,
                                            ) || '-'}
                                    </Tooltip>
                                 </Td>
                                 <Td textAlign='center'>
                                    <Tag
                                       size='sm'
                                       borderRadius='full'
                                       variant='solid'
                                       background={
                                          TRENDS[
                                             alert?.trend as keyof typeof TRENDS
                                          ]?.background
                                       }
                                       color={
                                          TRENDS[
                                             alert?.trend as keyof typeof TRENDS
                                          ]?.color
                                       }
                                       gap={1}
                                    >
                                       <Icon
                                          as={
                                             TRENDS[
                                                alert?.trend as keyof typeof TRENDS
                                             ]?.icon
                                          }
                                       />
                                       <TagLabel>
                                          {
                                             TRENDS[
                                                alert?.trend as keyof typeof TRENDS
                                             ]?.option
                                          }
                                       </TagLabel>
                                    </Tag>
                                 </Td>
                                 <Td textAlign='center'>
                                    {alert?.value_type === 'percentage'
                                       ? `${alert?.value}%`
                                       : '-'}
                                 </Td>
                                 <Td textAlign='center'>
                                    {alert?.value_type === 'number'
                                       ? alert?.value
                                       : '-'}
                                 </Td>
                                 <Td textAlign='center'>
                                    {alert?.comparison
                                       ? splitAndUppercaseString(
                                            alert?.comparison,
                                         )
                                       : '-'}
                                 </Td>
                                 <Td textAlign='center'>
                                    <Tag
                                       size='sm'
                                       borderRadius='full'
                                       variant='solid'
                                       background={
                                          ALERT_STATUS[
                                             alert?.alert_status as keyof typeof ALERT_STATUS
                                          ]?.background
                                       }
                                       color={
                                          ALERT_STATUS[
                                             alert?.alert_status as keyof typeof ALERT_STATUS
                                          ]?.color
                                       }
                                       gap={1}
                                    >
                                       <TagLabel>
                                          {
                                             ALERT_STATUS[
                                                alert?.alert_status as keyof typeof ALERT_STATUS
                                             ]?.option
                                          }
                                       </TagLabel>
                                    </Tag>
                                 </Td>
                              </Tr>
                           ))
                        )}
                     </Tbody>
                  </Table>
               </TableContainer>
            </div>
         </div>
         <DeleteConfirmation
            isOpen={isOpen}
            onClose={handleDeleteCancel}
            onConfirm={
               currentAlert
                  ? () => void handleDeleteConfirmation()
                  : () => void handleMultipleDeleteConfirmation()
            }
         />
      </>
   );
};

export default ViewAlerts;
