import { Notification } from '@/api/service/notifications';
import { PayloadAction, createSlice } from '@reduxjs/toolkit';

interface InitialState {
   notifications: Notification[];
}

const initialState: InitialState = {
   notifications: [],
};

const notificationsSlice = createSlice({
   name: 'notifications',
   initialState,
   reducers: {
      setNotifications: (state, action: PayloadAction<Notification[]>) => {
         state.notifications = action.payload;
      },
   },
});

export const { setNotifications } = notificationsSlice.actions;

export default notificationsSlice.reducer;
