@use '../../../sass/variable.scss';
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500&display=swap');

.popup-overlay {
   position: fixed;
   top: 0;
   left: 0;
   width: 100%;
   height: 100%;
   background: rgba(0, 0, 0, 0.5);
   display: flex;
   justify-content: center;
   align-items: center;
   z-index: 1000;

   .popup-content {
      background: #ffffff;
      padding: 20px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      width: 80%;
      max-height: 90%;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      box-sizing: border-box;
      padding-top: 0px;

      scrollbar-width: thin;
      scrollbar-color: #ccc transparent;

      &::-webkit-scrollbar {
         width: 6px;

         // [data-theme='dark'] & {
         //    background-color: $controls;
         // }
      }

      ::-webkit-scrollbar-thumb {
         background-color: #ccc;
         border-radius: 10px;
         border: 2px solid #e0e0e0;

         // [data-theme='dark'] & {
         //    background-color: $controls;
         // }
      }

      &::-webkit-scrollbar-thumb:hover {
         background-color: #888;

         // [data-theme='dark'] & {
         //    background-color: $controls-hover;
         // }
      }

      &::-webkit-scrollbar-track {
         background: transparent;

         // [data-theme='dark'] & {
         //    background-color: $controls;
         // }
      }

      &::-webkit-scrollbar-thumb:window-inactive {
         visibility: hidden;
      }

      .heading-bs {
         font-weight: 600;
         font-size: 18px;
         font-family: 'Poppins';

         margin-top: 10px;
         margin-bottom: 10px;
      }

      .recommendations {
         padding-left: 10px;
         margin: 10px 0;

         h3 {
            font-weight: 500;
            font-size: 18px;
            font-family: 'Poppins';
         }

         ul {
            background-color: #e3e2ff36;
            padding: 10px;
            padding-left: 25px;
            border: 1px solid #e3e2ff36;
            border-radius: 10px;
            margin-top: 10px;
            margin-right: 10px;
            font-size: 14px;
            font-family: inherit;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
            list-style: circle;
         }
      }

      .industry-ben {
         margin-top: 10px;
         margin-bottom: 10px;

         & > h3 {
            font-weight: 600;
            font-size: 18px;
            font-family: 'Poppins';
            margin-bottom: 10px;
         }

         .recommendations {
            padding-left: 0px;
         }
      }

      .heading {
         position: fixed;
         display: flex;
         justify-content: space-between;
         align-items: center;
         margin-bottom: 20px;
         z-index: 1000;
         background-color: #ffffff;
         width: 78%;

         padding-top: 10px;

         h3 {
            font-weight: 600;
            font-size: 20px;
            font-family: 'Poppins';
         }

         .close-button {
            cursor: pointer;
            font-weight: 600;
            font-size: 25px;
            background: none;
            border: none;
         }
      }

      .divider {
         margin-top: 60px;
      }

      .top {
         display: flex;
         gap: 5px;
         margin-bottom: 20px;
         margin-top: 20px;
         width: 100%;

         button {
            padding: 6px 10px;
            border: none;
            border-radius: 5px;
            background-color: #e3e2ff;
            color: blue;
            cursor: pointer;
            font-size: 12px;
         }
      }

      .spend-budget {
         width: 100%;
         height: 500px;
         padding-top: 15px;
         position: relative;

         .selects {
            position: absolute;
            right: 90px;
            top: 0px;
            width: fit-content;
         }

         // .spendBudgetSelect {
         //    [data-theme='dark'] & {
         //       background-color: $controls;
         //    }
         // }
      }

      .table-header {
         display: flex;
         align-items: center;

         font-family: 'Poppins';
         padding-right: 18px;
      }

      .details {
         margin-bottom: 20px;
         border: 1px solid #ddd;
         border-radius: 10px;
         padding: 20px 10px;

         .campaign-insights-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
         }

         .insight-icon {
            width: 20px;
            height: 20px;
            margin-right: 8px;
            margin-bottom: 10px;
         }

         .headers {
            background-color: #f6f5ff;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;

            h4 {
               font-weight: 500;
               font-size: 16px;
               font-family: 'Poppins';
               margin-bottom: 5px;
            }

            p {
               font-size: 14px;
               margin-top: 5px;
            }

            .line {
               height: 1px;
               background-color: #ccc;
               margin: 10px 0;
            }

            .see-more {
               color: rgb(0, 149, 255);
               cursor: pointer;
            }
         }
      }

      .overview {
         margin-bottom: 20px;
         display: flex;
         flex-direction: column;
         width: 100%;
         margin-left: 0px;
         padding-left: 0px;

         h3 {
            font-weight: 600;
            font-size: 18px;
            font-family: 'Poppins';
            margin-bottom: 10px;
         }

         .insights {
            .insights-table {
               width: 100%;
               border-collapse: collapse;

               th,
               td {
                  padding: 8px;
                  border-bottom: 1px solid #ddd;
                  text-align: left;
                  font-family: 'Poppins';
               }

               th {
                  font-weight: lighter;
                  font-size: 16px;
               }

               td {
                  font-weight: 300;
                  font-size: 24px;

                  span {
                     font-size: 12px;
                     margin-left: 5px;
                     font-weight: bold;
                  }
               }
            }
         }
      }

      .keyword-section {
         margin-bottom: 20px;

         .keywords-header {
            display: flex;
            align-items: center;
            justify-content: space-between;

            h3 {
               font-weight: 600;
               font-size: 18px;
               font-family: 'Poppins';
               margin-bottom: 10px;
            }
         }

         .keyword-overview {
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 10px;

            .keyword-content {
               max-height: 410px;
               overflow-x: auto;
               overflow-y: auto;
               box-sizing: border-box;
               scrollbar-width: thin;
               scrollbar-color: #ccc transparent;

               &::-webkit-scrollbar {
                  width: 6px;

                  // [data-theme='dark'] & {
                  //    background-color: $controls;
                  // }
               }

               ::-webkit-scrollbar-thumb {
                  background-color: #ccc;
                  border-radius: 10px;
                  border: 2px solid #e0e0e0;

                  // [data-theme='dark'] & {
                  //    background-color: $controls;
                  // }
               }

               &::-webkit-scrollbar-thumb:hover {
                  background-color: #888;

                  // [data-theme='dark'] & {
                  //    background-color: $controls-hover;
                  // }
               }

               &::-webkit-scrollbar-track {
                  background: transparent;

                  // [data-theme='dark'] & {
                  //    background-color: $controls;
                  // }
               }

               &::-webkit-scrollbar-thumb:window-inactive {
                  visibility: hidden;
               }

               .keyword-table {
                  width: 100%;

                  .table-head {
                     border-bottom: 1px solid #ddd;
                     text-align: left;

                     .head-col {
                        padding: 20px 5px;
                        font-weight: 500;
                        font-size: 18px;
                        font-family: 'Poppins';
                        text-align: left;
                        cursor: pointer;
                        position: relative;

                        &.heading-bs {
                           min-width: 300px;
                        }

                        &:hover {
                           .filter-arrow {
                              opacity: 1;
                           }
                        }

                        .inputfield {
                           min-height: 45px;
                           max-width: 370px;
                           min-width: 370px;

                           .selctedoptions {
                              padding-top: 2px;
                              max-width: 300px;
                              max-height: 27px;
                              overflow: hidden;

                              .matchnamesbox {
                                 background-color: #e4f2ff;
                                 color: #3c76e1;

                                 .matchnames {
                                    padding-top: 2px;
                                    font-size: 14px;
                                 }
                              }
                           }

                           .buttons {
                              padding-top: 4px;

                              .close-buttion {
                                 padding-right: 10px;
                                 border-radius: 0px;
                                 border-right: 1px solid #c2c2c2;
                              }

                              .drop-down-icon {
                                 background: none !important;
                              }

                              .css-ktd6ms {
                                 position: absolute !important;
                                 left: -90px !important;

                                 button {
                                    background: none !important;
                                 }
                              }
                           }
                        }
                     }

                     .filter-arrow {
                        opacity: 0;
                        transition: opacity 0.2s ease;
                     }

                     .kpiname {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 16px !important;
                     }
                  }

                  .table-body {
                     .body-row {
                        border-bottom: 1px solid #ddd;
                        margin: 3px 10px;
                        min-width: 350px;

                        &.search-terms {
                           border-bottom: 0px solid #ddd !important;

                           .search-term-name {
                              padding: 5px 5px 5px 35px !important;
                              text-align: left;
                              font-size: 15px;
                           }

                           .search-terms {
                              text-align: left;
                              padding: 12px 5px 12px 25px !important;
                              font-size: 15px;
                              color: var(--chakra-colors-blue-600);

                              &.search-term-kpi {
                                 text-align: center !important;
                                 color: #000;
                                 padding-left: 5px !important;

                                 [data-theme='dark'] & {
                                    color: #fff;
                                 }
                              }
                           }
                        }

                        .body-col {
                           padding: 15px 5px;
                           text-align: center;
                           font-weight: 400;
                           font-size: 16px;
                           font-family: 'Poppins';

                           &.keyword-name {
                              text-align: left !important;
                           }

                           &.keyword-terms {
                              text-align: left;
                              padding-left: 15px !important;
                              color: var(--chakra-colors-blue-600);
                           }

                           .keywords {
                              display: flex;
                              align-items: center;
                              gap: 5px;
                              width: 350px;

                              .keywords-name {
                                 text-decoration: underline;
                                 color: blue;
                                 font-size: 16px;
                                 font-family: 'Poppins';
                                 text-align: left !important;

                                 [data-theme='dark'] & {
                                    color: #437eeb;
                                 }
                              }
                           }
                        }
                     }

                     .body-row:last-child {
                        border-bottom: none;
                     }

                     .search-term-row {
                        .search-term-name {
                           padding-left: 50px !important;
                           text-align: left !important;
                        }

                        .serach-term-col {
                           padding: 10px 6px;
                           text-align: center;
                           font-weight: 400;
                           font-size: 14px;
                           font-family: 'Poppins';
                        }
                     }

                     .no-data-col {
                        text-align: center;
                        padding: 140px 0px;
                     }
                  }
               }
            }
         }
      }

      .ad-set {
         h3 {
            font-weight: 600;
            font-size: 18px;
            font-family: 'Poppins';
            margin-bottom: 10px;
         }
      }

      .adset-overview,
      .industry-benchmark {
         border: 1px solid #ddd;
         border-radius: 10px;
         padding: 20px;
         margin-bottom: 20px;
         overflow-x: auto;

         .adset-table {
            width: 100%;
            border-collapse: collapse;

            th,
            td {
               padding: 8px;

               border-bottom: 1px solid #ddd;
               text-align: left;
               font-family: 'Poppins';
            }

            th {
               font-weight: 500;
               font-size: 16px;
            }

            td {
               font-weight: 300;
               font-size: 14px;
               gap: 5px;

               span {
                  font-size: 12px;
                  margin-left: 5px;
                  font-weight: bold;
               }
            }

            .ads {
               max-width: 250px;
            }

            .links {
               text-decoration: underline;
               color: blue;
               font-size: 16px;
               font-family: 'Poppins';

               [data-theme='dark'] & {
                  color: #437eeb;
               }
            }

            .view-more {
               font-size: 12px;
               color: blue;
               font-weight: 500;

               [data-theme='dark'] & {
                  color: #437eeb;
               }
            }

            .adset-kpis {
               min-width: 70px;
            }
         }

         .adset-table td:last-child {
            max-width: 350px;
         }

         .recommendations-title {
            font-weight: 500;
            font-size: 18px;
            font-family: 'Poppins';
            display: flex;
            align-items: center;
         }

         .recommendations {
            padding-left: 0px;
         }
      }

      // Dark theme styles
      [data-theme='dark'] & {
         // background-color: $background_surface;
         color: #fff;

         // .heading {
         //    background-color: $background_surface;
         // }

         // .recommendations {
         //    background-color: $background_surface;
         // }

         .details {
            // background-color: $background_surface;
            border: 1px solid #666;
         }

         // .overview {
         // background-color: $background_surface;
         // }

         .adset-overview,
         .industry-benchmark {
            // background-color: $background_surface;
            border: 1px solid #666;
         }
      }
   }
}
