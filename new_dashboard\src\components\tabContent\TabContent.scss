@use '../../sass/variable.scss';
.text-area {
   width: -webkit-fill-available;
   height: 10rem;
   padding: 10px;
   border: 1px solid #a0a4a838;
   border-radius: 5px;
   font-size: 16px;
   resize: none;
   outline: none;
   font-family: 'Roboto', sans-serif;
   color: #333;
   background-color: #f9f9f9;
   transition: all 0.3s ease;
   &:focus {
      border: 1px solid #007bff;
      background-color: #fff;
   }
   // [data-theme='dark'] & {
   //    background-color: $background;
   // }
}
.img-load {
   position: absolute;
   width: 100%;
   height: 80%;
   display: flex;
   top: 10px;
   justify-content: center;
   align-items: center;
   z-index: 1;
   background: #fafafa22;
}
