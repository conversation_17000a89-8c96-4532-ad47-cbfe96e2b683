// import main.scss
@use './sass/variable.scss';

// html {
//    border-color: black !important;
// }

// h1 {
//    color: $primary_color;
// }

.chakra-modal__content {
   margin-top: 10% !important;
}
/* Remove extra spacing from the top and bottom of the arrow */
.introjs-arrow {
   top: 0 !important; /* Removes the extra space above */
   bottom: 0 !important; /* Removes the extra space below */
   border-bottom-color: transparent !important; /* If the arrow is still visible, hide it */
}
.introjs-arrow.left {
   left: 0 !important;
}
.introjs-arrow.left-bottom,
.introjs-arrow.right-bottom {
   left: 0 !important;
}

.introjs-tooltip {
   padding: 10px !important; /* You can decrease the padding if there's extra space inside */
}
.introjs-skipbutton {
   cursor: pointer !important; /* Show pointer on hover */
   color: #000; /* Set close button color */
   position: absolute;
   top: 10px;
   right: 10px;
   z-index: 9999; /* Ensure it stays on top */
}
// .introjs-tooltip {
//    [data-theme='dark'] & {
//       background-color: $controls;
//       color: $text_color;
//       border-color: $background_surface;
//    }
// }
.introjs-helperLayer {
   transition: none !important;
}
.introjs-showElement .introjs-relativePosition {
   [data-theme='dark'] & {
      background-color: 'red' !important;
   }
}
// .apexcharts-menu {
//    [data-theme='dark'] & {
//       background-color: $controls;
//       color: $text_color;
//       border-color: $background_surface;
//    }
// }
// .apexcharts-tooltip.apexcharts-theme-light,
// .apexcharts-tooltip.apexcharts-theme-light .apexcharts-tooltip-title,
// .apexcharts-yaxis {
//    [data-theme='dark'] & {
//       background-color: $controls;
//       color: $text_color;
//       border-color: $background_surface;
//    }
// }
// .apexcharts-legend-series,
// .apexcharts-legend-text {
//    [data-theme='dark'] & {
//       color: $text_color;
//    }
// }
// .apexcharts-legend-marker,
// .apexcharts-legend-text {
//    [data-theme='dark'] & {
//       color: $text_color;
//    }
// }
.apexcharts-legend-text .apexcharts-text .apexcharts-xaxis-label {
   [data-theme='dark'] & {
      color: #fff;
   }
}
// .apexcharts-tooltip.apexcharts-theme-light {
//    [data-theme='dark'] & {
//       background-color: $controls;
//       color: #000;
//       border-color: $background_surface;
//    }
// }

.defaultShippping {
   [data-theme='dark'] & {
      color: #000;
   }
}
.panel {
   [data-theme='dark'] & {
      color: #fff;
   }
}
.cost-setting .cog-table th,
.cog-table td,
.cost-setting .ce-table th,
.cost-setting .ce-table td {
   [data-theme='dark'] & {
      color: #fff;
   }
}
.amazon-ads-dark-image {
   height: 45px !important ;
   width: 30px !important;
}
