import {
   Table,
   TableContainer,
   Tbody,
   Td,
   Th,
   Thead,
   Tr,
} from '@chakra-ui/react';
import { PAYMENT_GATEWAY_STRINGS } from '../../../utils/strings/cfo';
import { useAppSelector } from '../../../store/store';
import { PaymentMethod } from '../../../api/service/cfo';
import { useEffect, useState } from 'react';
import PaymentM from './payment-method';

function PaymentGateway() {
   const { paymentMethod } = useAppSelector((state) => state.cfo);
   const [pMethods, setpMethods] = useState<PaymentMethod[]>(paymentMethod);
   useEffect(() => {
      setpMethods(paymentMethod);
      return () => setpMethods([]);
   }, [paymentMethod]);
   const handleValueChange = (
      newValue: string,
      pm: PaymentMethod,
      type: string,
   ): void => {
      setpMethods((prev) => {
         const newArr = prev.slice();
         const idx = newArr.findIndex((p) => p.method == pm.method);
         const newVal: PaymentMethod = {
            clientId: pm.clientId,
            method: pm.method,
            id: pm.id,
            cost: type == 'cost' ? newValue : pm.cost,
            fee: type == 'fee' ? newValue : pm.fee,
         };
         newArr.splice(idx, 1, newVal);
         return newArr;
      });
   };

   return (
      <TableContainer className='cog-table' my={5} mx={4}>
         <Table variant='simple'>
            <Thead>
               <Tr>
                  <Th>Payment Gateway Costs</Th>
                  <Th>Cost</Th>
                  <Th> Fee</Th>
                  <Th></Th>
               </Tr>
            </Thead>
            <Tbody>
               <Tr borderBottom={'1px solid #C2CBD4'}>
                  <Td>Shopify Payment</Td>
                  <Td>{PAYMENT_GATEWAY_STRINGS.fees}</Td>
                  <Td></Td>
                  <Td></Td>
               </Tr>
               {pMethods.map((pm) => {
                  return (
                     <PaymentM
                        pm={pm}
                        handleValueChange={handleValueChange}
                        pMethods={pMethods}
                     />
                  );
               })}
            </Tbody>
         </Table>
      </TableContainer>
   );
}

export default PaymentGateway;
