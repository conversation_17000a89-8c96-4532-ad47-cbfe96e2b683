import React, { useState, useRef, useEffect } from 'react';
import './select.scss';
import { Option } from '../chatbox/interface';
import useClickOutside from '../../hooks/click-outside';
import { Text, Flex, Image, Tooltip } from '@chakra-ui/react';

interface MySelectProps {
   options: Option[];
   selectedMode: string;
   onModeChange: (mode: string) => void;
   showSampleQuestions: boolean;
   marcoCustomSelect?: string;
}

const MySelect: React.FC<MySelectProps> = ({
   selectedMode,
   options,
   onModeChange,
   showSampleQuestions,
   marcoCustomSelect,
}) => {
   const [isOpen, setIsOpen] = useState(false);
   const [selectedOption, setSelectedOption] = useState<Option | null>(
      options[options.length - 1],
   );

   useEffect(() => {
      const defaultOption = options.find(
         (option) => option.mode === selectedMode,
      );
      setSelectedOption(defaultOption || options[options.length - 1]);
   }, [selectedMode, options]);

   const handleOptionClick = (option: Option) => {
      const selectedOption = option;
      setSelectedOption(selectedOption);
      onModeChange(selectedOption?.mode || '');
      setSelectedOption(option);
      setIsOpen(false);
   };
   const handleCloseDropdown = () => {
      setIsOpen(false);
   };

   const containerRef = useRef<HTMLDivElement>(null);
   useClickOutside(containerRef, handleCloseDropdown);

   return (
      <div className='custom-select-container' ref={containerRef}>
         <div
            id={marcoCustomSelect}
            className='custom-select-header'
            onClick={() => setIsOpen(!isOpen)}
         >
            <div className='selected-option'>
               {selectedOption && (
                  <Flex flexDirection={'row'} alignItems={'center'}>
                     <Image
                        src={selectedOption?.icon}
                        alt='Icon'
                        className='option-icon'
                        boxSize={'20px'}
                     />
                     <Text fontSize={'12px'}> {selectedOption.label}</Text>
                  </Flex>
               )}
               {!selectedOption && 'Select'}
            </div>
            <div className={`dropdown-icon ${isOpen ? 'open' : ''}`}>
               &#9660;
            </div>
         </div>
         {isOpen && (
            <div
               className={
                  showSampleQuestions
                     ? 'custom-select-options'
                     : 'custom-select-options-up'
               }
            >
               {options.map((option, index) => {
                  const disabled = option.comingSoon || !option.isActive;
                  const hoverText = !option.isActive
                     ? option.inActiveMsg
                     : option.description;
                  return (
                     <Tooltip label={hoverText} hasArrow gutter={20}>
                        <button
                           key={index}
                           className='option'
                           onClick={() => handleOptionClick(option)}
                           disabled={disabled}
                        >
                           <div>
                              <img
                                 src={option.icon}
                                 alt='Icon'
                                 className='option-icon'
                              />
                           </div>
                           <Text fontSize={'12px'}>{option.label}</Text>
                           <div
                              className={`option-description ${
                                 option.comingSoon ? 'orange' : ''
                              }`}
                           >
                              {option.description}
                           </div>
                        </button>
                     </Tooltip>
                  );
               })}
            </div>
         )}
      </div>
   );
};

export default MySelect;
