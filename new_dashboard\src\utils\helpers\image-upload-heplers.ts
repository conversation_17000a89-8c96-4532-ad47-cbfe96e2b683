import { decode, encode } from '../../api/service/social-watch';

export const encodeImage = (selectedFile: File): Promise<{ uri: string }> => {
   return new Promise<{ uri: string }>((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(selectedFile);
      reader.onloadend = async () => {
         const formData = new FormData();
         formData.append('media', selectedFile);
         try {
            const response = await encode(formData);
            if (response) resolve(response);
            return response?.uri;
         } catch (error) {
            return error;
         }
      };
      reader.onerror = (error) => reject(error);
   });
};

export const decodeImage = async (payload: { image_url: string }) => {
   try {
      return await decode(payload);
   } catch (error) {
      console.error('Error decoding image:', error);
      return null;
   }
};
