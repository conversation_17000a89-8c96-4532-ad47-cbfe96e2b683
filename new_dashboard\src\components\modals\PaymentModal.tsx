import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useApiMutation } from '@/hooks/react-query-hooks';
import ModalWrapper from './modal-wrapper';
import subsEndPoints from '@/api/service/subscription';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { closeModal, openModal } from '@/store/reducer/modal-reducer';
import { UserDetails } from '@/pages/socialwatch/interface';
import { modalTypes } from './modal-types';
import {
   PaymentModalProps,
   RazorpayInstance,
   RazorpayResponse,
   RazorpayTopupOptions,
} from '@/pages/settings/subscription/types';
import { toast } from 'sonner';

const PaymentModal = (modalProps: Record<string, unknown>) => {
   const dispatch = useDispatch();

   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);

   const props = modalProps as unknown as PaymentModalProps;
   const [gstin, setGstin] = useState('');
   const [billingAddress, setBillingAddress] = useState('');

   const [creatingCustomer, setCreatingCustomer] = useState(false);
   const [purchasingSubscription, setPurchasingSubscription] = useState(false);

   const { mutate: createCustomerId } = useApiMutation({
      queryKey: ['createCustomerId'],
      mutationFn: subsEndPoints.createRazorpayCustomer,
      onSuccessHandler: (data) => {
         setCreatingCustomer(false);
         dispatch(closeModal());
         if (data?.customer_id) {
            const updatedUserDetails = {
               ...userDetails,
               razorpay_customerid: data.customer_id,
            };
            LocalStorageService.setItem(
               Keys.FlableUserDetails,
               updatedUserDetails,
            );

            setPurchasingSubscription(true);
            dispatch(openModal({ modalType: modalTypes.LOADER_MODAL }));

            subscribeToPlan({
               plan_id: props.plan_id,
               plan_name: props.plan_name,
               client_id: props.client_id,
               client_Phone: props.client_Phone,
               client_Email: props.client_Email,
               rzrPay_PlanId: props.rzrPay_PlanId,
               rzrPay_ClientId: data.customer_id,
               isYearly: props.isYearly,
               amount: props.amount,
               attribute: props.attribute,
               autoRenew: props.autoRenew,
               currency: props.currency,
               extra_connectors: props.extra_connectors,
               max_users: props.max_users,
            });
         } else {
            toast.error('No customer ID returned from server.');
         }
      },
      onError: () => {
         setCreatingCustomer(false);
         dispatch(closeModal());
         toast.error('Failed to create Razorpay customer. Please try again.');
      },
   });

   const handleSuccess = (data: {
      success: boolean;
      message: string;
      subs_id?: string;
   }) => {
      setPurchasingSubscription(false);
      dispatch(closeModal());

      if (!window.Razorpay) {
         toast.error(
            'Razorpay SDK not loaded. Please refresh the page and try again.',
         );
         return;
      }

      toast.success(
         'Plan subscription initiated successfully! Redirecting to payment...',
      );

      const options = {
         key: import.meta.env.VITE_RAZRPAY_KEY_ID as string,
         subscription_id: data?.subs_id,
         name: 'Flable Ai',
         description: 'Subscription Plan',
         handler: async function (response: RazorpayResponse) {
            try {
               const verify = await fetch(
                  `${import.meta.env.VITE_BE_API}/subs/razorpay/verify-signature`,
                  {
                     method: 'POST',
                     headers: { 'Content-Type': 'application/json' },
                     body: JSON.stringify({
                        ...response,
                        type: 'subscription',
                     }),
                  },
               );
               //eslint-disable-next-line  @typescript-eslint/no-unsafe-assignment
               const result = await verify.json();
               //eslint-disable-next-line  @typescript-eslint/no-unsafe-member-access
               if (result.success) {
                  toast.success(
                     'Subscription Successful! Welcome to Flable AI.',
                  );
                  dispatch(closeModal());
                  window.location.reload();
               } else {
                  toast.error(
                     'Payment verification failed. Please contact support.',
                  );
               }
            } catch (error) {
               console.error('Verification error: ', error);
               toast.error(
                  'Payment verification error. Please contact support.',
               );
            }
         },
         prefill: {
            email: userDetails.email,
            contact: null,
         },
         theme: {
            color: '#3399cc',
         },
      };

      const rzp: RazorpayInstance = new window.Razorpay(
         options as unknown as RazorpayTopupOptions,
      );
      rzp.open();
   };

   const handleError = (error: unknown) => {
      setPurchasingSubscription(false);
      dispatch(closeModal());
      console.error('Plan subscription failed:', error);
      toast.error('Failed to initiate plan subscription. Please try again.');
   };

   const { mutate: subscribeToPlan } = useApiMutation({
      queryKey: ['subscribeToPlan'],
      mutationFn: subsEndPoints.subscribeToPlan,
      onSuccessHandler: (data) => handleSuccess(data),
      onError: handleError,
   });

   const handlePayment = () => {
      if (!billingAddress.trim()) {
         toast.error('Billing Address is required.');
         return;
      }

      if (userDetails.razorpay_customerid) {
         setPurchasingSubscription(true);
         dispatch(openModal({ modalType: modalTypes.LOADER_MODAL }));

         subscribeToPlan({
            plan_id: props.plan_id,
            plan_name: props.plan_name,
            client_id: props.client_id,
            client_Phone: null,
            client_Email: props.client_Email,
            rzrPay_PlanId: props.rzrPay_PlanId,
            rzrPay_ClientId: userDetails.razorpay_customerid,
            isYearly: props.isYearly,
            amount: props.amount,
            attribute: props.attribute,
            autoRenew: props.autoRenew,
            currency: props.currency,
            extra_connectors: props.extra_connectors,
            max_users: props.max_users,
         });
      } else {
         setCreatingCustomer(true);
         dispatch(openModal({ modalType: modalTypes.LOADER_MODAL }));

         createCustomerId({
            name: userDetails.fullName,
            contact: null,
            gstin: gstin.trim() || null,
            client_id: userDetails.client_id,
            email: userDetails.email,
            billing_address: billingAddress.trim(),
         });
      }
   };

   const loading = creatingCustomer || purchasingSubscription;

   return (
      <ModalWrapper
         heading='Complete Your Payment'
         overlayBgcolor='#00000099'
         noCloseBtn
      >
         <div className='p-4 text-left space-y-4 max-w-md mx-auto'>
            <h2 className='text-xl font-semibold'>
               Confirm Subscription Details
            </h2>

            <div>
               <label htmlFor='gstin' className='block mb-1 font-medium'>
                  GSTIN (optional)
               </label>
               <input
                  id='gstin'
                  type='text'
                  value={gstin}
                  onChange={(e) => setGstin(e.target.value.toUpperCase())}
                  className='w-full px-3 py-2 border rounded'
                  placeholder='Enter your GSTIN'
                  disabled={loading}
               />
            </div>

            <div>
               <label
                  htmlFor='billingAddress'
                  className='block mb-1 font-medium'
               >
                  Billing Address <span className='text-red-600'>*</span>
               </label>
               <textarea
                  id='billingAddress'
                  value={billingAddress}
                  onChange={(e) => setBillingAddress(e.target.value)}
                  className='w-full px-3 py-2 border rounded'
                  placeholder='Enter billing address'
                  rows={3}
                  required
                  disabled={loading}
               />
            </div>
            <button
               onClick={handlePayment}
               disabled={loading}
               className='w-full py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50'
               type='button'
            >
               {loading ? 'Processing...' : 'Proceed to Payment'}
            </button>
         </div>
      </ModalWrapper>
   );
};

export default PaymentModal;
