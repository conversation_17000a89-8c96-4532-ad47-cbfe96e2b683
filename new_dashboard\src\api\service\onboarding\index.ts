import { AxiosResponse } from 'axios';
import dashboardApiAgent from '../../agent';
import { AccountDetails } from '../../../store/reducer/onboarding-reducer';

export interface DefaultResponse {
   status: 'Success' | 'Failure';
   message: string;
}

/** DETAILS **/
export interface UserMasterList {
   type: string;
   value: string;
}

export interface CompetitorHandles {
   handle: string;
}

export interface UserSocialDetails {
   channel: string;
   client_id: string;
   channel_name: string;
   competitor_handles: CompetitorHandles[];
   is_active?: boolean;
}

export interface UserDetails {
   full_name: string;
   email_address: string;
   user_active: 'Y' | 'N';
   cb_product_updates: 'Y' | 'N';
   country: string | null;
   language: string | null;
   register_progress: string;
   client_id?: string;
}

export interface ClientDetails {
   [key: string]: string | null;
   email_address: string;
   register_progress: string;
   organization_type: 'Individual Business' | 'Marketing Agency';
   agency_name: string | null;
   agency_url: string | null;
   company_country: string;
   company_business_type: string;
   company_platform: string;
   company_url: string;
   company_traffic: string;
   company_annual_revenue: string;
   company_currency: string;
   company_name: string;
   last_update_date: string;
   role_in_organization: string;
}

export interface CompanyDetails {
   email_address: string;
   company_url: string;
   user_role: string;
   client_id: string;
   register_progress: string;
}

// PAYLOAD
export interface SendSnippetPayload {
   client_id: string;
   email_address_sender: string;
   email_address_receiver: string;
}

export interface UpdateRegisterProgressPayload {
   client_id: string;
   register_progress: string;
   email_address: string;
   action?: string;
}

export interface UserSocialDetailsPayload {
   client_id: string;
}

export interface FetchAllCompaniesPayload {
   email_address: string;
}

/** RESPONSE **/
export interface UserMasterListResponse extends DefaultResponse {
   list: UserMasterList[];
}

export interface SnippetResponse extends DefaultResponse {
   snippet: string;
}

export interface UserDetailsResponse extends DefaultResponse {
   details: {
      userDetails: UserDetails;
      accountDetails: AccountDetails[] | null;
   };
}

export interface ClientDetailsResponse extends DefaultResponse {
   details: {
      accountDetails: AccountDetails[];
   };
}

export interface UserSocialDetailsResponse extends DefaultResponse {
   details: UserSocialDetails[];
}

export interface FetchAllCompaniesResponse extends DefaultResponse {
   details: CompanyDetails[];
}

export interface UpdateRegisterProgressResponse extends DefaultResponse {
   details?: {
      [key: string]: string;
   };
   action?: string;
}

interface Endpoints {
   fetchUserMasterList: () => Promise<AxiosResponse<UserMasterListResponse>>;

   fetchSnippet: (payload: {
      client_id: string;
   }) => Promise<AxiosResponse<SnippetResponse>>;

   getUserDetails: (payload: {
      email_address: string;
   }) => Promise<AxiosResponse<UserDetailsResponse>>;

   createUserDetails: (
      payload: ClientDetails,
   ) => Promise<AxiosResponse<ClientDetailsResponse>>;

   updateRegisterProgress: (
      payload: UpdateRegisterProgressPayload,
   ) => Promise<AxiosResponse<UpdateRegisterProgressResponse>>;

   sendSnippetEmail: (
      payload: SendSnippetPayload,
   ) => Promise<AxiosResponse<DefaultResponse>>;

   getUserSocialDetails: (
      payload: UserSocialDetailsPayload,
   ) => Promise<AxiosResponse<UserSocialDetailsResponse>>;

   fetchAllCompanies: (
      payload: FetchAllCompaniesPayload,
   ) => Promise<AxiosResponse<FetchAllCompaniesResponse>>;

   addNewCompany: (payload: {
      email_address: string;
   }) => Promise<AxiosResponse<DefaultResponse>>;

   testConnection: (payload: {
      email_address: string;
   }) => Promise<AxiosResponse<DefaultResponse>>;
}

const onboardingEndpoints: Endpoints = {
   fetchUserMasterList: () => dashboardApiAgent.get('/onboarding/master_list'),

   fetchSnippet: (payload) =>
      dashboardApiAgent.get('/onboarding/snippet', {
         params: { client_id: payload.client_id },
      }),

   getUserDetails: (payload) =>
      dashboardApiAgent.get('/onboarding/user', {
         params: { email_address: payload.email_address },
      }),

   createUserDetails: (payload) =>
      dashboardApiAgent.post('/onboarding/user', payload),

   updateRegisterProgress: (payload) =>
      dashboardApiAgent.put('/onboarding/user', payload),

   sendSnippetEmail: (payload) =>
      dashboardApiAgent.post('/onboarding/snippet', payload),

   getUserSocialDetails: (payload) =>
      dashboardApiAgent.get('/onboarding/user/social', {
         params: { client_id: payload.client_id },
      }),

   fetchAllCompanies: (payload) =>
      dashboardApiAgent.get('/onboarding/user/company', {
         params: { email_address: payload.email_address },
      }),

   addNewCompany: (payload) =>
      dashboardApiAgent.post('/onboarding/user/company', payload),

   testConnection: (payload) =>
      dashboardApiAgent.post('/onboarding/snippet/test_connection', payload),
};

export default onboardingEndpoints;
