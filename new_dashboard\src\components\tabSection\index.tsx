import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';

export type tabProps = Record<
   string,
   { label: string; content: React.ReactNode }
>;

export default function TabSection({
   tabObject,
   underline = true,
}: {
   tabObject: tabProps;
   underline?: boolean;
}) {
   const firstTab = Object.keys(tabObject)[0];
   const [searchParams, setSearchParams] = useSearchParams();

   const tabFromUrl = searchParams.get('tab');
   const initialTab =
      tabFromUrl && tabObject[tabFromUrl] ? tabFromUrl : firstTab;

   const [activeTab, setActiveTab] = useState(initialTab);

   useEffect(() => {
      const urlTab = searchParams.get('tab');
      if (urlTab && tabObject[urlTab] && urlTab !== activeTab) {
         setActiveTab(urlTab);
      }
   }, [searchParams, tabObject, activeTab]);

   const handleTabChange = (value: string) => {
      setActiveTab(value);
      const newParams = new URLSearchParams(searchParams);
      newParams.set('tab', value);
      setSearchParams(newParams);
   };

   return (
      <Tabs
         value={activeTab}
         onValueChange={handleTabChange}
         className='w-full'
      >
         <TabsList className='px-1'>
            <div className='flex gap-4'>
               {Object.entries(tabObject).map(([key, { label }]) => (
                  <TabsTrigger
                     key={key}
                     value={key}
                     className='relative para4 font-medium px-4 py-2 text-jet data-[state=active]:text-cerulean'
                  >
                     {label}
                     {activeTab === key && underline && (
                        <span className='absolute inset-x-0 -bottom-1 h-1 rounded-full bg-cerulean' />
                     )}
                  </TabsTrigger>
               ))}
            </div>
         </TabsList>

         {Object.entries(tabObject).map(([key, { content }]) => (
            <TabsContent key={key} value={key} className='pt-4'>
               {content}
            </TabsContent>
         ))}
      </Tabs>
   );
}
