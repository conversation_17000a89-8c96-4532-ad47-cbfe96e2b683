import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { useAppSelector } from '../../store/store';
import { useDispatch } from 'react-redux';
import { closeModal } from '../../store/reducer/modal-reducer';

interface AccountSelectModalProps {
   title?: string;
   options: Record<string, string>;
   placeholder?: string;
   onSelect: (selectedValue: string | null) => void;
   confirmButtonText?: string;
   showCancelButton?: boolean;
}

const AccountSelectModal: React.FC = () => {
   const dispatch = useDispatch();
   const { payload } = useAppSelector((state) => state.modal);

   const {
      title = 'Select Account',
      options = {},
      placeholder = 'Choose an account',
      onSelect,
      confirmButtonText = 'Next',
      showCancelButton = true,
   } = (payload?.modalProps || {}) as AccountSelectModalProps;

   const [selectedValue, setSelectedValue] = useState<string>('');
   const [error, setError] = useState<string>('');

   const handleSelectChange = (value: string) => {
      setSelectedValue(value);
      if (error) setError('');
   };

   const handleConfirm = () => {
      if (!selectedValue) {
         setError('Please select an account');
         return;
      }

      onSelect(selectedValue);
      dispatch(closeModal());
   };

   const handleCancel = () => {
      onSelect(null);
      dispatch(closeModal());
   };

   return (
      <Dialog open onOpenChange={() => handleCancel()}>
         <DialogContent className="max-w-lg bg-white dark:bg-zinc-900 shadow-2xl rounded-xl border-0 z-50 p-0 overflow-hidden">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-zinc-800 dark:to-zinc-700 px-6 py-4 border-b border-gray-100 dark:border-zinc-600">
               <DialogHeader className="space-y-0">
                  <DialogTitle className="text-xl font-semibold text-gray-900 dark:text-white">
                     {title}
                  </DialogTitle>
               </DialogHeader>
            </div>

            <div className="px-6 py-6 space-y-6">
               <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300 block">
                     Select Account
                  </label>
                  <Select value={selectedValue} onValueChange={handleSelectChange}>
                     <SelectTrigger className={`w-full h-16 px-4 border-1 rounded-sm transition-all duration-200 ${
                        error
                           ? 'border-red-400 bg-red-50 dark:bg-red-900/20 focus:border-red-500 focus:ring-red-200'
                           : 'border-gray-200 dark:border-zinc-600 bg-gray-50 dark:bg-zinc-800 hover:border-blue-300  '
                     } focus:ring-2 focus:ring-opacity-50`}>
                        <SelectValue
                           placeholder={placeholder}
                           className="text-gray-900 dark:text-white font-medium"
                        />
                     </SelectTrigger>
                     <SelectContent className="bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-600 rounded-lg  shadow-lg">
                        {Object.entries(options).map(([value, label]) => (
                           <SelectItem
                              key={value}
                              value={value}
                              className="px-2 py-2 hover:bg-blue-50 dark:hover:bg-zinc-700 cursor-pointer transition-colors duration-150 text-gray-900 dark:text-white"
                           >
                              <div className="flex items-center space-x-3">
                                 
                                 <span className="font-medium">{label}</span>
                              </div>
                           </SelectItem>
                        ))}
                     </SelectContent>
                  </Select>

                  {error && (
                     <div className="flex items-center space-x-2 mt-2">
                        <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                           <span className="text-white text-xs">!</span>
                        </div>
                        <p className="text-sm text-red-600 dark:text-red-400 font-medium">
                           {error}
                        </p>
                     </div>
                  )}
               </div>
            </div>

            <div className=" px-6 py-4   dark:border-zinc-600">
               <DialogFooter className="flex justify-end space-x-3">
                  {showCancelButton && (
                     <Button
                        variant="outline"
                        onClick={handleCancel}
                        className="px-6 py-2 border-gray-300 dark:border-zinc-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-zinc-700 transition-colors duration-200"
                     >
                        Cancel
                     </Button>
                  )}
                  <Button
                     onClick={handleConfirm}
                     disabled={!selectedValue}
                     className={`px-6 py-2 font-medium transition-all duration-200 ${
                        !selectedValue
                           ? 'bg-gray-300 dark:bg-zinc-600 text-gray-500 dark:text-zinc-400 cursor-not-allowed'
                           : 'bg-blue-500 hover:bg-blue-500 text-white shadow-md hover:shadow-xl transform hover:-translate-y-0.5'
                     }`}
                  >
                     {confirmButtonText}
                  </Button>
               </DialogFooter>
            </div>
         </DialogContent>
      </Dialog>
   );
};

export default AccountSelectModal;
