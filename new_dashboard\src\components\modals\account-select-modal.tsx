import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { useAppSelector } from '../../store/store';
import { useDispatch } from 'react-redux';
import { closeModal } from '../../store/reducer/modal-reducer';

interface AccountSelectModalProps {
   title?: string;
   options: Record<string, string>;
   placeholder?: string;
   onSelect: (selectedValue: string | null) => void;
   confirmButtonText?: string;
   showCancelButton?: boolean;
}

const AccountSelectModal: React.FC = () => {
   const dispatch = useDispatch();
   const { payload } = useAppSelector((state) => state.modal);

   const {
      title = 'Select Account',
      options = {},
      placeholder = 'Choose an account',
      onSelect,
      confirmButtonText = 'Next',
      showCancelButton = true,
   } = (payload?.modalProps || {}) as AccountSelectModalProps;

   const [selectedValue, setSelectedValue] = useState<string>('');
   const [error, setError] = useState<string>('');

   const handleSelectChange = (value: string) => {
      setSelectedValue(value);
      if (error) setError('');
   };

   const handleConfirm = () => {
      if (!selectedValue) {
         setError('Please select an account');
         return;
      }

      onSelect(selectedValue);
      dispatch(closeModal());
   };

   const handleCancel = () => {
      onSelect(null);
      dispatch(closeModal());
   };

   return (
      <Dialog open onOpenChange={() => handleCancel()}>
         <DialogContent className="max-w-md bg-white dark:bg-zinc-900 shadow-lg rounded-lg z-50">
            <DialogHeader>
               <DialogTitle>{title}</DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
               <Select value={selectedValue} onValueChange={handleSelectChange}>
                  <SelectTrigger className={`w-full ${error ? 'border-red-500' : ''}`}>
                     <SelectValue placeholder={placeholder} />
                  </SelectTrigger>
                  <SelectContent>
                     {Object.entries(options).map(([value, label]) => (
                        <SelectItem key={value} value={value}>
                           {label}
                        </SelectItem>
                     ))}
                  </SelectContent>
               </Select>

               {error && (
                  <p className="text-sm text-red-500 mt-1">
                     {error}
                  </p>
               )}
            </div>

            <DialogFooter>
               {showCancelButton && (
                  <Button variant="outline" onClick={handleCancel}>
                     Cancel
                  </Button>
               )}
               <Button
                  onClick={handleConfirm}
                  disabled={!selectedValue}
               >
                  {confirmButtonText}
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
};

export default AccountSelectModal;
