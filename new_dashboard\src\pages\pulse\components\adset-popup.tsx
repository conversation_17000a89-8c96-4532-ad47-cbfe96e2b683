import React, { useState, useEffect } from 'react';
import './adset-popup.scss';
import { UserDetails } from './interface';
import { useAppSelector } from '../../../store/store';
import {
   objectiveToMetrics,
   cap,
   toShowCurrency,
   DEFAULT_SORT_BY_OBJECTIVE,
   sortAdsByNestedKPI,
   getDateRange,
   getTop5BySpend,
} from '../utils/helper';
import { LocalStorageService, Keys } from '../../../utils/local-storage';
import XI_icon from '../../../assets/icons/marco-response-icon.svg';
import {
   Tabs,
   TabList,
   TabPanels,
   Tab,
   TabPanel,
   useColorMode,
   Icon,
   Flex,
   Box,
   Skeleton,
} from '@chakra-ui/react';
import { RiVideoLine } from 'react-icons/ri';
import { calculateHelper } from '../../utils/kpiCalculaterHelper';
import { Lu<PERSON>rrowUp, LuArrowDown, LuArrowDownUp } from 'react-icons/lu';
import pulseMetaService, {
   DaywiseAdKPIsCalculated,
   DaywiseAdsetKPIsCalculated,
   DaywiseCampaignKPIsCalculated,
   DaywiseTargetingKPIsCalculated,
} from '../../../api/service/pulse/performance-insights/meta-ads';
import { useApiQuery } from '../../../hooks/react-query-hooks';
import { pulseMetaKeys } from '../../dashboard/utils/query-keys';
import { splitAndUppercaseString } from '../../marco/utils/alerting-agent/agents-helpers';

interface NestedPopupProps {
   isOpen: boolean;
   onBack: () => void;
   onClose: () => void;
   details?: number;
   campaign: DaywiseCampaignKPIsCalculated;
   adset: DaywiseAdsetKPIsCalculated;
   allAdsets?: DaywiseAdsetKPIsCalculated[] | null | undefined;
}

const AdsetPopup: React.FC<NestedPopupProps> = ({
   isOpen,
   onBack,
   onClose,
   adset,
   campaign,
}) => {
   const { colorMode, toggleColorMode } = useColorMode();

   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);

   const { dateRange, prevRange } = useAppSelector((state) => state.kpi);
   const { channel, objective, metric } = useAppSelector(
      (state) => state.dropdown,
   );

   const { start_date, end_date, prev_start_date, prev_end_date, days } =
      getDateRange(dateRange, prevRange);

   const [adData, setAdData] = useState<DaywiseAdKPIsCalculated[]>([]);
   const [sortDetails, setSortDetails] = useState<{
      metric: string;
      order: string;
   }>({
      metric:
         DEFAULT_SORT_BY_OBJECTIVE[
            objective as keyof typeof DEFAULT_SORT_BY_OBJECTIVE
         ],
      order: 'desc',
   });

   const adPayload = {
      client_id: userDetails.client_id,
      adset_id: adset.adset_id.toString(),
      objective,
      campaign_id: String(campaign.campaign_id),
      start_date,
      end_date,
      prev_start_date,
      prev_end_date,
   };

   const {
      data: metaAds,
      isFetching: isFetchingMetaAds,
      isSuccess: fetchedMetaAds,
   } = useApiQuery({
      queryKey: [
         pulseMetaKeys.metaAds,
         metric,
         objective,
         `${campaign.campaign_id}`,
      ],
      queryFn: () => pulseMetaService.fetchMetaAdsDaywise(adPayload),
      enabled: !!days && !!metric && !!objective,
      refetchOnWindowFocus: false,
   });

   const targetingPayload = {
      client_id: userDetails.client_id,
      adset_id: adset.adset_id.toString(),
      campaign_id: campaign.campaign_id.toString(),
      objective,
      start_date,
      end_date,
   };

   const { data: metaTargeting } = useApiQuery({
      queryKey: [
         pulseMetaKeys.metaTargeting,
         metric,
         objective,
         `${campaign.campaign_id}`,
      ],
      queryFn: () =>
         pulseMetaService.fetchMetaTargetingDaywise(targetingPayload),
      enabled: !!days && !!metric && !!objective,
      refetchOnWindowFocus: false,
   });

   const payload = {
      client_id: userDetails.client_id,
      adset_id: adset.adset_id.toString(),
      campaign_id: campaign.campaign_id.toString(),
      objective,
      targeting_kpis: {
         age: getTop5BySpend(metaTargeting?.data?.age || []) || [],
         gender: getTop5BySpend(metaTargeting?.data?.gender || []) || [],
         placement: getTop5BySpend(metaTargeting?.data?.placement || []) || [],
         region: getTop5BySpend(metaTargeting?.data?.region || []) || [],
         country: getTop5BySpend(metaTargeting?.data?.country || []) || [],
      },
   };

   const {
      data: metaTargetingInsights,
      isSuccess: fetchedTargetingInsights,
      isFetching: isFetchingTargetingInsights,
   } = useApiQuery({
      queryKey: [
         pulseMetaKeys.targetingInsights,
         metric,
         objective,
         `${campaign?.campaign_id}`,
      ],
      queryFn: () => pulseMetaService.fetchTargetingInsights(payload),
      enabled: !!days && !!metric && !!objective && !!metaTargeting,
      refetchOnWindowFocus: false,
   });

   useEffect(() => {
      setAdData(metaAds?.data || []);
   }, [fetchedMetaAds]);

   const metricOrder = objectiveToMetrics[objective];

   const sortedAdsetKpis = adset.kpis.slice().sort((a, b) => {
      const indexA = metricOrder.indexOf(a.kpi_name);
      const indexB = metricOrder.indexOf(b.kpi_name);

      return (
         (indexA === -1 ? Infinity : indexA) -
         (indexB === -1 ? Infinity : indexB)
      );
   });

   const sortedAdData = adData.slice().map((ad) => ({
      ...ad,
      kpis: ad.kpis.slice().sort((a, b) => {
         const indexA = metricOrder.indexOf(a.kpi_name);
         const indexB = metricOrder.indexOf(b.kpi_name);
         return (
            (indexA === -1 ? Infinity : indexA) -
            (indexB === -1 ? Infinity : indexB)
         );
      }),
   }));

   const handleAdsSorting = (metric: string) => {
      const order =
         sortDetails.metric === metric
            ? sortDetails.order === 'asc'
               ? 'desc'
               : 'asc'
            : 'asc';
      setSortDetails({ metric, order });

      const sortedAds = sortAdsByNestedKPI(adData, metric, order);

      setAdData([...sortedAds]);
   };

   const renderData = (value: string | number) => {
      if (typeof value === 'number') {
         return Number.isInteger(value) ? value : value.toFixed(2);
      }
      if (typeof value === 'string') {
         return cap(value);
      }
      return value === 'NaN' ? 'N/A' : value;
   };

   useEffect(() => {
      const darkThemeQuery = window.matchMedia('(prefers-color-scheme: dark)');

      const handleThemeChange = (e: MediaQueryListEvent) => {
         if (e.matches) {
            toggleColorMode();
         }
      };

      document.documentElement.setAttribute(
         'data-theme',
         colorMode === 'dark' ? 'dark' : 'light',
      );

      darkThemeQuery.addEventListener('change', handleThemeChange);

      return () => {
         darkThemeQuery.removeEventListener('change', handleThemeChange);
      };
   }, [colorMode]);

   if (!isOpen) return null;

   const renderTable = (
      data: DaywiseTargetingKPIsCalculated[],
      targeting_type: 'age' | 'gender' | 'placement' | 'region' | 'country',
   ) => {
      if (!data || data.length === 0) {
         return <p>No data available</p>;
      }

      const headers = data[0].kpis.map((kpi) => kpi.kpi_name);

      return (
         <>
            <table
               className='adset-summary-table'
               style={{
                  backgroundColor:
                     colorMode === 'dark'
                        ? '#var(--background-surface)'
                        : '#fff',
               }}
            >
               <thead>
                  <tr>
                     <th>{splitAndUppercaseString(targeting_type)}</th>
                     {headers.map((header, index) => (
                        <th key={index}>{splitAndUppercaseString(header)}</th>
                     ))}
                  </tr>
               </thead>
               <tbody>
                  {getTop5BySpend(data).map((row, rowIndex) => (
                     <tr key={rowIndex}>
                        <td>{splitAndUppercaseString(row?.targeting_key)}</td>
                        {headers?.map((header, colIndex) => (
                           <td key={colIndex}>
                              {renderData(
                                 row.kpis.find((kpi) => kpi.kpi_name === header)
                                    ?.kpi_value || 'N/A',
                              )}
                           </td>
                        ))}
                     </tr>
                  ))}
               </tbody>
            </table>
            {isFetchingTargetingInsights ? (
               <p>Loading...</p>
            ) : fetchedTargetingInsights &&
              metaTargetingInsights?.[targeting_type] ? (
               <p>
                  <span className='recommendation'>Recommendation:</span>{' '}
                  {metaTargetingInsights?.[targeting_type]}
               </p>
            ) : (
               <p>No data available</p>
            )}
         </>
      );
   };

   const renderAgeTable = () =>
      renderTable(metaTargeting?.data?.age || [], 'age');

   const renderGenderTable = () =>
      renderTable(metaTargeting?.data?.gender || [], 'gender');

   const renderPlacementTable = () =>
      renderTable(metaTargeting?.data?.placement || [], 'placement');

   const renderRegionTable = () =>
      renderTable(metaTargeting?.data?.region || [], 'region');

   const renderCountryTable = () =>
      renderTable(metaTargeting?.data?.country || [], 'country');

   return (
      <div className='adset-popup-overlay'>
         <div className='adset-popup-content'>
            <div
               className='adset-heading'
               style={{
                  color: colorMode === 'dark' ? 'var(--text-color)' : '#000',
                  background:
                     colorMode === 'dark'
                        ? 'var(--background-surface)'
                        : '#white',
               }}
            >
               <h3>
                  <button onClick={onBack}>{'<'}</button> {adset?.adset_name}
               </h3>
               <button className='close-button' onClick={onClose}>
                  x
               </button>
            </div>
            <hr className='adset-divider' />
            <div className=' adset-top'>
               <button>{cap(channel)}</button>
               <button>{days} Days</button>
            </div>
            <div className='adset-details'>
               <div className='campaign-insights-header'>
                  <img
                     src={XI_icon}
                     alt='Insight Icon'
                     className='insight-icon'
                  />
                  <h3>Ad Set Insights</h3>
               </div>
               <div
                  className='headers'
                  style={{
                     background:
                        colorMode === 'dark'
                           ? 'var(--background-surface)'
                           : '#fff',
                  }}
               >
                  <Tabs>
                     <TabList>
                        <Tab>
                           <h4>Age</h4>
                        </Tab>
                        <Tab>
                           <h4>Gender</h4>
                        </Tab>
                     </TabList>

                     <TabPanels>
                        <TabPanel>
                           {metaTargeting?.data
                              ? renderAgeTable()
                              : 'Loading....'}
                        </TabPanel>
                        <TabPanel>
                           {metaTargeting?.data
                              ? renderGenderTable()
                              : 'Loading....'}
                        </TabPanel>
                     </TabPanels>
                  </Tabs>
               </div>
               <div
                  className='headers'
                  style={{
                     background:
                        colorMode === 'dark'
                           ? 'var(--background-surface)'
                           : '#fff',
                  }}
               >
                  <h4>Placement</h4>
                  {metaTargeting?.data ? renderPlacementTable() : 'Loading....'}
               </div>
               <div
                  className='headers'
                  style={{
                     background:
                        colorMode === 'dark'
                           ? 'var(--background-surface)'
                           : '#fff',
                  }}
               >
                  <Tabs>
                     <TabList>
                        <Tab>
                           <h4>Region</h4>
                        </Tab>
                        <Tab>
                           <h4>Country</h4>
                        </Tab>
                     </TabList>

                     <TabPanels>
                        <TabPanel>
                           {metaTargeting?.data
                              ? renderRegionTable()
                              : 'Loading....'}
                        </TabPanel>
                        <TabPanel>
                           {metaTargeting?.data
                              ? renderCountryTable()
                              : 'Loading....'}
                        </TabPanel>
                     </TabPanels>
                  </Tabs>
               </div>
            </div>
            <div className='adset-overview'>
               <h3>Ad Set Overview</h3>
               <div className='insights'>
                  <table className='insights-table'>
                     <thead>
                        <tr>
                           {objectiveToMetrics[objective].map((kpi, index) => (
                              <th key={index}>{cap(kpi.toUpperCase())}</th>
                           ))}
                        </tr>
                     </thead>
                     <tbody>
                        {sortedAdsetKpis.map((kpi, index) => {
                           const {
                              percentage,
                              color,
                              direction,
                              currentValue,
                           } = calculateHelper(
                              kpi?.kpi_name,
                              kpi?.kpi_current,
                              kpi?.kpi_previous,
                           );
                           const arrow = direction === 'is up' ? '↑' : '↓';
                           const currencyValue = toShowCurrency(
                              kpi.kpi_name,
                              campaign.recent_currency,
                           );
                           if (
                              !objectiveToMetrics[objective].includes(
                                 kpi.kpi_name,
                              )
                           ) {
                              return <></>;
                           }
                           return (
                              <td key={index}>
                                 <div>
                                    {currencyValue}
                                    {currentValue}
                                 </div>
                                 <div>
                                    {percentage && direction && (
                                       <span style={{ color }}>
                                          {percentage}% {arrow}
                                       </span>
                                    )}
                                 </div>
                              </td>
                           );
                        })}
                     </tbody>
                  </table>
               </div>
            </div>
            <div className='ad-set'>
               <h3>Ads Overview</h3>
               <div className='adset-overview'>
                  <table className='adset-table'>
                     <thead>
                        <tr>
                           <th>Ads</th>
                           <th>Creative</th>
                           <th>Creative Type</th>
                           {objectiveToMetrics[objective].map((kpi, index) => (
                              <th key={index}>
                                 <Flex
                                    justifyContent='start'
                                    alignItems='center'
                                    cursor='pointer'
                                    onClick={() => handleAdsSorting(kpi)}
                                    role='group'
                                 >
                                    {kpi === 'conversion_rate'
                                       ? 'CVR'
                                       : cap(kpi.toUpperCase())}
                                    {sortDetails.metric === kpi ? (
                                       sortDetails.order === 'asc' ? (
                                          <LuArrowUp />
                                       ) : (
                                          <LuArrowDown />
                                       )
                                    ) : (
                                       <Box
                                          opacity={0}
                                          transition='opacity 0.2s ease'
                                          _groupHover={{ opacity: 1 }}
                                          ml='4px'
                                       >
                                          <LuArrowDownUp />
                                       </Box>
                                    )}
                                 </Flex>
                              </th>
                           ))}
                        </tr>
                     </thead>
                     <tbody>
                        {isFetchingMetaAds
                           ? Array.from({ length: 2 }).map((_, rowIndex) => (
                                <tr key={rowIndex}>
                                   {Array.from({ length: 12 }).map(
                                      (_, cellIndex) => (
                                         <td key={cellIndex}>
                                            <Skeleton
                                               noOfLines={1}
                                               height='10px'
                                            />
                                         </td>
                                      ),
                                   )}
                                </tr>
                             ))
                           : sortedAdData.map((ad, index) => {
                                const adDetails = ad.meta_data;

                                return (
                                   <>
                                      <tr key={index}>
                                         <td className='ads'>{ad.ad_name}</td>
                                         <td className='image_link'>
                                            {adDetails.instagram_permalink_url ? (
                                               <a
                                                  href={
                                                     adDetails.instagram_permalink_url
                                                  }
                                                  target='_blank'
                                               >
                                                  {!adDetails.image_link ? (
                                                     <Icon
                                                        className='image_link icon'
                                                        as={RiVideoLine}
                                                     />
                                                  ) : (
                                                     <img
                                                        className='image_link image'
                                                        src={
                                                           adDetails.image_link
                                                        }
                                                        alt='ad image'
                                                     />
                                                  )}
                                               </a>
                                            ) : (
                                               'NA'
                                            )}
                                         </td>
                                         <td>{adDetails.creative_type}</td>
                                         {sortedAdsetKpis.map(
                                            (kpi, kpiIndex) => {
                                               const adKpi = ad.kpis.find(
                                                  (adKpi) =>
                                                     adKpi.kpi_name ===
                                                     kpi.kpi_name,
                                               );
                                               const {
                                                  percentage,
                                                  color,
                                                  direction,
                                                  currentValue,
                                               } = calculateHelper(
                                                  adKpi?.kpi_name ?? '',
                                                  adKpi?.kpi_current,
                                                  adKpi?.kpi_previous,
                                               );
                                               const arrow =
                                                  direction === 'is up'
                                                     ? '↑'
                                                     : '↓';
                                               const currencyValue =
                                                  toShowCurrency(
                                                     adKpi?.kpi_name || '',
                                                     campaign.recent_currency,
                                                  );
                                               if (
                                                  !objectiveToMetrics[
                                                     objective
                                                  ].includes(kpi.kpi_name)
                                               ) {
                                                  return <></>;
                                               }
                                               return (
                                                  <td key={kpiIndex}>
                                                     <div>
                                                        {currencyValue}
                                                        {currentValue}
                                                     </div>
                                                     <div>
                                                        {percentage &&
                                                           direction && (
                                                              <span
                                                                 style={{
                                                                    color,
                                                                 }}
                                                              >
                                                                 {percentage}%{' '}
                                                                 {arrow}
                                                              </span>
                                                           )}
                                                     </div>
                                                  </td>
                                               );
                                            },
                                         )}
                                      </tr>
                                   </>
                                );
                             })}
                     </tbody>
                  </table>
               </div>
            </div>
         </div>
      </div>
   );
};

export default AdsetPopup;
