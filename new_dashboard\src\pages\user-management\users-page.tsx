import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import {
   Flex,
   Table,
   Button,
   Tr,
   Th,
   Td,
   useToast,
   Tabs,
   TabList,
   Tab,
   TabPanel,
   TabPanels,
} from '@chakra-ui/react';
import { Input, Text, Thead, Tbody } from '@chakra-ui/react';
import { TableContainer } from '@chakra-ui/react';
import { AiOutlineUserAdd } from 'react-icons/ai';
import { useAppDispatch, useAppSelector } from '../../store/store';

import {
   usersPageStrings,
   usersTableStrings,
} from '../../utils/strings/user-managament-strings';
import { useApiMutation, useApiQuery } from '../../hooks/react-query-hooks';
import { userManagementKeys } from '../../utils/strings/query-keys';
import userManagementEndpoints, {
   AllUserDetails,
   CreateUpdateUserPayload,
} from '../../api/service/users';
import { Keys, LocalStorageService } from '../../utils/local-storage';
import TableLoader from './components/table-loader';

import './table.scss';
import {
   setAllUsers,
   setSelectedUser,
} from '../../store/reducer/user-management-reducer';

interface UserDetails {
   full_name: string;
   email: string;
   client_id: string;
   company_url: string;
}

const UsersPage = () => {
   const toast = useToast();
   const navigate = useNavigate();
   const dispatch = useAppDispatch();

   const userDetails: UserDetails | null = LocalStorageService.getItem(
      Keys.FlableUserDetails,
   );

   const [searchUser, setSearchUser] = useState('');
   const [deletePermission, setDeletePermission] = useState(false);

   const { allUsers } = useAppSelector((state) => state.userManagement);

   const { isFetching: isAllUsersFetching, refetch: refetchAllUsers } =
      useApiQuery({
         queryKey: [userManagementKeys.getAllUsers],
         queryFn: () =>
            userManagementEndpoints.getAllUsers({
               company_url: userDetails?.company_url as string,
            }),
         selectHandler: (data) => {
            dispatch(setAllUsers(data.details));
            return data;
         },
         refetchOnReconnect: false,
         refetchOnWindowFocus: false,
      });

   const updateUserMutation = useApiMutation({
      queryKey: [userManagementKeys.updateUser],
      mutationFn: userManagementEndpoints.updateUser,
      onSuccessHandler: () => {
         toast({
            title: 'Success',
            description: usersPageStrings.activateUser,
            status: 'success',
            duration: 5000,
            isClosable: true,
         });
      },
      onError: () => {
         toast({
            title: 'Failed',
            description: usersPageStrings.actionFailed,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
   });

   const handleAddUser = () => {
      navigate('/user/add');
   };

   const handleEditProfile = (user: AllUserDetails) => {
      navigate('/user/edit');
      dispatch(setSelectedUser(user));
   };

   const handleActivateDeactivateUser = async (user: AllUserDetails) => {
      const updateUserPayload: CreateUpdateUserPayload = {
         client_id: LocalStorageService.getItem(Keys.ClientId) as string,
         email_address: user.email_address,
         company_url: user.company_url,
         is_active: !user.role_is_active,
         cb_product_updates: user.cb_product_updates,
         country: user.country,
         create_date: user.create_date,
         full_name: user.full_name,
         language: user.language,
         last_update_date: new Date().toISOString(),
         user_active: user.login_user_active,
         user_role: user.user_role,
         profile_image: user.profile_image,
         user_confirmed: 'Y',
      };

      await updateUserMutation.mutateAsync(updateUserPayload);
      await refetchAllUsers();
   };

   const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchUser(e.target.value);
   };

   const filterUsers = (users: AllUserDetails[], is_active: boolean) => {
      return users
         ?.filter(
            (user: AllUserDetails) =>
               user.user_role &&
               user.login_user_active === 'Y' &&
               user.role_is_active === is_active,
         )
         ?.filter(
            (user: AllUserDetails) =>
               user?.full_name
                  .toLowerCase()
                  .includes(searchUser.toLowerCase()) ||
               user?.company_url
                  .toLowerCase()
                  .includes(searchUser.toLowerCase()) ||
               user?.user_role
                  .toLowerCase()
                  .includes(searchUser.toLowerCase()) ||
               user?.email_address
                  .toLowerCase()
                  .includes(searchUser.toLowerCase()),
         );
   };

   const returnUsersRow = (
      user: AllUserDetails,
      status: 'active' | 'inactive',
   ) => {
      return isAllUsersFetching ? (
         <TableLoader />
      ) : (
         <Tr key={user.email_address}>
            <Td className='t-data'>
               {user?.email_address}
               {status === 'active' && (
                  <Text
                     fontSize='14px'
                     textDecoration='underline'
                     cursor='pointer'
                     onClick={() => handleEditProfile(user)}
                  >
                     {usersTableStrings.editProfile}
                  </Text>
               )}
               {user.user_role !== 'Admin' &&
                  (user.email_address === userDetails?.email ||
                  deletePermission ? (
                     <Text
                        mt={4}
                        fontSize='14px'
                        color={status === 'active' ? 'red' : 'green'}
                        textDecoration='underline'
                        cursor='pointer'
                        onClick={() => void handleActivateDeactivateUser(user)}
                     >
                        {status === 'active'
                           ? usersTableStrings.deactivateUser
                           : allUsers?.filter(
                                (user: AllUserDetails) =>
                                   user.user_role &&
                                   user.login_user_active === 'Y' &&
                                   user.role_is_active,
                             ).length < 5 && usersTableStrings.activateUser}
                     </Text>
                  ) : null)}
            </Td>
            <Td className='t-data'>{user?.full_name}</Td>
            <Td className='t-data'>{user?.company_url}</Td>
            <Td className='t-data'>{user?.user_role}</Td>
         </Tr>
      );
   };

   useEffect(() => {
      const hasDeletePermission = allUsers?.some(
         (user: AllUserDetails) =>
            user.email_address === userDetails?.email &&
            user.company_url === userDetails?.company_url &&
            user.user_role === 'Admin',
      );

      if (hasDeletePermission) {
         setDeletePermission(true);
      }
   }, [allUsers]);

   return (
      <Flex direction='column' margin='70px 70px 0px 70px'>
         <Text fontSize='20px' fontWeight='bold'>
            {usersPageStrings.users}
         </Text>
         <Flex
            gap={2}
            justifyContent='space-between'
            alignItems='center'
            mt={4}
         >
            <Text fontSize='14px'>{usersPageStrings.usersDesc}</Text>
            <Flex gap={2}>
               <Button
                  leftIcon={<AiOutlineUserAdd />}
                  onClick={handleAddUser}
                  isDisabled={
                     isAllUsersFetching ||
                     allUsers?.filter(
                        (user: AllUserDetails) =>
                           user.user_role &&
                           user.login_user_active === 'Y' &&
                           user.role_is_active,
                     ).length >= 5
                  }
                  fontSize='14px'
               >
                  {usersPageStrings.addUser}
               </Button>
               <Input
                  name='search_user'
                  width='270px'
                  placeholder='Search User'
                  fontSize='14px'
                  value={searchUser}
                  onChange={handleInputChange}
               />
            </Flex>
         </Flex>
         <Tabs>
            <TabList>
               <Tab>
                  <h4>Active Users</h4>
               </Tab>
               <Tab>
                  <h4>Inactive Users</h4>
               </Tab>
            </TabList>
            <TabPanels>
               <TabPanel>
                  <TableContainer className='t-container'>
                     <Table variant='striped' colorScheme='gray' size='lg'>
                        <Thead>
                           <Tr>
                              <Th className='t-header'>
                                 {usersTableStrings.email}
                              </Th>
                              <Th className='t-header'>
                                 {usersTableStrings.name}
                              </Th>
                              <Th className='t-header'>
                                 {usersTableStrings.business}
                              </Th>
                              <Th className='t-header'>
                                 {usersTableStrings.role}
                              </Th>
                           </Tr>
                        </Thead>
                        <Tbody>
                           {allUsers &&
                              filterUsers(allUsers, true).map(
                                 (user: AllUserDetails) =>
                                    returnUsersRow(user, 'active'),
                              )}
                        </Tbody>
                     </Table>
                  </TableContainer>
               </TabPanel>
               <TabPanel>
                  <TableContainer className='t-container'>
                     <Table variant='striped' colorScheme='gray' size='lg'>
                        <Thead>
                           <Tr>
                              <Th className='t-header'>
                                 {usersTableStrings.email}
                              </Th>
                              <Th className='t-header'>
                                 {usersTableStrings.name}
                              </Th>
                              <Th className='t-header'>
                                 {usersTableStrings.business}
                              </Th>
                              <Th className='t-header'>
                                 {usersTableStrings.role}
                              </Th>
                           </Tr>
                        </Thead>
                        <Tbody>
                           {allUsers &&
                              filterUsers(allUsers, false).map(
                                 (user: AllUserDetails) =>
                                    returnUsersRow(user, 'inactive'),
                              )}
                        </Tbody>
                     </Table>
                  </TableContainer>
               </TabPanel>
            </TabPanels>
         </Tabs>
         {/* <TableContainer className='t-container' mt={10}>
            <Table variant='striped' colorScheme='gray' size='lg'>
               <Thead>
                  <Tr>
                     <Th className='t-header'>{usersTableStrings.email}</Th>
                     <Th className='t-header'>{usersTableStrings.name}</Th>
                     <Th className='t-header'>{usersTableStrings.business}</Th>
                     <Th className='t-header'>{usersTableStrings.role}</Th>
                  </Tr>
               </Thead>
               <Tbody>
                  {allUsers &&
                     filterUsers(allUsers).map((user: AllUserDetails) =>
                        returnUsersRow(user),
                     )}
               </Tbody>
            </Table>
         </TableContainer> */}
      </Flex>
   );
};

export default UsersPage;
