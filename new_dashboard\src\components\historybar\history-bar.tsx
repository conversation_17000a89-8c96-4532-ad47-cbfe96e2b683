import { FC, useState, useRef } from 'react';
import { jsPDF } from 'jspdf';
import './historybar.scss';
import { deleteMarcoHistory } from '../../api/service/marco';
import {
   Button,
   Input,
   Skeleton,
   useColorModeValue,
   Box,
} from '@chakra-ui/react';

import HistoryMenu from './history-menu';
import flableaiIcon from '../../assets/icons/flableai_icon.png';
import { FaPlus } from 'react-icons/fa';
import { renameHistoryTitle } from '../../api/service/marco';
import { PerformanceChartData } from '../chatbox/interface';
import { createRoot } from 'react-dom/client';
import PerformanceChart from '../chart/performancechart/performance-chart';
import { formatDate as convertToLocalTime } from '../../utils/date-formate';
import { useApiQuery } from '../../hooks/react-query-hooks';
import settingsService from '../../api/service/settings/index';
import { SettingsQueryKeys } from '../../pages/dashboard/utils/query-keys';
import { defineds } from '../../pages/dashboard/utils/default-ranges';

interface ChatMessage {
   id: string;
   userask: string;
   reply: { text: string; image: string | object; selectedMode: string };
   feedback?: string;
}

interface HistoryItem {
   sessionId: string;
   summary: string;
   title: string;
   chat: ChatMessage[];
   createdAt: string;
   updatedAt: string;
   id?: string;
   marcoHistoryQuestionId?: string;
   clearHistoryId?: string;
   marcoNewChatId?: string;
}

interface HistoryBarProps {
   history: HistoryItem[];
   onNewChat: () => void;
   onSelectSession: (sessionId: string) => void;
   onDeleteSession: (sessionId: string) => void;
   setHistory: (newHistory: HistoryItem[]) => void;
   handleLoadMore: () => void;
   hasMore: boolean;
   loading: boolean;
   id?: string;
   marcoHistoryQuestionId?: string;
   clearHistoryId?: string;
   marcoNewChatId?: string;
}
interface UserDetails {
   client_id: string;
   email: string;
}

const formatDate = (updatedAt: string): string => {
   const updatedAtDate = new Date(updatedAt);

   if (
      defineds.startOfToday <= updatedAtDate &&
      defineds.endOfToday >= updatedAtDate
   ) {
      return 'Today';
   } else if (
      defineds.startOfYesterday <= updatedAtDate &&
      defineds.endOfYesterday >= updatedAtDate
   ) {
      return 'Yesterday';
   } else if (
      defineds.startOfLastWeek <= updatedAtDate &&
      defineds.endOfYesterday >= updatedAtDate
   ) {
      return 'Last week';
   } else if (
      defineds.startOfLastMonth <= updatedAtDate &&
      defineds.endOfYesterday >= updatedAtDate
   ) {
      return 'Previous 30 days';
   }
   const monthNames = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
   ];
   return `${
      monthNames[updatedAtDate.getMonth()]
   } ${updatedAtDate.getFullYear()}`;
};

const HistoryBar: FC<HistoryBarProps> = ({
   history,
   onNewChat,
   onSelectSession,
   onDeleteSession,
   setHistory,
   hasMore,
   handleLoadMore,
   loading,
   id,
   marcoHistoryQuestionId,
   marcoNewChatId,
}) => {
   const groupedQuestions: {
      [formattedDate: string]: { sessionId: string; title: string }[];
   } = {};
   // const { isOpen, onOpen, onClose } = useDisclosure();
   const [renamingSessionId, setRenamingSessionId] = useState<string | null>(
      null,
   );
   const [newTitle, setNewTitle] = useState<string>('');
   const userDetails = JSON.parse(
      localStorage.getItem('userDetails') || '{}',
   ) as UserDetails;
   history.forEach((historyItem) => {
      const formattedDate = formatDate(historyItem.updatedAt);
      const title = historyItem.title.length > 0 ? historyItem.title : '';

      if (!groupedQuestions[formattedDate]) {
         groupedQuestions[formattedDate] = [];
      }

      groupedQuestions[formattedDate].push({
         sessionId: historyItem.sessionId,
         title,
      });
   });
   const { data } = useApiQuery({
      queryKey: [SettingsQueryKeys.timezone],
      queryFn: () =>
         settingsService.fetchTimezone({ client_id: userDetails.client_id }),
      enabled: true,
   });

   const handleChatClick = () => {
      onNewChat();
   };

   const handleSessionClick = (sessionId: string) => {
      onSelectSession(sessionId);
   };

   const renameTimeoutRef = useRef<number | null>(null);
   const renameTitle = async (sessionId: string, value: string) => {
      const payload = {
         session_id: sessionId,
         client_id: userDetails.client_id,
         user: userDetails.email,
         new_title: value,
      };

      try {
         const response = await renameHistoryTitle(payload);
         if (response && response.status === 200) {
            const updatedHistory = history.map((item) =>
               item.sessionId === sessionId ? { ...item, title: value } : item,
            );
            setHistory(
               updatedHistory.sort(
                  (a, b) =>
                     new Date(b.updatedAt).getTime() -
                     new Date(a.updatedAt).getTime(),
               ),
            );
         } else {
            console.log('Error while renaming history title');
         }
      } catch (error) {
         console.error('Unexpected error:', error);
      }
   };

   const handleRenameSessionChange = (
      e: React.ChangeEvent<HTMLInputElement>,
   ) => {
      const { value } = e.target;
      setNewTitle(value);
      if (renamingSessionId) {
         if (renameTimeoutRef.current) {
            clearTimeout(renameTimeoutRef.current);
         }
         renameTimeoutRef.current = window.setTimeout(() => {
            void renameTitle(renamingSessionId, value);
         }, 10000);
      }
   };
   const handleRenameSessionKeyPress = (
      e: React.KeyboardEvent<HTMLInputElement>,
   ) => {
      if (e.key === 'Enter' && renamingSessionId && newTitle != '') {
         if (renameTimeoutRef.current) {
            clearTimeout(renameTimeoutRef.current);
         }
         void renameTitle(renamingSessionId, newTitle);
      }
   };

   const handleRenameSessionBlur = () => {
      if (renamingSessionId) {
         setRenamingSessionId(null);
         setNewTitle('');
      }
   };

   const handleDeleteSession = async (sessionId: string) => {
      const index = history.findIndex((item) => item.sessionId === sessionId);

      if (index !== -1) {
         const result = await deleteMarcoHistory(sessionId, userDetails);
         if (result) {
            history.splice(index, 1);
            onDeleteSession(sessionId);
         }
      }
   };

   const trimTitle = (title: string, maxLength: number = 28) => {
      if (title.length > maxLength) {
         return `${title.slice(0, maxLength)}...`;
      }
      return title;
   };
   // const handleClearHistory = async () => {
   //    await clearallHistory(userDetails);
   //    setHistory([]);
   //    handleChatClick();
   //    onClose();
   // };

   const isPerformanceChartData = (
      data: unknown,
   ): data is PerformanceChartData => {
      return (
         (data as PerformanceChartData).schema !== undefined ||
         Array.isArray(data)
      );
   };

   const handleExportClick = (sessionId: string) => {
      const doc = new jsPDF();
      const flableAiIcon = new Image();
      flableAiIcon.src = flableaiIcon;
      flableAiIcon.crossOrigin = 'anonymous';
      const renderChartToImage = async (
         data: PerformanceChartData,
      ): Promise<string> => {
         return new Promise((resolve, reject) => {
            const chartDiv = document.createElement('div');
            chartDiv.style.position = 'absolute';
            chartDiv.style.top = '-9999px';
            document.body.appendChild(chartDiv);

            const root = createRoot(chartDiv);
            root.render(
               <PerformanceChart
                  performanceData={
                     isPerformanceChartData(data) ? data : undefined
                  }
               />,
            );

            setTimeout(() => {
               const svg = chartDiv.querySelector('svg');
               if (!svg) {
                  reject('SVG element not found');
                  return;
               }
               const canvas = document.createElement('canvas');
               const ctx = canvas.getContext('2d');
               if (!ctx) {
                  reject('Canvas context not found');
                  return;
               }
               const svgData = new XMLSerializer().serializeToString(svg);
               const img = new Image();
               img.onload = () => {
                  canvas.width = img.width;
                  canvas.height = img.height;
                  ctx.drawImage(img, 0, 0);
                  const imgURI = canvas.toDataURL('image/png');
                  document.body.removeChild(chartDiv);
                  resolve(imgURI);
               };
               img.src = `data:image/svg+xml;base64,${btoa(svgData)}`;
            }, 2000);
         });
      };

      flableAiIcon.onload = async () => {
         const imgWidth = 30;
         const imgHeight =
            (flableAiIcon.height * imgWidth) / flableAiIcon.width;

         const addFlableAiIcon = () => {
            for (let i = 1; i <= doc.getNumberOfPages(); i++) {
               doc.setPage(i);
               doc.addImage(flableAiIcon, 'PNG', 10, 10, imgWidth, imgHeight);
            }
         };

         const addClientNameAndDate = () => {
            const clientName = userDetails.email;
            const currentDate = new Date().toLocaleDateString();
            const maxWidth = doc.internal.pageSize.width;
            const textPadding = 40;
            const clientNameText = 'Client Name: ';
            const clientNameWidth = doc.getTextWidth(clientNameText);
            const dateofGeneration = 'Date of generation:';
            const currentDateWidth = doc.getTextWidth(dateofGeneration);
            const xPositionClientName =
               maxWidth - clientNameWidth - textPadding;
            const xPositionDate =
               maxWidth - currentDateWidth - (textPadding - 10);
            for (let i = 1; i <= doc.getNumberOfPages(); i++) {
               doc.setPage(i);
               doc.setFont('helvetica', 'bold');
               doc.setFontSize(10);

               doc.text(clientNameText, xPositionClientName, 10);
               doc.text(clientName, xPositionClientName + clientNameWidth, 10);

               doc.text(dateofGeneration, xPositionDate, 20);
               doc.text(currentDate, xPositionDate + currentDateWidth + 4, 20);
            }
         };

         const addQuestionsAndAnswers = async () => {
            const sessionToUse = sessionId;
            const session = history.find(
               (item) => item.sessionId === sessionToUse,
            );
            if (!session) {
               console.error('Session not found.');
               return;
            }
            let pageY = 40;
            let pageIndex = 0;
            let clientDetailsAdded = false;
            const { chat, createdAt } = session;
            for (const { userask, reply } of chat) {
               if (pageY + 40 > doc.internal.pageSize.height) {
                  doc.addPage();
                  pageY = 40;
                  pageIndex++;
                  addFlableAiIcon();
                  addClientNameAndDate();
                  clientDetailsAdded = true;
               }
               doc.setPage(pageIndex + 1);
               doc.setFont('helvetica', 'bold');
               doc.setFontSize(12);
               const useraskLines = doc.splitTextToSize(
                  userask,
                  180,
               ) as string[];

               let yOffset = pageY;
               doc.setFont('helvetica', 'normal');
               doc.setFontSize(10);
               doc.text(
                  convertToLocalTime(createdAt, data?.timezone),
                  10,
                  yOffset,
               );
               yOffset += 5;
               doc.setFont('helvetica', 'bold');
               doc.setFontSize(12);
               doc.text('You :', 10, yOffset);
               yOffset += 5;
               useraskLines.forEach((line: string) => {
                  doc.setFont('helvetica', 'normal');
                  doc.setFontSize(10);
                  doc.text(line, 10, yOffset);
                  yOffset += 5;
               });
               yOffset += 5;
               doc.setFont('helvetica', 'normal');
               doc.setFontSize(10);
               doc.text(
                  convertToLocalTime(createdAt, data?.timezone),
                  10,
                  yOffset,
               );
               yOffset += 5;
               doc.setFont('helvetica', 'bold');
               doc.setFontSize(12);
               doc.text('Marco:', 10, yOffset);
               yOffset += 5;
               console.log(reply.text, 'at 359');
               if (reply.text != '') {
                  const replyLines = doc.splitTextToSize(
                     reply.text,
                     180,
                  ) as string[];
                  replyLines.forEach((line) => {
                     doc.setFont('helvetica', 'normal');
                     doc.setFontSize(10);
                     doc.text(line, 10, yOffset);
                     yOffset += 5;
                  });
               } else if (reply.image as PerformanceChartData) {
                  const chartData = reply.image as PerformanceChartData;
                  const imgURI = await renderChartToImage(chartData);
                  console.log(imgURI, 'at 326');
                  const imgWidth = 180;
                  const imgHeight = 120;
                  if (yOffset + 125 >= 300) {
                     doc.addPage();
                     pageY = 40;
                     pageIndex++;
                     yOffset = pageY;
                     doc.setPage(pageIndex + 1);
                     doc.setFont('helvetica', 'bold');
                     doc.setFontSize(12);
                     addFlableAiIcon();
                     addClientNameAndDate();
                     clientDetailsAdded = true;
                  }
                  doc.addImage(imgURI, 'JPG', 10, yOffset, imgWidth, imgHeight);
                  yOffset += 125;
               }

               pageY = yOffset + 10;
            }
            if (!clientDetailsAdded) {
               addClientNameAndDate();
            }
         };

         addFlableAiIcon();
         await addQuestionsAndAnswers();
         doc.save('history.pdf');
      };

      flableAiIcon.onerror = (error) => {
         console.error('Error loading Flable AI icon:', error);
         doc.save('history.pdf');
      };
   };
   const handleOptionClick = (option: string, sessionId: string) => {
      switch (option) {
         case 'Share':
            // Handle share functionality
            break;
         case 'Rename':
            setRenamingSessionId(sessionId);
            break;
         case 'Delete':
            void handleDeleteSession(sessionId);
            break;
         case 'Export':
            void handleExportClick(sessionId);
            break;
         default:
            break;
      }
   };

   const textColor = useColorModeValue(
      'var(--background-surface)',
      'var(--text-color)',
   );
   const hoverBgColor = useColorModeValue('gray.100', 'gray.700');
   const borderColor = useColorModeValue('gray.200', 'gray.600');

   return (
      <div className='history-bar'>
         <h2 className='history-heading' style={{ color: textColor }}>
            History
         </h2>

         <div className='head'>
            <button
               className='new-chat-button'
               onClick={handleChatClick}
               id={marcoNewChatId}
            >
               <FaPlus className='plus-icon' />
               <span className='newchat-text'>New Chat</span>
            </button>
         </div>
         <div className='history-question-title' id={marcoHistoryQuestionId}>
            {Object.entries(groupedQuestions).map(([formattedDate, titles]) => (
               <div key={formattedDate} className='question-answer-group'>
                  <div className='formatted-date' style={{ color: textColor }}>
                     {formattedDate}
                  </div>
                  {titles.map(({ sessionId, title }) => (
                     <Box
                        key={sessionId}
                        className='history-question'
                        onClick={() => handleSessionClick(sessionId)}
                        color={textColor}
                        borderColor={borderColor}
                        _hover={{ bg: hoverBgColor }}
                     >
                        {renamingSessionId === sessionId ? (
                           // <input
                           //    type='text'
                           //    value={title}
                           //    onChange={(e) =>
                           //       void handleRenameSession(
                           //          sessionId,
                           //          e.target.value,
                           //       )
                           //    }
                           //    onBlur={() => setRenamingSessionId(null)}
                           // />
                           <Input
                              defaultValue={trimTitle(title)}
                              value={newTitle}
                              onChange={handleRenameSessionChange}
                              onBlur={handleRenameSessionBlur}
                              onKeyDown={handleRenameSessionKeyPress}
                              size='sm'
                              autoFocus
                           />
                        ) : (
                           <>
                              <div
                                 onClick={() => handleSessionClick(sessionId)}
                              >
                                 {trimTitle(title)}
                              </div>
                              <HistoryMenu
                                 sessionId={sessionId}
                                 onOptionClick={handleOptionClick}
                              />
                           </>
                        )}
                     </Box>
                  ))}
               </div>
            ))}
         </div>
         {/* <ClearHistoryDialog
            isOpen={isOpen}
            onClose={onClose}
            onClearHistory={handleClearHistory}
         /> */}
         {/* {hasMore && (
            <Button
               onClick={handleLoadMore}
               isLoading={loading}
               loadingText='Loading...'
               className='load-more-container'
            >
               Load More
            </Button>
         )} */}
         {loading
            ? Array.from({ length: 20 }).map((_, index) => (
                 <Skeleton height='10px' my='10px' key={index} />
              ))
            : hasMore && (
                 <Button
                    id={id}
                    onClick={handleLoadMore}
                    isLoading={loading}
                    loadingText='Loading...'
                    className='load-more-container'
                 >
                    Load More
                 </Button>
              )}
      </div>
   );
};

export default HistoryBar;
