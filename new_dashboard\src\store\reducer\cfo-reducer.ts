import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ICountry } from 'countries-list';
import {
   COGSResult,
   PaymentMethod,
   ShippingCost,
   ShippingCostByOrderIdData,
} from '../../api/service/cfo';
import { Keys, LocalStorageService } from '../../utils/local-storage';
import {
   FixedExpenseData,
   VariableExpenseData,
} from '../../pages/settings/interface';

interface CFOState {
   cogsFixedRate: COGSResult;
   paymentMethod: PaymentMethod[];
   shippingProfile: ShippingProfile;
   shippingCost: ShippingCost;
   shippingProfiles: ShippingProfiles[];
   fixedExpenses: FixedExpenseData[];
   shippingCostByOrderId: ShippingCostByOrderIdData[];
   variableExpenses: VariableExpenseData[];
}

export interface ShippingProfiles {
   profile_name: string;
   min_weights: number[];
   max_weights: number[];
   rates: number[];
   measures: string[];
   zone: string;
   is_fixed: boolean;
   id?: number;
}
export interface ShippingProfile {
   name: string;
   zones: ICountry[] | string;
   fixedRate: string;
   weightBased: WeightBased[];
}

export interface WeightBased {
   rate: string;

   minWeight: string;

   maxWeight: string;

   measure: string;
}

export const initialCFOState: CFOState = {
   cogsFixedRate: {
      fixed_percent: '',
      id: null,
   },
   paymentMethod: [],
   shippingProfile: {
      name: '',
      zones: 'worldwide',
      fixedRate: '',
      weightBased: [],
   },
   shippingProfiles: [],
   shippingCostByOrderId: [],
   shippingCost: {
      is_default: null,
      id: null,
      clientId: LocalStorageService.getItem(Keys.ClientId) as string,
   },
   fixedExpenses: [],
   variableExpenses: [],
};

const cfoSlice = createSlice({
   name: 'cfo',
   initialState: initialCFOState,
   reducers: {
      setCogsFixedRate: (
         state: CFOState,
         action: PayloadAction<COGSResult>,
      ) => {
         state.cogsFixedRate = action.payload;
      },
      setShippingProfile: (
         state: CFOState,
         action: PayloadAction<ShippingProfile | null>,
      ) => {
         state.shippingProfile =
            action.payload || initialCFOState.shippingProfile;
      },
      setShippingProfiles: (
         state: CFOState,
         action: PayloadAction<ShippingProfiles[]>,
      ) => {
         state.shippingProfiles = action.payload;
      },
      setshippingCostByOrderId: (
         state: CFOState,
         action: PayloadAction<ShippingCostByOrderIdData[]>,
      ) => {
         state.shippingCostByOrderId = action.payload;
      },
      setProfileName: (state: CFOState, action: PayloadAction<string>) => {
         state.shippingProfile.name = action.payload;
      },
      setFixedRate: (state: CFOState, action: PayloadAction<string>) => {
         state.shippingProfile.fixedRate = action.payload;
      },

      setzones: (
         state: CFOState,
         action: PayloadAction<ICountry[] | string>,
      ) => {
         state.shippingProfile.zones = action.payload;
      },
      setWeightBased: (
         state: CFOState,
         action: PayloadAction<WeightBased[]>,
      ) => {
         state.shippingProfile.weightBased = action.payload;
      },
      setPaymentMethods: (
         state: CFOState,
         action: PayloadAction<PaymentMethod[]>,
      ) => {
         state.paymentMethod = action.payload;
      },
      setshippingCost: (
         state: CFOState,
         action: PayloadAction<ShippingCost>,
      ) => {
         state.shippingCost = action.payload;
      },
      setFixedExpenses: (
         state: CFOState,
         action: PayloadAction<FixedExpenseData[]>,
      ) => {
         state.fixedExpenses = action.payload;
      },
      setVariableExpenses: (
         state: CFOState,
         action: PayloadAction<VariableExpenseData[]>,
      ) => {
         state.variableExpenses = action.payload;
      },
   },
});

export const {
   setCogsFixedRate,
   setshippingCost,
   setShippingProfile,
   setFixedRate,
   setProfileName,
   setzones,
   setWeightBased,
   setPaymentMethods,
   setShippingProfiles,
   setFixedExpenses,
   setVariableExpenses,
   setshippingCostByOrderId,
} = cfoSlice.actions;

export default cfoSlice.reducer;
