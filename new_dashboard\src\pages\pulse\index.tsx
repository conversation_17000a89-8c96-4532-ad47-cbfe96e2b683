import React, { useCallback, useEffect, useMemo, useState } from 'react';
import Webinsights from './components/web-insights';
import getPlatformImage from '../../utils/platform-image';
import { getInsightIcon } from '../../utils/insights-icon';
import { Keys, LocalStorageService } from '../../utils/local-storage';
import { DynamicInsightRankData } from '../../api/service/pulse';

import { capitalizeFirstLetter } from '../../utils';
import { AuthUser } from '../../types/auth';

import introJs from 'intro.js';
import { TooltipPosition } from 'intro.js/src/packages/tooltip';
import { useAppDispatch, useAppSelector } from '../../store/store';
import { componentNames, setFlag } from '../../store/reducer/tour-reducer';
import WebAnalyticsSkeleton from './components/web-insights-loader';
import { Box, Flex, Heading, Text, useColorMode } from '@chakra-ui/react';
import TooltipIcon from '../../components/info-icon-content/tooltip-message';
import { content } from '../../components/info-icon-content/info-content';
import Dropdown from '../../components/customDropdown/choose-dropdown';
import UserCard from './components/user-card';

import './pulse.scss';
import {
   useWebInsights,
   handleTrack,
   fetchTrackedInsights,
} from '../../hooks/use-web-insights';
import IntegrateInfo from '../../components/integrate-info/integrate-info';
import { integrationInfoStrings } from '../../utils/strings/integrate-info-strings';

interface WebInsight {
   title: string;
   components: JSX.Element[];
}

export const dropdownOptions = [
   { value: '1', label: '1 Day' },
   { value: '3', label: '3 Days' },
   { value: '7', label: '7 Days' },
   { value: '15', label: '15 Days' },
   { value: '30', label: '30 Days' },
   { value: '60', label: '60 Days' },
   { value: '90', label: '90 Days' },
];

const groupByCategory = (
   data: DynamicInsightRankData[],
): Record<string, DynamicInsightRankData[]> =>
   data.reduce(
      (acc, curr) => {
         acc[curr.business_category] = acc[curr.business_category] || [];
         acc[curr.business_category].push(curr);
         return acc;
      },
      {} as Record<string, DynamicInsightRankData[]>,
   );

const createComponents = (
   data: DynamicInsightRankData[],
   trackedInsights: string[],
   handleTrackedInsight: (insights: string[]) => void,
   backgroundColor?: string,
): JSX.Element[] => {
   const { colorMode } = useColorMode();

   const handleTrackWrapper = (insight_text: string, load_date: string) => {
      void handleTrack({ insight_text, load_date });
      handleTrackedInsight([insight_text]);
   };

   return data.map((info, index) => {
      const { insight_text, insight_value, insight_type, load_date } = info;
      const heading = insight_value === 'Null' ? '' : insight_value || '';
      const subHeading = capitalizeFirstLetter(insight_text);
      const iconSrc = getInsightIcon(insight_type);
      const imageSrc = getPlatformImage(subHeading);

      // Light और Dark mode के लिए background colors
      const bgColor =
         colorMode === 'light' ? backgroundColor : `${backgroundColor}24`; // Dark mode में 24 opacity के साथ

      return (
         <UserCard
            key={insight_text + index}
            heading={heading}
            subHeading={subHeading}
            backgroundColor={bgColor}
            src={imageSrc}
            icon={iconSrc}
            isTracking={trackedInsights.includes(insight_text)}
            onTrack={() => handleTrackWrapper(insight_text, load_date)}
         />
      );
   });
};

const mapDataToWebInsights = (
   backendData: DynamicInsightRankData[],
   trackedInsights: string[],
   handleTrackedInsight: (insights: string[]) => void,
): WebInsight[] => {
   const groupedData = groupByCategory(backendData);
   const colors = ['#DEF8FC', '#EAF6EA', '#FFFFB7', '#FFEBEE'];

   if (backendData.length === 0) return [];

   return Object.keys(groupedData).map((category, index) => {
      const backgroundColor = colors[index % colors.length];
      return {
         title: category,
         components: createComponents(
            groupedData[category],
            trackedInsights,
            handleTrackedInsight,
            backgroundColor,
         ),
      };
   });
};

const Pulse: React.FC = () => {
   const { colorMode } = useColorMode();
   const userDetails = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   );
   const [selectedDays, setSelectedDays] = useState<string>('7');
   const [trackedInsights, setTrackedInsights] = useState<string[]>([]);

   const { webInsight } = useAppSelector((state) => state.tour);
   const { optimisationsStatus } = useAppSelector((state) => state.onboarding);

   const dispatch = useAppDispatch();

   const handleTrackedInsight = useCallback((insights: string[]) => {
      setTrackedInsights((prev) => [...prev, ...insights]);
   }, []);

   const { insights, isLoading } = useWebInsights(
      userDetails?.client_id,
      selectedDays,
   );
   const intro = introJs.tour();

   const startTour = () => {
      // Filter out the steps where data is not available
      const availableSteps = steps.filter((step) => step.available);

      // If any steps have available data, start the tour
      if (availableSteps.length > 0) {
         intro.setOptions({ steps: availableSteps });
         void intro.start();

         dispatch(
            setFlag({ componentName: componentNames.WEB_INSIGHT, flag: false }),
         );
      } else {
         console.log('No data available to start the tour.');
      }
   };

   useEffect(() => {
      // Only start the tour if webInsight data is available
      if (webInsight) {
         startTour();
      }
   }, [webInsight]);
   const handleDaysSelect = useCallback((value: string) => {
      setSelectedDays(value);
   }, []);

   useEffect(() => {
      const fetchTracked = async () => {
         try {
            const insights = await fetchTrackedInsights();
            if (insights) {
               handleTrackedInsight(
                  insights.map((insight) => insight.insight_text),
               );
            }
         } catch (error) {
            console.error('Error fetching tracked insights', error);
         }
      };
      void fetchTracked();
   }, [handleTrackedInsight]);

   const webInsights = useMemo(
      () =>
         mapDataToWebInsights(insights, trackedInsights, handleTrackedInsight),
      [insights, trackedInsights],
   );

   const steps: {
      element: string;
      intro: string;
      position: TooltipPosition;
      available?: boolean;
   }[] = [
      {
         element: '#webInsightDropdown',
         intro: 'Select the time frame that matters most to your analysis.',
         position: 'top',
         available: true,
      },
      {
         element: '#webInsightSection-user-traffic',
         intro: 'Check out User Traffic to get a detailed overview of your site’s visitor activity.',
         position: 'top',
         available: webInsights?.length > 0, // Check for user traffic data
      },
      {
         element: '#webInsightSection-channel-engagement',
         intro: 'Use Channel Engagement to see how your traffic is distributed across different channels.',
         position: 'top',
         available: webInsights?.length > 0, // Check for channel engagement data
      },
      {
         element: '#webInsightSection-event-correlation',
         intro: 'Explore Event Correlation to analyze how different events align with user engagement patterns.',
         position: 'top',
         available: webInsights?.length > 0, // Check for event correlation data
      },
      {
         element: '#webInsightSection-regional-traffic',
         intro: 'With regional traffic, get a clear view of visitor regions and engagement.',
         position: 'top',
         available: webInsights?.length > 0, // Check for regional traffic data
      },
      {
         element: '#webInsightSection-device--platform',
         intro: 'With device & platform gain insights into how your audience interacts with your content across different devices and platforms.',
         position: 'top',
         available: webInsights?.length > 0, // Check for device & platform data
      },
      {
         element: '#webInsightSection-visitor',
         intro: 'Track visitor actions, from the pages they visit to the paths they take, and get insights into their behavior.',
         position: 'top',
         available: webInsights?.length > 0, // Check for visitor data
      },
   ];
   if (!optimisationsStatus.complete && !optimisationsStatus.flable_pixel) {
      return (
         <IntegrateInfo
            feature={integrationInfoStrings.webInsights.title}
            text={integrationInfoStrings.webInsights.description}
         />
      );
   }

   return (
      <div className='Pulse'>
         <div className='align'>
            <Box>
               <Flex align='center'>
                  <Heading
                     as='h4'
                     size='bold'
                     className='Main'
                     fontWeight='600'
                     color={
                        colorMode === 'dark'
                           ? 'white !important'
                           : 'gray.800 !important'
                     }
                  >
                     Web Analytics Insights
                  </Heading>
                  <TooltipIcon
                     label={content.web_insight}
                     placement='top'
                     iconColor='blue.500'
                     ml={2}
                     mt={7}
                  />
               </Flex>
            </Box>
            <div className='web-dropdown'>
               <Dropdown
                  id='webInsightDropdown'
                  options={dropdownOptions}
                  onSelect={handleDaysSelect}
                  initialValue={selectedDays}
               />
            </div>
         </div>
         {/* <OverviewAndTrackedTabs type='web' /> */}
         {isLoading ? (
            <Box w={'85%'}>
               <WebAnalyticsSkeleton />
            </Box>
         ) : webInsights.length == 0 ? (
            <Flex justify='center' align='center' h='50vh'>
               <Text fontSize='xl'>
                  No data available for last {selectedDays} days
               </Text>
            </Flex>
         ) : (
            <div className='widget-container'>
               {webInsights.map((webInsight, index) => (
                  <Webinsights
                     id={`webInsightSection-${webInsight.title
                        .toLowerCase()
                        .replace(/\s+/g, '-')
                        .replace(/[^\w-]/g, '')}`}
                     key={index}
                     components={webInsight.components}
                     title={webInsight.title}
                  />
               ))}
            </div>
         )}
      </div>
   );
};

export default Pulse;
