import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAppSelector } from '../../../store/store';
import './performance-card.scss';
import Popup from './pop-up';
import { button } from '../../../utils/strings/pulse-strings';
import { UserDetails } from './interface';
import LineChart from './linechart';
import pulseService from '../../../api/service/pulse';
import { formatValue, toShowCurrency, truncateText } from '../utils/helper';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import {
   useToast,
   Image,
   Flex,
   Tooltip,
   useColorMode,
   Text,
} from '@chakra-ui/react';
import trackedpin from '../../../assets/icons/tracked-pin.svg';
import { pulseMetaKeys } from '../../dashboard/utils/query-keys';
import { useApiMutation } from '../../../hooks/react-query-hooks';
import {
   calculateHelper,
   specialMetrics,
} from '../../utils/kpiCalculaterHelper';
import { DaywiseCampaignKPIsCalculated } from '../../../api/service/pulse/performance-insights/meta-ads';
import { useQueryClient } from '@tanstack/react-query';

interface CardProps {
   campaign: DaywiseCampaignKPIsCalculated;
   tracked: boolean;
   overview?: string;
   performanceTrackBtn?: string;
   performanceTrackBtnId?: string;
}

const PerformanceCard: React.FC<CardProps> = ({
   campaign,
   performanceTrackBtnId,
   overview,
   tracked,
}) => {
   const toast = useToast();
   const queryClient = useQueryClient();
   const colorMode = useColorMode().colorMode;

   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);

   const { channel, objective, metric } = useAppSelector(
      (state) => state.dropdown,
   );
   const [isPopupOpen, setIsPopupOpen] = useState(false);

   const handlePopupOpen = () => setIsPopupOpen(true);
   const handlePopupClose = () => setIsPopupOpen(false);

   const handleTrackWrapper = () => {
      void trackedKpiService.mutate({
         client_id: userDetails.client_id,
         kpi_name: metric,
         objective: objective,
         campaign_id: campaign.campaign_id,
         tracked: true,
         channel: channel,
      });
   };

   const handleError = (msg: string | null) =>
      toast({
         title: 'Error',
         description: msg,
         status: 'error',
         duration: 5000,
         isClosable: true,
      });

   const trackedKpiService = useApiMutation({
      mutationFn: pulseService.updateTrackedKpis,
      onSuccessHandler: async () => {
         await queryClient.invalidateQueries({
            queryKey: [pulseMetaKeys.trackedCampaigns],
         });
         toast({
            title: 'Tracked successfully',
            status: 'success',
            duration: 3000,
            isClosable: true,
         });
      },
      onError(msg) {
         handleError(msg);
      },
      invalidateCacheQuery: [pulseMetaKeys.metaCampaigns],
   });

   const getSelectedKPI = () => {
      return (
         campaign.kpis.find((kpi) => kpi.kpi_name === metric) ||
         campaign.kpis[0]
      );
   };

   const filteredChartData = campaign?.kpis.filter(
      (kpi) => !isNaN(Number(kpi.kpi_value)) && kpi.kpi_name === metric,
   );

   const kpi_recommendationData = campaign?.daywise_kpis?.[metric]
      ? Object.entries(campaign.daywise_kpis[metric])
           .filter(([, value]) => value !== null)
           .sort(([, valueA], [, valueB]) => valueB - valueA)
      : [];

   const selectedKPI = getSelectedKPI();
   const selectedKPIValue = campaign?.current_kpi_val?.[metric];
   const prevPeriodKPIValue = campaign?.prev_kpi_val?.[metric];
   const { percentage, color, direction, currentValue } = calculateHelper(
      metric,
      selectedKPIValue,
      prevPeriodKPIValue,
   );
   const arrow = direction === 'is up' ? '↑' : '↓';

   return (
      <div className='CardWrapper'>
         <div className={`usercards ${colorMode}`}>
            <div className={`usercard-containers ${colorMode}`}>
               <div className='card-top'>
                  <Tooltip
                     label={
                        campaign.campaign_name.length > 40
                           ? campaign.campaign_name
                           : ''
                     }
                     placement='top'
                     fontSize='small'
                  >
                     <button className='campaign-name'>
                        <span>
                           {truncateText(campaign.campaign_name, false, 40)}
                        </span>
                     </button>
                  </Tooltip>
                  <button className='campaign-status'>
                     <p
                        className={
                           campaign.recent_campaign_status === 'ACTIVE'
                              ? 'campaign-status-active'
                              : 'campaign-status-pause'
                        }
                     >
                        {campaign.recent_campaign_status}
                     </p>
                  </button>
               </div>
               <div className='chart-elements'>
                  <div className='elements'>
                     <h6 style={{ color }}>
                        <span>{percentage && `${percentage}% ${arrow}`}</span>
                     </h6>
                     <p>
                        <span>
                           {selectedKPI.kpi_name.length > 15 ? (
                              <Tooltip
                                 label={selectedKPI.kpi_name.toUpperCase()}
                                 hasArrow
                                 placement='bottom'
                              >{`${selectedKPI.kpi_name.toUpperCase().slice(0, 15)}...`}</Tooltip>
                           ) : (
                              selectedKPI.kpi_name.toUpperCase()
                           )}{' '}
                           {percentage && direction}
                        </span>
                     </p>
                     <h4>
                        <span>
                           {currentValue !== 'N/A' &&
                              toShowCurrency(metric, campaign?.recent_currency)}
                           {currentValue}
                        </span>
                     </h4>
                  </div>
                  <div className='chart' style={{ color: 'black' }}>
                     {campaign &&
                        filteredChartData &&
                        currentValue !== 'N/A' && (
                           <LineChart
                              kpiDetails={{
                                 displayName: campaign.campaign_name,
                                 allData: filteredChartData.map((kpi) => ({
                                    date: kpi.kpi_date,
                                    kpi_value: formatValue(kpi.kpi_value),
                                 })),
                                 stat: campaign.kpis[0].campaign_status,
                              }}
                           />
                        )}
                  </div>
               </div>
               <div className='kpi-recommendation'>
                  {kpi_recommendationData.length > 1 &&
                     kpi_recommendationData?.[0]?.[1] > 0 && (
                        <>
                           <button className='campaign-recommendation'>
                              <p
                                 className={` ${specialMetrics.includes(metric) ? 'campaign-recommendation-bad-day' : 'campaign-recommendation-good-day'}`}
                              >
                                 {`Highest on ${
                                    kpi_recommendationData?.[0]?.[1] &&
                                    kpi_recommendationData?.[0]?.[0]
                                 }s : ${Math.floor(
                                    kpi_recommendationData?.[0]?.[1] &&
                                       kpi_recommendationData?.[0]?.[1],
                                 )}`}
                              </p>
                           </button>
                           <button className='campaign-recommendation'>
                              <p
                                 className={` ${specialMetrics.includes(metric) ? 'campaign-recommendation-good-day' : 'campaign-recommendation-bad-day'}`}
                              >
                                 {`Lowest on ${
                                    kpi_recommendationData?.[
                                       kpi_recommendationData.length - 1
                                    ]?.[0]
                                 }s : ${Math.ceil(
                                    kpi_recommendationData?.[
                                       kpi_recommendationData.length - 1
                                    ]?.[1],
                                 )}`}
                              </p>
                           </button>
                        </>
                     )}
               </div>
               <div className='bottom'>
                  <hr className={`divider ${colorMode}`} />
                  <div className='bottom-buttons'>
                     {currentValue === 'N/A' || Number(currentValue) === 0 ? (
                        <Tooltip
                           hasArrow
                           label='No campaign available for this campaign'
                        >
                           <Text cursor='not-allowed'>View Details</Text>
                        </Tooltip>
                     ) : (
                        <Link to='#' onClick={handlePopupOpen} id={overview}>
                           View Details
                        </Link>
                     )}

                     <Flex gap={2}>
                        {!tracked && (
                           <Image
                              src={trackedpin}
                              style={{
                                 filter:
                                    colorMode === 'dark' ? 'invert(1)' : 'none',
                              }}
                           />
                        )}
                        <button
                           id={performanceTrackBtnId}
                           className={`track-button ${tracked ? 'tracking' : ''}`}
                           disabled={tracked}
                           style={{
                              cursor: tracked ? 'not-allowed' : 'pointer',
                           }}
                           onClick={handleTrackWrapper}
                        >
                           {tracked ? button.tracking : button.track}
                        </button>
                     </Flex>
                  </div>
               </div>
            </div>
            {isPopupOpen && (
               <Popup
                  campaign={campaign}
                  isOpen={isPopupOpen}
                  onClose={handlePopupClose}
                  details='Detailed information about'
               />
            )}
         </div>
      </div>
   );
};

export default PerformanceCard;
