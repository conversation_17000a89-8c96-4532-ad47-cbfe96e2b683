import {
   CostSettingOptions,
   SettingsMode,
} from '../../pages/settings/interface';

export const settingsIndex = {
   title: 'Settings',
   language: 'Language and Time Zone',
   languageDesc: 'Region Information',
   timezone: ' Competitors',
   timezoneDesc: 'Competitor Social Handles',
};
export const settingsModes: SettingsMode[] = [
   {
      title: 'General Settings',
      key: 'languages',
      description: '',
   },
   {
      title: ' Competitors',
      key: 'competitors',
      description: 'Competitor Social Handles',
   },
   {
      title: 'Reports',
      key: 'reports',
      description: 'Email Reports',
   },
   {
      title: 'Cost Setting',
      key: 'costsettings',
      description: '',
   },
   {
      title: 'Manage API Token',
      key: 'apitoken',
      description: 'Meta & Google Ads Manager Tokens',
   },
   {
      title: 'Attribution Settings',
      key: 'attributions',
      description: '',
   },
   {
      title: 'Plans & Topups',
      key: 'plansTopups',
      description: '',
   },
   {
      title: 'Usages & Billing',
      key: 'usagesBilling',
      description: '',
   },
];
export const costSettingOptions: CostSettingOptions[] = [
   {
      title: 'COGS',
      key: 'cogs',
      description: 'Region Information',
   },
   {
      title: 'Shipping',
      key: 'shipping',
      description: 'Competitor Social Handles',
   },
   {
      title: 'Payment Gateway',
      key: 'paymentgate',
      description: 'Email Reports',
   },
   {
      title: 'Custom expenses ',
      key: 'customexpenses',
      description: '',
   },
];
export const settingsCompetitors = {
   title: 'Competitors',
   addNew: 'Add new competitor',
   inputTitle: 'Social Handle',
   added: 'Added competitors',
   na: 'No competitors added',
};

export const settingsPreferences = {
   title: 'General Settings',
   language: 'Choose Language',
   timezone: 'Choose Time Zone',
   industry: 'Choose Industry',
   currency: 'Choose Currency',
   annualRevenue: 'Choose Annual Revenue',
};

export const VALID_EMAILS_PATTERN: RegExp =
   /^(([a-zA-Z0-9_.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(]?)\s*(;|,)?\s*)*$/;
