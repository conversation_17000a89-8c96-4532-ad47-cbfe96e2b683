import React from 'react';
import Chart from 'react-apexcharts';
import { PerformanceChartData } from '../../chatbox/interface';
import {
   getUserDate,
   isValidDateString,
} from '../../../pages/dashboard/utils/helpers';
import { useColorMode } from '@chakra-ui/react';
import { ApexOptions } from 'apexcharts';

const truncate = (str: string) => {
   return str.length > 20 ? str.substring(0, 20) + '...' : str;
};

interface PieChartProps {
   performanceData: PerformanceChartData;
   height?: string | number;
   theme: 'light' | 'dark';
}
const generateRandomColor = () => {
   const letters = '0123456789ABCDEF';
   let color = '#';
   for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
   }
   return color;
};

const generateUniqueColors = (numColors: number) => {
   const colors = new Set<string>();
   while (colors.size < numColors) {
      colors.add(generateRandomColor());
   }
   return Array.from(colors);
};
const PerformancePieChart: React.FC<PieChartProps> = ({
   performanceData,
   height,
   theme,
}) => {
   const { fields } = performanceData.schema;
   const validFields = fields.filter((f) => !f.name.includes('cumulative'));
   const xAxisField = validFields[1].name;
   const yAxisField = validFields[2].name;
   const dataLabelField = validFields[3]
      ? validFields[3].name
      : validFields[2].name;
   const colors = generateUniqueColors(performanceData.data.length);

   const series = performanceData.data.map((entry) =>
      Number(entry[yAxisField]),
   );
   const options: ApexOptions = {
      chart: {
         id: 'PerformanceChart',
         background: useColorMode().colorMode === 'dark' ? '#1b202d' : '',
         toolbar: {
            show: true,
         },
      },
      labels: performanceData.data.map((entry) => {
         const str = truncate(String(entry[xAxisField]));
         if (!isValidDateString(str)) return str;
         return getUserDate(new Date(str), false);
      }),
      colors: colors,
      title: {
         style: {
            color: useColorMode().colorMode === 'dark' ? '#fff' : '#000',
         },
         text: performanceData.text,
         align: 'center' as const,
      },
      legend: {
         labels: {
            colors: performanceData.data.map(() =>
               useColorMode().colorMode === 'dark' ? '#fff' : '#000',
            ),
         },
      },
      tooltip: {
         enabled: true,
         y: {
            formatter: (
               value: number,
               { dataPointIndex }: { dataPointIndex: number },
            ) => {
               const fullLabel = performanceData.data[dataPointIndex][
                  xAxisField
               ] as string;
               const dataLabel = performanceData.data[dataPointIndex][
                  dataLabelField
               ] as number;
               const yValue = value.toFixed(2);

               const additionalInfo =
                  dataLabelField != yAxisField
                     ? `<div><span style="font-size: 12px; color: #666;">${dataLabelField}: ${performanceData.currency} ${dataLabel.toFixed(2)}</span></div>`
                     : '';

               return `
            <div style="background-color: #f3f3f3; padding: 8px; border-radius: 5px;">
               <strong style="font-size: 14px; color: #333;">${fullLabel}</strong>
               <span style="font-size: 12px; color: #666;">${yAxisField}: ${yValue}</span>
              <span style="font-size: 12px; color: #666;"> ${additionalInfo}</span>
            </div>`;
            },
         },
      },
   };

   return (
      <Chart
         options={options}
         series={series}
         type='pie'
         height={height}
         theme={theme}
      />
   );
};

export default PerformancePieChart;
