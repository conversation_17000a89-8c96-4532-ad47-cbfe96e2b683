export const loginStrings = {
   loginFailed: 'Login failed',
   loginFailedDesc: 'An error occurred during login',
   error: 'error',
   success: 'success',
   loginSuccessful: 'Login successful',
   loginSuccessfulDesc: 'You have successfully logged in',
   forgotPassword: 'Forget Password?',
   dontHaveAccount: "Don't have an account?",
   register: 'Register',
   takeATour: 'Take a tour',
   login: 'Login',
};

export const registerStrings = {
   mailSent: 'OTP verification sent via email',
   registerSuccessful: 'Registration successful',
   registerFailed: 'Registration failed',
};

export const regex = {
   email_address: /\S+@\S+\.\S+/,
   password:
      /^(?=.*[0-9])(?=.*[!@#$%^&*(),.?":{}|<>_])[A-Za-z0-9!@#$%^&*(),.?":{}|<>]{8,}$/,
};
