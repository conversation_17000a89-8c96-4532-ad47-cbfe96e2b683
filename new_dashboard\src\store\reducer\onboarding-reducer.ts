import { PayloadAction, createSlice } from '@reduxjs/toolkit';

type OrganizationType = 'Individual Business' | 'Marketing Agency';

export interface UserMasterList {
   type: string;
   value: string;
}

export interface OptimisationsStatus {
   complete: boolean;
   channels_marketplace: boolean;
   flable_pixel: boolean;
   ads_account: boolean;
   competitors: boolean;
   seo: boolean;
   socials: boolean;
}

export interface AccountDetails {
   [key: string]: string;
}

interface InitialState {
   registerProgress: string;
   organizationType: 'Individual Business' | 'Marketing Agency';
   accountDetails: AccountDetails[];
   masterList: UserMasterList[];
   snippet: string;
   optimisationsStatus: OptimisationsStatus;
}

const initialState: InitialState = {
   registerProgress: '',
   organizationType: 'Individual Business',
   accountDetails: [],
   masterList: [],
   snippet: '',
   optimisationsStatus: {
      complete: false,
      channels_marketplace: false,
      flable_pixel: false,
      ads_account: false,
      competitors: false,
      seo: false,
      socials: false,
   },
};

const onboardingSlice = createSlice({
   name: 'onboarding',
   initialState,
   reducers: {
      setRegisterProgress: (state, action: PayloadAction<string>) => {
         state.registerProgress = action.payload;
      },
      setOrganizationType: (state, action: PayloadAction<OrganizationType>) => {
         state.organizationType = action.payload;
      },
      setAccountDetails: (state, action: PayloadAction<AccountDetails[]>) => {
         state.accountDetails = action.payload;
      },
      setMasterList: (state, action: PayloadAction<UserMasterList[]>) => {
         state.masterList = action.payload;
      },
      setSnippet: (state, action: PayloadAction<string>) => {
         state.snippet = action.payload;
      },
      setOptimisationsStatus: (
         state,
         action: PayloadAction<OptimisationsStatus>,
      ) => {
         state.optimisationsStatus = action.payload;
      },
   },
});

export const {
   setRegisterProgress,
   setOrganizationType,
   setAccountDetails,
   setMasterList,
   setSnippet,
   setOptimisationsStatus,
} = onboardingSlice.actions;

export default onboardingSlice.reducer;
