import { useState } from 'react';
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from '@/components/ui/card';
import { PiCheckBold } from 'react-icons/pi';

import { Button } from '@/components/ui/button';
import { cn } from '@/utils';
import subsEndPoints from '@/api/service/subscription';
import { useApiMutation } from '@/hooks/react-query-hooks';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { UserDetails } from '@/pages/socialwatch/interface';
import { closeModal, openModal } from '@/store/reducer/modal-reducer';
import { modalTypes } from '@/components/modals/modal-types';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/store';
import {
   PricingCardProps,
   RazorpayInstance,
   RazorpayOptions,
   RazorpayResponse,
} from '@/pages/settings/subscription/types';
import { toast } from 'sonner';

export const CheckItem = ({
   text,
   popular,
}: {
   text: string;
   popular?: boolean;
}) => (
   <div className='flex items-center gap-2'>
      <PiCheckBold
         className={cn('text-lime', {
            'text-white': popular,
         })}
      />
      <span className='para6'>{text}</span>
   </div>
);

const PricingCard = ({
   isYearly,
   id,
   name,
   tier,
   plan_info,
   monthly_price,
   yearly_price,
   plan_desc,
   plan_feature,
   attribute,
   action_label,
   currency,
   razorpay_yearly_planid,
   razorpay_monthly_planid,
   extra_connectors,
   max_users,
}: PricingCardProps) => {
   const {
      isActive,
      planId,
      isYearly: isYearlyState,
   } = useSelector((state: RootState) => state.subscription);
   const currentPlan =
      isActive && planId && id === planId && isYearly === isYearlyState;
   const launchPlan = tier === 1;
   const popular = tier === 3;
   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);
   const dispatch = useDispatch();
   const [loading, setLoading] = useState(false);

   const razorpay_customerid = userDetails.razorpay_customerid as string;

   const purchasePayload = {
      rzrPay_PlanId: isYearly
         ? razorpay_yearly_planid
         : razorpay_monthly_planid,
      plan_name: name,
      client_id: userDetails.client_id,
      client_Phone: null,
      client_Email: userDetails.email,
      isYearly,
      currency,
      amount: isYearly ? yearly_price : monthly_price,
      attribute,
      extra_connectors,
      max_users,
      autoRenew: false,
   };

   const handleSuccess = (data: {
      success: boolean;
      message: string;
      subs_id?: string;
   }) => {
      setLoading(false);
      dispatch(closeModal());

      if (!window.Razorpay) {
         toast.error(
            'Razorpay SDK not loaded. Please refresh the page and try again.',
         );
         return;
      }

      toast.success(
         'Plan subscription initiated successfully! Redirecting to payment...',
      );

      const options = {
         key: import.meta.env.VITE_RAZRPAY_KEY_ID as string,
         subscription_id: data.subs_id,
         name: 'Flable Ai',
         description: 'Subscription Plan',
         handler: async function (response: RazorpayResponse) {
            try {
               const verify = await fetch(
                  `${import.meta.env.VITE_BE_API}/subs/razorpay/verify-signature`,
                  {
                     method: 'POST',
                     headers: { 'Content-Type': 'application/json' },
                     body: JSON.stringify({
                        ...response,
                        type: 'subscription',
                     }),
                  },
               );
               const result: { success: boolean } = (await verify.json()) as {
                  success: boolean;
               };
               if (result.success) {
                  toast.success(
                     'Subscription Successful! Your will be activated in few minutes. Welcome to Flable AI.',
                  );
                  dispatch(closeModal());
               } else {
                  toast.error(
                     'Payment verification failed. Please contact support.',
                  );
               }
            } catch (error) {
               console.error('Verification error: ', error);
               toast.error(
                  'Payment verification error. Please contact support.',
               );
            }
         },
         prefill: {
            email: userDetails.email,
            contact: null,
         },
      };

      const rzp: RazorpayInstance = new window.Razorpay(
         options as unknown as RazorpayOptions,
      );
      rzp.open();
   };

   const handleError = () => {
      setLoading(false);
      dispatch(closeModal());
      toast.error('Failed to initiate plan subscription. Please try again.');
   };

   const { mutate: subscribeToPlan } = useApiMutation({
      queryKey: ['subscribeToPlan'],
      mutationFn: subsEndPoints.subscribeToPlan,
      onSuccessHandler: (data) => handleSuccess(data),
      onError: handleError,
   });

   const handlePurchase = (plan_id: string) => {
      if (!razorpay_customerid) {
         dispatch(
            openModal({
               modalType: modalTypes.PAYMENT_MODAL,
               modalProps: { plan_id, ...purchasePayload },
            }),
         );
         return;
      }

      if (loading) return;

      setLoading(true);
      dispatch(
         openModal({
            modalType: modalTypes.LOADER_MODAL,
         }),
      );

      subscribeToPlan({
         plan_id,
         rzrPay_ClientId: razorpay_customerid,
         ...purchasePayload,
      });
   };

   return (
      <Card
         className={cn(
            'w-[40%] text-charcoal justify-start',
            { 'bg-midnight': popular },
            { 'border-2 !border-cerulean': currentPlan },
            { 'text-ash': popular },
         )}
      >
         <CardHeader>
            <div className='flex justify-between items-center'>
               <CardTitle className={cn('text-jet', { 'text-white': popular })}>
                  {name}
               </CardTitle>
               {(currentPlan || popular) && (
                  <div
                     className={cn(
                        'bg-skyLight px-3 py-1 text-azure para6 font-medium rounded-xl whitespace-nowrap',
                        {
                           'bg-indigo text-white': popular,
                        },
                     )}
                  >
                     {currentPlan
                        ? 'Your Current Plan'
                        : popular
                          ? 'Most Popular'
                          : ''}
                  </div>
               )}
            </div>
            <CardDescription className='pt-3 '>{plan_info}</CardDescription>
         </CardHeader>

         <div className='flex gap-0.5'>
            <h3
               className={cn('head3 font-bold text-jet', {
                  'text-white': popular,
               })}
            >
               {yearly_price && isYearly
                  ? '$' + yearly_price
                  : monthly_price
                    ? '$' + monthly_price
                    : 'Custom'}
            </h3>
            <span className='flex flex-col justify-end text-sm mb-1'>
               {yearly_price && isYearly
                  ? '/ year'
                  : monthly_price
                    ? '/ month'
                    : null}
            </span>
         </div>
         <div className='min-h-[60px] w-full flex items-center justify-start'>
            <CardDescription className='pt-1.5'>{plan_desc}</CardDescription>
         </div>

         <Button
            size='buy'
            className={cn(
               { 'bg-white text-charcoal': popular },
               { 'opacity-50 cursor-not-allowed': currentPlan || launchPlan },
            )}
            onClick={() => handlePurchase(id)}
            disabled={loading || currentPlan || launchPlan}
         >
            {loading ? 'Processing...' : action_label}
         </Button>

         <CardContent
            className={cn('flex flex-col gap-3 text-charcoal', {
               'text-white': popular,
            })}
         >
            <p
               className={cn('text-jet font-medium para6', {
                  'text-white': popular,
               })}
            >
               Includes:
            </p>
            {plan_feature.map((feature: string) => (
               <CheckItem key={feature} text={feature} popular={popular} />
            ))}
         </CardContent>
      </Card>
   );
};

export default PricingCard;
