import { PayloadAction, createSlice } from '@reduxjs/toolkit';

interface InitialState {
   currentSessionID: string;
   currentAlertID: string;
   currentChatID: string;
   currentPage: number;
}

const initialState: InitialState = {
   currentSessionID: '',
   currentAlertID: '',
   currentChatID: '',
   currentPage: 1,
};

const alertingAgentSlice = createSlice({
   name: 'alertingAgent',
   initialState,
   reducers: {
      setCurrentSessionID: (state, action: PayloadAction<string>) => {
         state.currentSessionID = action.payload;
      },
      setCurrentAlertID: (state, action: PayloadAction<string>) => {
         state.currentAlertID = action.payload;
      },
      setCurrentChatID: (state, action: PayloadAction<string>) => {
         state.currentChatID = action.payload;
      },
      setCurrentPage: (state, action: PayloadAction<number>) => {
         state.currentPage = action.payload;
      },
   },
});

export const {
   setCurrentSessionID,
   setCurrentAlertID,
   setCurrentChatID,
   setCurrentPage,
} = alertingAgentSlice.actions;

export default alertingAgentSlice.reducer;
