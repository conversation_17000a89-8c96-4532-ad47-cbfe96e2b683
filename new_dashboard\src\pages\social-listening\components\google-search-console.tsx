import image from '../images/integrations/searchConsole.jpeg';
import Card from './Card';
import endPoints from '../apis/agent';
import Swal from 'sweetalert2';

import { useEffect, useState } from 'react';
import { useApiMutation, useApiQuery } from '../../../hooks/react-query-hooks';
import { channelNames } from '../utils/constant';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { AuthUser } from '../../../types/auth';
import { connectToGSCSentiment } from '../utils';
import { dialogMessage } from '../../../utils/strings/content-manager';
import { ApiError } from './facebook-ads-form';
import { useToast } from '@chakra-ui/react';

const GoogleSearchConsole = () => {
   const [isDisconnecting, setIsDisconnecting] = useState(false);
   const toast = useToast();
   const authUser = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   );

   const showToast = (
      title: string,
      description: string,
      status: 'success' | 'error',
   ) => {
      toast({ title, description, status, duration: 2000, isClosable: true });
   };
   // Fetch connection details
   const { data, isLoading, errorMessage } = useApiQuery({
      queryKey: [`gscConnectionDetails`],
      queryFn: () =>
         endPoints.checkConnectionDetails({
            client_id: authUser?.client_id || '',
            channel_name: channelNames.GSC,
         }),
   });

   const { mutate: connectToSentiment, isPending: isConnectingToSentiment } =
      useApiMutation({
         mutationFn: connectToGSCSentiment,
         onSuccessHandler: () => {
            setTimeout(() => {
               window.location.href = `${window.location.origin}/integrations`;
            }, 1000);
         },
      });

   const { is_active = false } = data?.details || {};

   async function onConnect() {
      const {
         data: { url },
      } = await endPoints.getAuthUrl();
      window.location.href = url;
   }

   const onDisconnect = async () => {
      const result = await Swal.fire({
         title: dialogMessage.delete.title,
         text: dialogMessage.delete.description,
         icon: 'warning',
         showCancelButton: true,
         confirmButtonColor: '#3085d6',
         cancelButtonColor: '#d33',
         confirmButtonText: dialogMessage.delete.buttonMessage,
      });
      if (result.isConfirmed && authUser?.client_id) {
         try {
            setIsDisconnecting(true);
            connectToSentiment({
               client_id: authUser?.client_id,
               isConnect: false,
            });
         } catch (err) {
            const error = err as ApiError;
            const msg = error.response.data.message;
            showToast('Could not disconnect', msg!, 'error');
         } finally {
            setIsDisconnecting(false);
         }
      }
   };

   function onClick() {
      is_active ? void onDisconnect() : void onConnect();
   }

   useEffect(() => {
      const searchParams = new URLSearchParams(window.location.search);
      const gsc = searchParams.get('gsc');
      const accessToken = searchParams.get('t');
      const refreshToken = searchParams.get('r');

      if (gsc && accessToken && refreshToken && authUser?.client_id) {
         connectToSentiment({
            client_id: authUser.client_id,
            access_token: accessToken,
            refresh_token: refreshToken,
         });
      }
   }, []);

   return (
      <Card
         error={errorMessage}
         isConnected={is_active}
         isDisconnecting={isDisconnecting}
         isConnecting={isConnectingToSentiment}
         isFetching={isLoading}
         onButtonClick={onClick}
         heading='Google Search Console'
         src={image}
      />
   );
};

export default GoogleSearchConsole;
