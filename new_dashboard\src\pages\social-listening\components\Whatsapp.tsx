/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import Card from './Card';
import image from '../images/integrations/WhatsApp.jpg';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { AuthUser } from '../../../types/auth';
import endPoints from '../apis/agent';

interface WhatsappConnectionDetails {
   is_active?: boolean;
}

const WhatsApp = () => {
   const navigate = useNavigate();
   const client_id = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;
   const [connectionDetails, setConnectionDetails] =
      useState<WhatsappConnectionDetails>({});
   const [isFetching, setIsFetching] = useState<boolean>(false);

   function handleNavigation() {
      navigate('/integrations/whatsapp');
   }

   useEffect(() => {
      if (!client_id) return;
      const fetchData = async () => {
         try {
            setIsFetching(true);
            const data = await endPoints.checkWhatsappConnectionDetails({
               client_id,
            });
            setConnectionDetails(data);
         } catch (error) {
            console.error('Error fetching WhatsApp connection details:', error);
         } finally {
            setIsFetching(false);
         }
      };
      void fetchData();
   }, [client_id]);

   async function handleDisconnect() {
      if (!client_id) return;
      try {
         const isConfirmed = confirm(
            'Are you sure you want to disconnect WhatsApp?',
         );
         if (!isConfirmed) return;
         await endPoints.disconnectWhatsapp({ client_id });
         setConnectionDetails({});
      } catch (err) {
         console.log(err);
      }
   }

   return (
      <Card
         isConnected={connectionDetails.is_active}
         isFetching={isFetching}
         heading={'WhatsApp'}
         src={image}
         onButtonClick={
            connectionDetails.is_active ? handleDisconnect : handleNavigation
         }
      />
   );
};

export default WhatsApp;
